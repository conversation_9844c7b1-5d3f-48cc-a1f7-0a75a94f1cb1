# 部署指南

## 🚀 概述

本指南详细说明了管理系统在不同环境下的部署方法，包括开发环境、测试环境和生产环境的配置。

## 📋 系统要求

### 最低配置要求
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+
- **Node.js**: >= 18.0.0
- **npm**: >= 8.0.0
- **内存**: >= 4GB RAM
- **存储**: >= 10GB 可用空间
- **网络**: 稳定的网络连接

### 推荐配置
- **操作系统**: Windows 11, macOS 12+, Ubuntu 20.04+
- **Node.js**: >= 20.0.0
- **npm**: >= 10.0.0
- **内存**: >= 8GB RAM
- **存储**: >= 50GB 可用空间 (SSD推荐)
- **网络**: 千兆网络连接

## 🛠️ 开发环境部署

### 1. 环境准备

```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 如果版本不符合要求，请更新Node.js
```

### 2. 项目克隆

```bash
# 克隆项目
git clone https://github.com/Darrowyu/management-system.git
cd management-system

# 或者下载ZIP包并解压
```

### 3. 依赖安装

```bash
# 安装项目依赖
npm install

# 如果遇到网络问题，可以使用国内镜像
npm install --registry=https://registry.npmmirror.com
```

### 4. 配置文件设置

```bash
# 复制配置文件模板（如果存在）
cp .env.example .env

# 编辑配置文件
# Windows: notepad .env
# macOS/Linux: nano .env
```

### 5. 数据库初始化

```bash
# 系统会自动创建SQLite数据库
# 首次运行时会自动初始化数据表
```

### 6. 启动开发服务器

```bash
# 启动服务器
npm start

# 或者使用nodemon进行开发（如果已安装）
npx nodemon backend/server.js
```

### 7. 访问系统

打开浏览器访问：`http://localhost:3000`

## 🏭 生产环境部署

### 方式一：直接部署

#### 1. 服务器准备

```bash
# 更新系统包
sudo apt update && sudo apt upgrade -y  # Ubuntu/Debian
# 或
sudo yum update -y  # CentOS/RHEL

# 安装Node.js (使用NodeSource仓库)
curl -fsSL https://deb.nodesource.com/setup_20.x | sudo -E bash -
sudo apt-get install -y nodejs

# 安装PM2进程管理器
sudo npm install -g pm2
```

#### 2. 项目部署

```bash
# 创建应用目录
sudo mkdir -p /opt/management-system
cd /opt/management-system

# 克隆项目
sudo git clone https://github.com/Darrowyu/management-system.git .

# 安装依赖
sudo npm install --production

# 设置文件权限
sudo chown -R www-data:www-data /opt/management-system
sudo chmod -R 755 /opt/management-system
```

#### 3. 配置生产环境

```bash
# 创建生产环境配置
sudo nano /opt/management-system/.env
```

配置内容示例：
```env
NODE_ENV=production
PORT=3000
DB_PATH=/opt/management-system/backend/data/application_system.db
UPLOAD_PATH=/opt/management-system/uploads
LOG_LEVEL=info
```

#### 4. 使用PM2启动

```bash
# 创建PM2配置文件
sudo nano /opt/management-system/ecosystem.config.js
```

PM2配置内容：
```javascript
module.exports = {
  apps: [{
    name: 'management-system',
    script: 'backend/server.js',
    cwd: '/opt/management-system',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'production',
      PORT: 3000
    },
    error_file: '/var/log/management-system/error.log',
    out_file: '/var/log/management-system/out.log',
    log_file: '/var/log/management-system/combined.log',
    time: true,
    max_memory_restart: '1G',
    node_args: '--max-old-space-size=1024'
  }]
};
```

```bash
# 创建日志目录
sudo mkdir -p /var/log/management-system
sudo chown www-data:www-data /var/log/management-system

# 启动应用
sudo pm2 start ecosystem.config.js

# 设置开机自启
sudo pm2 startup
sudo pm2 save
```

### 方式二：Docker部署

#### 1. 创建Dockerfile

```dockerfile
FROM node:20-alpine

# 设置工作目录
WORKDIR /app

# 复制package文件
COPY package*.json ./

# 安装依赖
RUN npm ci --only=production

# 复制应用代码
COPY . .

# 创建必要目录
RUN mkdir -p uploads logs backend/data

# 设置权限
RUN chown -R node:node /app
USER node

# 暴露端口
EXPOSE 3000

# 健康检查
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
  CMD curl -f http://localhost:3000/api/health || exit 1

# 启动命令
CMD ["node", "backend/server.js"]
```

#### 2. 创建docker-compose.yml

```yaml
version: '3.8'

services:
  management-system:
    build: .
    container_name: management-system
    restart: unless-stopped
    ports:
      - "3000:3000"
    volumes:
      - ./backend/data:/app/backend/data
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    environment:
      - NODE_ENV=production
      - PORT=3000
    networks:
      - management-network

  nginx:
    image: nginx:alpine
    container_name: management-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf
      - ./ssl:/etc/nginx/ssl
    depends_on:
      - management-system
    networks:
      - management-network

networks:
  management-network:
    driver: bridge
```

#### 3. 部署命令

```bash
# 构建并启动
docker-compose up -d

# 查看日志
docker-compose logs -f

# 停止服务
docker-compose down
```

## 🌐 Nginx反向代理配置

### 基础配置

```nginx
server {
    listen 80;
    server_name your-domain.com;
    
    # 重定向到HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;
    
    # SSL配置
    ssl_certificate /etc/nginx/ssl/cert.pem;
    ssl_certificate_key /etc/nginx/ssl/key.pem;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512;
    
    # 安全头
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    
    # 静态文件缓存
    location ~* \.(js|css|png|jpg|jpeg|gif|ico|svg)$ {
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
    
    # 文件上传大小限制
    client_max_body_size 20M;
    
    # 代理到Node.js应用
    location / {
        proxy_pass http://localhost:3000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 300s;
        proxy_connect_timeout 75s;
    }
}
```

## 🔒 安全配置

### 1. 防火墙设置

```bash
# Ubuntu/Debian
sudo ufw allow 22/tcp
sudo ufw allow 80/tcp
sudo ufw allow 443/tcp
sudo ufw enable

# CentOS/RHEL
sudo firewall-cmd --permanent --add-service=ssh
sudo firewall-cmd --permanent --add-service=http
sudo firewall-cmd --permanent --add-service=https
sudo firewall-cmd --reload
```

### 2. SSL证书配置

```bash
# 使用Let's Encrypt免费证书
sudo apt install certbot python3-certbot-nginx
sudo certbot --nginx -d your-domain.com

# 设置自动续期
sudo crontab -e
# 添加：0 12 * * * /usr/bin/certbot renew --quiet
```

### 3. 系统安全加固

```bash
# 禁用root SSH登录
sudo nano /etc/ssh/sshd_config
# 设置：PermitRootLogin no

# 创建专用用户
sudo adduser management
sudo usermod -aG sudo management

# 重启SSH服务
sudo systemctl restart sshd
```

## 📊 监控和日志

### 1. 系统监控

```bash
# 安装监控工具
sudo npm install -g pm2-logrotate
pm2 install pm2-server-monit

# 配置日志轮转
pm2 set pm2-logrotate:max_size 10M
pm2 set pm2-logrotate:retain 30
```

### 2. 日志管理

```bash
# 查看应用日志
pm2 logs management-system

# 查看系统日志
sudo journalctl -u management-system -f

# 清理旧日志
sudo find /var/log -name "*.log" -type f -mtime +30 -delete
```

## 🔄 备份策略

### 1. 数据库备份

```bash
# 创建备份脚本
sudo nano /opt/scripts/backup.sh
```

备份脚本内容：
```bash
#!/bin/bash
BACKUP_DIR="/opt/backups"
DATE=$(date +%Y%m%d_%H%M%S)
DB_PATH="/opt/management-system/backend/data/application_system.db"

# 创建备份目录
mkdir -p $BACKUP_DIR

# 备份数据库
cp $DB_PATH $BACKUP_DIR/db_backup_$DATE.db

# 备份上传文件
tar -czf $BACKUP_DIR/uploads_backup_$DATE.tar.gz /opt/management-system/uploads

# 清理30天前的备份
find $BACKUP_DIR -name "*.db" -mtime +30 -delete
find $BACKUP_DIR -name "*.tar.gz" -mtime +30 -delete
```

```bash
# 设置执行权限
sudo chmod +x /opt/scripts/backup.sh

# 设置定时备份
sudo crontab -e
# 添加：0 2 * * * /opt/scripts/backup.sh
```

## 🚨 故障排除

### 常见问题

1. **端口被占用**
   ```bash
   # 查看端口占用
   sudo netstat -tlnp | grep :3000
   # 或
   sudo lsof -i :3000
   ```

2. **权限问题**
   ```bash
   # 修复文件权限
   sudo chown -R www-data:www-data /opt/management-system
   sudo chmod -R 755 /opt/management-system
   ```

3. **内存不足**
   ```bash
   # 增加swap空间
   sudo fallocate -l 2G /swapfile
   sudo chmod 600 /swapfile
   sudo mkswap /swapfile
   sudo swapon /swapfile
   ```

4. **数据库锁定**
   ```bash
   # 检查数据库进程
   sudo lsof /opt/management-system/backend/data/application_system.db
   ```

### 性能优化

1. **Node.js优化**
   ```bash
   # 设置环境变量
   export NODE_OPTIONS="--max-old-space-size=2048"
   ```

2. **系统优化**
   ```bash
   # 调整文件描述符限制
   echo "* soft nofile 65536" | sudo tee -a /etc/security/limits.conf
   echo "* hard nofile 65536" | sudo tee -a /etc/security/limits.conf
   ```

---

*本部署指南涵盖了从开发到生产的完整部署流程，确保系统稳定运行。*
