/**
 * 高级缓存管理模块
 * 提供多层缓存策略、自动失效、内存管理和性能监控
 * 支持LRU算法、TTL过期、标签分组等高级功能
 */
class CacheManager {
    constructor(options = {}) {
        this.config = {
            maxSize: options.maxSize || 1000,           // 最大缓存条目数
            defaultTTL: options.defaultTTL || 300000,   // 默认TTL: 5分钟
            maxMemoryMB: options.maxMemoryMB || 100,    // 最大内存使用: 100MB
            cleanupInterval: options.cleanupInterval || 60000, // 清理间隔: 1分钟
            enableStats: options.enableStats !== false  // 启用统计
        };

        // 缓存存储
        this.cache = new Map();
        
        // LRU访问顺序跟踪
        this.accessOrder = new Map();
        
        // 标签分组
        this.tags = new Map();
        
        // 统计信息
        this.stats = {
            hits: 0,
            misses: 0,
            sets: 0,
            deletes: 0,
            evictions: 0,
            memoryUsage: 0,
            lastCleanup: null
        };

        // 启动定期清理
        this.startCleanupSchedule();
    }

    /**
     * 设置缓存项
     * @param {string} key - 缓存键
     * @param {any} value - 缓存值
     * @param {Object} options - 选项
     */
    set(key, value, options = {}) {
        try {
            const now = Date.now();
            const ttl = options.ttl || this.config.defaultTTL;
            const tags = options.tags || [];
            
            // 创建缓存项
            const cacheItem = {
                key,
                value,
                createdAt: now,
                lastAccessed: now,
                expiresAt: now + ttl,
                accessCount: 0,
                size: this.calculateSize(value),
                tags: new Set(tags)
            };

            // 检查内存限制
            if (this.shouldEvictForMemory(cacheItem.size)) {
                this.evictLRU();
            }

            // 检查大小限制
            if (this.cache.size >= this.config.maxSize) {
                this.evictLRU();
            }

            // 如果键已存在，先清理旧的标签关联
            if (this.cache.has(key)) {
                this.removeFromTags(key);
            }

            // 设置缓存
            this.cache.set(key, cacheItem);
            this.accessOrder.set(key, now);

            // 添加标签关联
            this.addToTags(key, tags);

            // 更新统计
            if (this.config.enableStats) {
                this.stats.sets++;
                this.updateMemoryUsage();
            }

            return true;
        } catch (error) {
            console.error('缓存设置失败:', error);
            return false;
        }
    }

    /**
     * 获取缓存项
     * @param {string} key - 缓存键
     * @returns {any} 缓存值或undefined
     */
    get(key) {
        try {
            const cacheItem = this.cache.get(key);
            
            if (!cacheItem) {
                if (this.config.enableStats) {
                    this.stats.misses++;
                }
                return undefined;
            }

            const now = Date.now();

            // 检查是否过期
            if (cacheItem.expiresAt <= now) {
                this.delete(key);
                if (this.config.enableStats) {
                    this.stats.misses++;
                }
                return undefined;
            }

            // 更新访问信息
            cacheItem.lastAccessed = now;
            cacheItem.accessCount++;
            this.accessOrder.set(key, now);

            if (this.config.enableStats) {
                this.stats.hits++;
            }

            return cacheItem.value;
        } catch (error) {
            console.error('缓存获取失败:', error);
            if (this.config.enableStats) {
                this.stats.misses++;
            }
            return undefined;
        }
    }

    /**
     * 删除缓存项
     * @param {string} key - 缓存键
     * @returns {boolean} 是否成功删除
     */
    delete(key) {
        try {
            const existed = this.cache.has(key);
            
            if (existed) {
                this.removeFromTags(key);
                this.cache.delete(key);
                this.accessOrder.delete(key);

                if (this.config.enableStats) {
                    this.stats.deletes++;
                    this.updateMemoryUsage();
                }
            }

            return existed;
        } catch (error) {
            console.error('缓存删除失败:', error);
            return false;
        }
    }

    /**
     * 检查缓存项是否存在且未过期
     * @param {string} key - 缓存键
     * @returns {boolean} 是否存在
     */
    has(key) {
        const cacheItem = this.cache.get(key);
        if (!cacheItem) return false;

        const now = Date.now();
        if (cacheItem.expiresAt <= now) {
            this.delete(key);
            return false;
        }

        return true;
    }

    /**
     * 根据标签删除缓存项
     * @param {string|Array} tags - 标签
     */
    deleteByTags(tags) {
        const tagsArray = Array.isArray(tags) ? tags : [tags];
        const keysToDelete = new Set();

        tagsArray.forEach(tag => {
            const tagKeys = this.tags.get(tag);
            if (tagKeys) {
                tagKeys.forEach(key => keysToDelete.add(key));
            }
        });

        keysToDelete.forEach(key => this.delete(key));
        
        return keysToDelete.size;
    }

    /**
     * 清空所有缓存
     */
    clear() {
        this.cache.clear();
        this.accessOrder.clear();
        this.tags.clear();
        
        if (this.config.enableStats) {
            this.stats.memoryUsage = 0;
        }
    }

    /**
     * 获取缓存统计信息
     */
    getStats() {
        if (!this.config.enableStats) {
            return { message: '统计功能已禁用' };
        }

        const hitRate = this.stats.hits + this.stats.misses > 0 
            ? (this.stats.hits / (this.stats.hits + this.stats.misses) * 100).toFixed(2)
            : 0;

        return {
            ...this.stats,
            hitRate: `${hitRate}%`,
            cacheSize: this.cache.size,
            maxSize: this.config.maxSize,
            memoryUsageMB: (this.stats.memoryUsage / (1024 * 1024)).toFixed(2),
            maxMemoryMB: this.config.maxMemoryMB,
            tagCount: this.tags.size
        };
    }

    /**
     * 获取缓存项详细信息
     */
    getItemInfo(key) {
        const cacheItem = this.cache.get(key);
        if (!cacheItem) return null;

        const now = Date.now();
        return {
            key: cacheItem.key,
            size: cacheItem.size,
            createdAt: new Date(cacheItem.createdAt),
            lastAccessed: new Date(cacheItem.lastAccessed),
            expiresAt: new Date(cacheItem.expiresAt),
            accessCount: cacheItem.accessCount,
            ttlRemaining: Math.max(0, cacheItem.expiresAt - now),
            isExpired: cacheItem.expiresAt <= now,
            tags: Array.from(cacheItem.tags)
        };
    }

    /**
     * 获取所有缓存键
     */
    keys() {
        return Array.from(this.cache.keys());
    }

    /**
     * 获取缓存大小
     */
    size() {
        return this.cache.size;
    }

    /**
     * LRU淘汰算法
     */
    evictLRU() {
        if (this.cache.size === 0) return;

        // 找到最久未访问的项
        let oldestKey = null;
        let oldestTime = Infinity;

        for (const [key, time] of this.accessOrder.entries()) {
            if (time < oldestTime) {
                oldestTime = time;
                oldestKey = key;
            }
        }

        if (oldestKey) {
            this.delete(oldestKey);
            if (this.config.enableStats) {
                this.stats.evictions++;
            }
        }
    }

    /**
     * 检查是否需要为内存而淘汰
     */
    shouldEvictForMemory(newItemSize) {
        const currentMemoryMB = this.stats.memoryUsage / (1024 * 1024);
        const newItemSizeMB = newItemSize / (1024 * 1024);
        
        return (currentMemoryMB + newItemSizeMB) > this.config.maxMemoryMB;
    }

    /**
     * 计算对象大小（粗略估算）
     */
    calculateSize(obj) {
        try {
            const jsonString = JSON.stringify(obj);
            return jsonString.length * 2; // 假设每个字符2字节
        } catch (error) {
            return 1024; // 默认1KB
        }
    }

    /**
     * 更新内存使用统计
     */
    updateMemoryUsage() {
        if (!this.config.enableStats) return;

        let totalSize = 0;
        for (const cacheItem of this.cache.values()) {
            totalSize += cacheItem.size;
        }
        this.stats.memoryUsage = totalSize;
    }

    /**
     * 添加到标签组
     */
    addToTags(key, tags) {
        tags.forEach(tag => {
            if (!this.tags.has(tag)) {
                this.tags.set(tag, new Set());
            }
            this.tags.get(tag).add(key);
        });
    }

    /**
     * 从标签组中移除
     */
    removeFromTags(key) {
        const cacheItem = this.cache.get(key);
        if (!cacheItem) return;

        cacheItem.tags.forEach(tag => {
            const tagKeys = this.tags.get(tag);
            if (tagKeys) {
                tagKeys.delete(key);
                if (tagKeys.size === 0) {
                    this.tags.delete(tag);
                }
            }
        });
    }

    /**
     * 清理过期项
     */
    cleanup() {
        const now = Date.now();
        const expiredKeys = [];

        for (const [key, cacheItem] of this.cache.entries()) {
            if (cacheItem.expiresAt <= now) {
                expiredKeys.push(key);
            }
        }

        expiredKeys.forEach(key => this.delete(key));

        if (this.config.enableStats) {
            this.stats.lastCleanup = new Date();
        }

        return expiredKeys.length;
    }

    /**
     * 启动定期清理
     */
    startCleanupSchedule() {
        setInterval(() => {
            const cleanedCount = this.cleanup();
            if (cleanedCount > 0) {
                // 缓存清理完成，详细信息已记录到日志
        if (cleanedCount > 0) {
            console.log(`🧹 缓存清理: ${cleanedCount}个过期项`);
        }
            }
        }, this.config.cleanupInterval);
    }

    /**
     * 获取热点数据（访问频率最高的缓存项）
     */
    getHotData(limit = 10) {
        const items = Array.from(this.cache.entries())
            .map(([key, item]) => ({
                key,
                accessCount: item.accessCount,
                lastAccessed: item.lastAccessed,
                size: item.size
            }))
            .sort((a, b) => b.accessCount - a.accessCount)
            .slice(0, limit);

        return items;
    }

    /**
     * 预热缓存
     * @param {Function} dataLoader - 数据加载函数
     * @param {Array} keys - 要预热的键列表
     */
    async warmup(dataLoader, keys) {
        const results = [];
        
        for (const key of keys) {
            try {
                if (!this.has(key)) {
                    const data = await dataLoader(key);
                    if (data !== undefined) {
                        this.set(key, data);
                        results.push({ key, success: true });
                    } else {
                        results.push({ key, success: false, reason: 'No data' });
                    }
                } else {
                    results.push({ key, success: true, reason: 'Already cached' });
                }
            } catch (error) {
                results.push({ key, success: false, reason: error.message });
            }
        }

        return results;
    }
}

module.exports = CacheManager;
