# API接口文档

## 📋 概述

本文档详细描述了管理系统的所有API接口，包括请求格式、响应格式、错误处理等信息。

### 基础信息
- **基础URL**: `http://localhost:3000`
- **数据格式**: JSON
- **字符编码**: UTF-8
- **认证方式**: Session-based

## 🔐 认证接口

### 用户登录
```http
POST /api/login
```

**请求体**:
```json
{
  "username": "string",
  "password": "string"
}
```

**响应**:
```json
{
  "success": true,
  "message": "登录成功",
  "user": {
    "id": 1,
    "username": "admin",
    "role": "admin",
    "department": "IT部门"
  }
}
```

### 用户登出
```http
POST /api/logout
```

**响应**:
```json
{
  "success": true,
  "message": "登出成功"
}
```

## 👥 用户管理接口

### 获取用户列表
```http
GET /api/users
```

**查询参数**:
- `page`: 页码 (可选)
- `limit`: 每页数量 (可选)
- `search`: 搜索关键词 (可选)

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "username": "admin",
      "email": "<EMAIL>",
      "role": "admin",
      "department": "IT部门",
      "created_at": "2025-01-01T00:00:00.000Z"
    }
  ],
  "total": 100,
  "page": 1,
  "limit": 20
}
```

### 创建用户
```http
POST /api/users
```

**请求体**:
```json
{
  "username": "string",
  "password": "string",
  "email": "string",
  "role": "user|admin|manager",
  "department": "string"
}
```

### 更新用户
```http
PUT /api/users/:id
```

### 删除用户
```http
DELETE /api/users/:id
```

## 🔧 设备管理接口

### 获取设备列表
```http
GET /api/devices
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `search`: 搜索关键词
- `status`: 设备状态筛选
- `location`: 位置筛选

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "name": "设备名称",
      "model": "型号",
      "serial_number": "序列号",
      "location": "位置",
      "status": "运行中|维修中|停用",
      "health_score": 85,
      "last_maintenance": "2025-01-01",
      "next_maintenance": "2025-02-01"
    }
  ]
}
```

### 创建设备
```http
POST /api/devices
```

**请求体**:
```json
{
  "name": "string",
  "model": "string",
  "serial_number": "string",
  "location": "string",
  "purchase_date": "YYYY-MM-DD",
  "warranty_period": "number (months)",
  "specifications": "string"
}
```

### 更新设备
```http
PUT /api/devices/:id
```

### 删除设备
```http
DELETE /api/devices/:id
```

### 获取设备详情
```http
GET /api/devices/:id
```

### 设备健康评估
```http
GET /api/devices/:id/health
```

**响应**:
```json
{
  "success": true,
  "data": {
    "device_id": 1,
    "health_score": 85,
    "status": "良好",
    "last_check": "2025-01-01T10:00:00.000Z",
    "issues": [
      {
        "type": "warning",
        "message": "建议更换滤芯",
        "priority": "medium"
      }
    ],
    "recommendations": [
      "定期清洁设备表面",
      "检查电源连接"
    ]
  }
}
```

## 🔨 维修管理接口

### 获取维修记录
```http
GET /api/maintenance
```

**查询参数**:
- `device_id`: 设备ID筛选
- `type`: 维修类型筛选
- `start_date`: 开始日期
- `end_date`: 结束日期

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "device_id": 1,
      "device_name": "设备名称",
      "type": "预防性维护|故障维修|定期保养",
      "description": "维修描述",
      "cost": 1000.00,
      "technician": "技术员姓名",
      "date": "2025-01-01",
      "duration": 120,
      "status": "已完成|进行中|计划中"
    }
  ]
}
```

### 创建维修记录
```http
POST /api/maintenance
```

**请求体**:
```json
{
  "device_id": "number",
  "type": "string",
  "description": "string",
  "scheduled_date": "YYYY-MM-DD",
  "estimated_cost": "number",
  "technician": "string",
  "priority": "low|medium|high|urgent"
}
```

### 更新维修记录
```http
PUT /api/maintenance/:id
```

### 完成维修
```http
POST /api/maintenance/:id/complete
```

**请求体**:
```json
{
  "actual_cost": "number",
  "completion_notes": "string",
  "parts_used": [
    {
      "name": "零件名称",
      "quantity": 1,
      "cost": 100.00
    }
  ]
}
```

## 📝 申请管理接口

### 获取申请列表
```http
GET /api/applications
```

**响应**:
```json
{
  "success": true,
  "data": [
    {
      "id": 1,
      "title": "申请标题",
      "content": "申请内容",
      "applicant": "申请人",
      "status": "待审批|已批准|已拒绝",
      "created_at": "2025-01-01T00:00:00.000Z",
      "updated_at": "2025-01-01T00:00:00.000Z",
      "attachments": ["file1.pdf", "file2.jpg"]
    }
  ]
}
```

### 创建申请
```http
POST /api/applications
```

**请求体**:
```json
{
  "title": "string",
  "content": "string",
  "applicant": "string",
  "department": "string",
  "urgency": "low|medium|high",
  "category": "string"
}
```

### 审批申请
```http
POST /api/applications/:id/approve
```

**请求体**:
```json
{
  "action": "approve|reject",
  "comments": "string",
  "approver": "string"
}
```

## 📊 统计分析接口

### 仪表板数据
```http
GET /api/dashboard/stats
```

**响应**:
```json
{
  "success": true,
  "data": {
    "total_devices": 150,
    "active_devices": 142,
    "maintenance_due": 8,
    "pending_applications": 12,
    "monthly_costs": 25000.00,
    "device_health_avg": 87.5,
    "charts": {
      "device_status": {
        "labels": ["运行中", "维修中", "停用"],
        "data": [142, 5, 3]
      },
      "maintenance_trend": {
        "labels": ["1月", "2月", "3月", "4月", "5月", "6月"],
        "data": [15, 12, 18, 20, 16, 14]
      }
    }
  }
}
```

### 设备统计
```http
GET /api/stats/devices
```

### 维修统计
```http
GET /api/stats/maintenance
```

### 成本分析
```http
GET /api/stats/costs
```

## 📤 导出接口

### 导出设备列表
```http
GET /api/export/devices
```

**查询参数**:
- `format`: excel|pdf|csv
- `filters`: JSON格式的筛选条件

### 导出维修记录
```http
GET /api/export/maintenance
```

### 导出申请记录
```http
GET /api/export/applications
```

## 📁 文件管理接口

### 文件上传
```http
POST /api/upload
```

**请求**: multipart/form-data
- `file`: 文件数据
- `type`: 文件类型 (application|device|maintenance)
- `related_id`: 关联记录ID

**响应**:
```json
{
  "success": true,
  "data": {
    "filename": "uploaded_file.pdf",
    "original_name": "原始文件名.pdf",
    "size": 1024000,
    "url": "/uploads/uploaded_file.pdf"
  }
}
```

### 文件下载
```http
GET /api/files/:filename
```

### 文件删除
```http
DELETE /api/files/:filename
```

## 🏢 组织管理接口

### 部门管理
```http
GET /api/departments
POST /api/departments
PUT /api/departments/:id
DELETE /api/departments/:id
```

### 厂区管理
```http
GET /api/factories
POST /api/factories
PUT /api/factories/:id
DELETE /api/factories/:id
```

## ⚠️ 错误处理

### 标准错误响应格式
```json
{
  "success": false,
  "error": {
    "code": "ERROR_CODE",
    "message": "错误描述",
    "details": "详细错误信息"
  }
}
```

### 常见错误码
- `400` - 请求参数错误
- `401` - 未授权访问
- `403` - 权限不足
- `404` - 资源不存在
- `409` - 资源冲突
- `422` - 数据验证失败
- `500` - 服务器内部错误

## 🔧 请求示例

### JavaScript Fetch示例
```javascript
// 获取设备列表
const response = await fetch('/api/devices?page=1&limit=20', {
  method: 'GET',
  headers: {
    'Content-Type': 'application/json'
  }
});
const data = await response.json();

// 创建设备
const newDevice = await fetch('/api/devices', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    name: '新设备',
    model: 'Model-X',
    location: '车间A'
  })
});
```

### cURL示例
```bash
# 获取设备列表
curl -X GET "http://localhost:3000/api/devices" \
  -H "Content-Type: application/json"

# 创建设备
curl -X POST "http://localhost:3000/api/devices" \
  -H "Content-Type: application/json" \
  -d '{"name":"新设备","model":"Model-X","location":"车间A"}'
```

---

*本API文档提供了完整的接口说明，便于前端开发和第三方集成。*
