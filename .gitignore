# Node.js dependencies
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Database files
backend/data/*.db
backend/data/*.db-shm
backend/data/*.db-wal
backend/data/database.db

# Log files
logs/
*.log

# Uploaded files
uploads/
archive/

# Environment variables
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# IDE files
.vscode/
.idea/
*.swp
*.swo
*~

# OS generated files
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db

# Temporary files
*.tmp
*.temp

# Backup files
*.backup
*.bak

# JSON backup files
backend/data/json_backup_*

# Package lock files (optional, depending on team preference)
# package-lock.json
