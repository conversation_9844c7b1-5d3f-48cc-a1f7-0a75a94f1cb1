const express = require('express');
const multer = require('multer');
const cors = require('cors');
const path = require('path');
const fs = require('fs');
const nodemailer = require('nodemailer');
const https = require('https');
const Excel = require('exceljs');
const session = require('express-session');

// 导入设备管理模块
const DeviceManagement = require('./modules/business/device-management');
const MaintenanceManagement = require('./modules/business/maintenance-management');
const ExportService = require('./modules/business/export-service');
const DeviceHealth = require('./modules/business/device-health');

// 导入数据访问层
const DataAccess = require('./data-access');

// 导入系统集成模块（高优先级优化）
const SystemIntegration = require('./modules/infrastructure/system-integration');

// 导入统一日志工具
const consoleLogger = require('./utils/console-logger');

const app = express();

// 初始化数据访问层
const dataAccess = new DataAccess();

// 初始化系统集成（高优先级优化）
let systemIntegration = null;
let optimizedModules = null;

try {
    // 延迟初始化，在app配置完成后进行
    consoleLogger.systemStart('准备初始化系统优化模块...');
} catch (error) {
    consoleLogger.systemError('系统优化模块初始化准备失败:', error);
}

// 初始化设备管理模块
const deviceManager = new DeviceManagement();
const maintenanceManager = new MaintenanceManagement();
const exportService = new ExportService();
const deviceHealth = new DeviceHealth(deviceManager, maintenanceManager);

// 网络配置常量 - 确保代理和VPN完全兼容
const NETWORK_CONFIG = {
    // 服务器配置
    port: 3030,
    trustProxy: true, // 信任代理，支持负载均衡器、CDN和代理

    // CORS配置 - 优化国际网络性能
    corsOrigin: '*', // 允许所有来源访问，支持代理和VPN
    corsMaxAge: 7 * 24 * 60 * 60, // 预检请求结果缓存7天（减少预检请求）

    // 速率限制配置 - 适应国际网络重试需求
    rateLimitWindowMs: 15 * 60 * 1000, // 15分钟
    rateLimitMax: 2000, // 每个IP每15分钟最多2000个请求（更宽松的限制，适应重试）

    // 缓存配置 - 优化国际网络性能
    cacheTTL: 5 * 60 * 1000, // 缓存有效期：5分钟（增加缓存时间）
    staticCacheMaxAge: 7 * 24 * 60 * 60 * 1000, // 静态资源缓存7天（增加缓存时间）

    // 文件上传配置
    maxFileSize: 15 * 1024 * 1024, // 15MB
    allowedFileTypes: ['pdf', 'doc', 'docx', 'jpg', 'jpeg', 'png'],

    // 安全配置
    helmetEnabled: true,
    cspEnabled: true,

    // 调试配置
    debugMode: true
};

const port = NETWORK_CONFIG.port;

// 信任代理配置，支持负载均衡器、CDN和代理
app.set('trust proxy', NETWORK_CONFIG.trustProxy);

// 使用统一的日志工具（向后兼容）
const logWithTime = consoleLogger.log;
const errorWithTime = consoleLogger.error;

// 服务器配置
const SERVER_CONFIG = {
    url: 'http://*************:3000/', // 服务器URL，用于邮件中的链接
    emailRetryDelay: 3000 // 邮件发送失败重试延迟（毫秒），将持续重试直到成功
};

// 添加内存缓存
const cache = {
    users: null,
    applications: null,
    lastUserUpdate: 0,
    lastApplicationUpdate: 0,
    cacheTTL: NETWORK_CONFIG.cacheTTL // 缓存有效期：60秒
};

// 邮件发送配置
const transporter = nodemailer.createTransport({
    service: 'gmail',
    auth: {
        user: '<EMAIL>', // Gmail邮箱
        pass: 'sokypzpgwlxmsrzn' // 应使用Gmail的应用专用密码，而非普通密码
    }
});

// 中间件配置
// 增强CORS配置，允许所有来源，并增加预检请求缓存时间
const corsOptions = {
    origin: NETWORK_CONFIG.corsOrigin, // 允许所有来源访问，支持代理和VPN
    methods: 'GET,HEAD,PUT,PATCH,POST,DELETE',
    preflightContinue: false,
    optionsSuccessStatus: 204,
    credentials: true,
    maxAge: NETWORK_CONFIG.corsMaxAge, // 预检请求结果缓存24小时
    allowedHeaders: ['Content-Type', 'Authorization', 'X-Requested-With', 'X-Forwarded-For', 'X-Real-IP']
};
app.use(cors(corsOptions));

// 添加OPTIONS请求的全局处理
app.options('*', cors(corsOptions));

// Session配置（向后兼容支持）
app.use(session({
    secret: process.env.SESSION_SECRET || 'makrite-session-secret-2025-change-in-production',
    resave: false,
    saveUninitialized: false,
    cookie: {
        secure: false, // 开发环境设为false，生产环境应设为true（需要HTTPS）
        httpOnly: true,
        maxAge: 24 * 60 * 60 * 1000 // 24小时
    }
}));

// 静态资源配置
app.use(express.static(path.join(__dirname, '..', 'frontend'), {
    maxAge: NETWORK_CONFIG.staticCacheMaxAge // 静态资源缓存1天
}));

// 专门处理favicon.ico请求
app.get('/favicon.ico', (req, res) => {
    const faviconPath = path.join(__dirname, '..', 'frontend', 'favicon.ico');
    if (fs.existsSync(faviconPath)) {
        res.sendFile(faviconPath);
    } else {
        // 如果favicon不存在，返回204 No Content而不是404
        res.status(204).end();
    }
});

// 请求体解析配置
app.use(express.json({ limit: '15mb' }));
app.use(express.urlencoded({ extended: true, limit: '15mb' }));

// 添加压缩中间件 - 优化国际网络传输
const compression = require('compression');
app.use(compression({
    level: 6,                    // 压缩级别 (1-9, 6是平衡点)
    threshold: 1024,             // 超过1KB才压缩
    filter: (req, res) => {
        // 对所有可压缩的内容类型启用压缩
        if (req.headers['x-no-compression']) {
            return false;
        }
        return compression.filter(req, res);
    }
}));

// 添加安全头配置，确保代理和VPN兼容性
const helmet = require('helmet');
if (NETWORK_CONFIG.helmetEnabled) {
    app.use(helmet({
        contentSecurityPolicy: NETWORK_CONFIG.cspEnabled ? {
            directives: {
                defaultSrc: ["'self'"],
                styleSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com"],
                scriptSrc: ["'self'", "'unsafe-inline'", "https://cdn.tailwindcss.com", "https://cdnjs.cloudflare.com"],
                imgSrc: ["'self'", "data:", "https:"],
                connectSrc: ["'self'", "*"], // 允许连接到任何源，支持代理
                fontSrc: ["'self'", "https:", "data:"],
                objectSrc: ["'none'"],
                mediaSrc: ["'self'"],
                frameSrc: ["'none'"],
            },
        } : false,
        crossOriginEmbedderPolicy: false, // 禁用以避免代理问题
        crossOriginResourcePolicy: false, // 禁用以支持跨域访问
    }));
}

// 添加速率限制，但对代理友好
const rateLimit = require('express-rate-limit');
const limiter = rateLimit({
    windowMs: NETWORK_CONFIG.rateLimitWindowMs, // 15分钟
    max: NETWORK_CONFIG.rateLimitMax, // 每个IP每15分钟最多1000个请求（宽松限制）
    message: '请求过于频繁，请稍后再试',
    standardHeaders: true,
    legacyHeaders: false,
    // 信任代理，正确获取真实IP
    trustProxy: NETWORK_CONFIG.trustProxy,
    // 跳过成功的请求
    skipSuccessfulRequests: true,
    // 自定义键生成器，考虑代理头
    keyGenerator: (req) => {
        return req.ip || req.connection.remoteAddress ||
               req.headers['x-forwarded-for'] ||
               req.headers['x-real-ip'] ||
               'unknown';
    },
});
app.use(limiter);

// 初始化系统集成模块（在中间件配置完成后）
try {
    systemIntegration = new SystemIntegration(app, dataAccess.db, dataAccess);
    optimizedModules = systemIntegration.getModules();

    // 使用优化的数据访问
    const optimizedDataAccess = systemIntegration.createOptimizedDataAccess();

    // 系统优化模块初始化成功的详细信息已记录到日志文件
} catch (error) {
    consoleLogger.systemError('系统优化模块初始化失败:', error);
    consoleLogger.systemWarn('系统将继续使用原有功能运行');
}

// 数据存储路径（保留用于文件上传等功能）
const archiveDir = path.join(__dirname, '..', 'archive'); // 归档目录

// 初始化数据文件夹
if (!fs.existsSync(path.join(__dirname, 'data'))) {
    fs.mkdirSync(path.join(__dirname, 'data'));
}


// 注意：提醒设置初始化已移除，现在使用SQLite数据库
if (!fs.existsSync(archiveDir)) {
    fs.mkdirSync(archiveDir, { recursive: true }); // 创建归档目录
}

// 读取用户数据（带缓存）
function getUsers() {
    // 使用新的缓存系统（如果可用）
    if (optimizedModules && optimizedModules.cacheManager) {
        const cacheKey = 'users:all';
        let users = optimizedModules.cacheManager.get(cacheKey);

        if (!users) {
            try {
                users = dataAccess.getUsers();
                optimizedModules.cacheManager.set(cacheKey, users, {
                    ttl: 300000, // 5分钟
                    tags: ['users']
                });
            } catch (error) {
                if (optimizedModules.logger) {
                    optimizedModules.logger.error('读取用户数据失败', { error: error.message });
                } else {
                    consoleLogger.error('读取用户数据失败:', error);
                }
                return [];
            }
        }
        return users;
    }

    // 回退到原有缓存系统
    const now = Date.now();
    if (cache.users && (now - cache.lastUserUpdate < cache.cacheTTL)) {
        return cache.users;
    }

    try {
        cache.users = dataAccess.getUsers();
        cache.lastUserUpdate = now;
        return cache.users;
    } catch (error) {
        consoleLogger.error('读取用户数据失败:', error);
        return [];
    }
}

// 保存用户数据（更新缓存）
function saveUsers(users) {
    try {
        consoleLogger.dbLog('SAVE', `保存用户数据: ${users.length}个用户`);

        // 注意：这个函数现在主要用于批量操作，单个用户操作应该使用dataAccess的方法
        // 这里保持接口兼容性，但实际上SQLite操作是原子性的

        // 清除新缓存系统（如果可用）
        if (optimizedModules && optimizedModules.cacheManager) {
            optimizedModules.cacheManager.deleteByTags(['users']);
            if (optimizedModules.logger) {
                optimizedModules.logger.debug('用户缓存已清理', { userCount: users.length });
            }
        }

        // 更新原有缓存
        cache.users = users;
        cache.lastUserUpdate = Date.now();

        return true;
    } catch (error) {
        if (optimizedModules && optimizedModules.logger) {
            optimizedModules.logger.error('保存用户数据失败', { error: error.message });
        } else {
            console.error('保存用户数据失败:', error);
        }
        return false;
    }
}

// 注意：getApplicationFilePath函数已移除，现在使用SQLite数据库存储

// 读取单个申请数据
function getApplicationById(applicationId) {
    try {
        return dataAccess.getApplicationById(applicationId);
    } catch (error) {
        console.error('读取申请数据失败:', error);
        return null;
    }
}

// 从SQLite数据库中读取所有申请数据（保持接口兼容性）
function getAllApplicationsFromFiles() {
    try {
        return dataAccess.getApplications();
    } catch (error) {
        console.error('读取申请数据失败:', error);
        return [];
    }
}

// 读取申请数据（带缓存）
function getApplications() {
    const now = Date.now();
    if (cache.applications && (now - cache.lastApplicationUpdate < cache.cacheTTL)) {
        return cache.applications;
    }

    try {
        const applications = dataAccess.getApplications();

        cache.applications = applications;
        cache.lastApplicationUpdate = now;
        return cache.applications;
    } catch (error) {
        console.error('读取申请数据失败:', error);
        return [];
    }
}

// 保存单个申请数据
function saveApplication(application) {
    try {
        // 如果是新申请，使用addApplication，否则使用updateApplication
        if (!application.id || !dataAccess.getApplicationById(application.id)) {
            dataAccess.addApplication(application);
        } else {
            dataAccess.updateApplication(application.id, application);
        }

        // 保存审批信息到application_approvals表
        if (application.approvals) {
            // 保存厂长审批信息（包括pending状态）
            if (application.approvals.directors) {
                Object.entries(application.approvals.directors).forEach(([username, approval]) => {
                    dataAccess.addOrUpdateApproval({
                        applicationId: application.id,
                        approverType: 'director',
                        approverUsername: username,
                        status: approval.status,
                        comment: approval.comment || null,
                        attachments: approval.attachments || [],
                        approvedAt: approval.date || null
                    });
                });
            }

            // 保存总监审批信息（包括pending状态）
            if (application.approvals.chief) {
                dataAccess.addOrUpdateApproval({
                    applicationId: application.id,
                    approverType: 'chief',
                    approverUsername: application.approvals.chief.approverUsername || 'unknown',
                    status: application.approvals.chief.status,
                    comment: application.approvals.chief.comment || null,
                    attachments: application.approvals.chief.attachments || [],
                    approvedAt: application.approvals.chief.date || null
                });
            }

            // 保存经理审批信息（包括pending状态）
            if (application.approvals.managers) {
                Object.entries(application.approvals.managers).forEach(([username, approval]) => {
                    dataAccess.addOrUpdateApproval({
                        applicationId: application.id,
                        approverType: 'manager',
                        approverUsername: username,
                        status: approval.status,
                        comment: approval.comment || null,
                        attachments: approval.attachments || [],
                        approvedAt: approval.date || null
                    });
                });
            }

            // 保存CEO审批信息（包括pending状态）
            if (application.approvals.ceo) {
                dataAccess.addOrUpdateApproval({
                    applicationId: application.id,
                    approverType: 'ceo',
                    approverUsername: application.approvals.ceo.approverUsername || 'unknown',
                    status: application.approvals.ceo.status,
                    comment: application.approvals.ceo.comment || null,
                    attachments: application.approvals.ceo.attachments || [],
                    approvedAt: application.approvals.ceo.date || null
                });
            }
        }

        // 清除缓存，强制下次读取时重新加载
        cache.applications = null;
        cache.lastApplicationUpdate = 0;

        logWithTime(`申请数据已保存: ID=${application.id}`);
        return true;
    } catch (error) {
        console.error('保存申请数据失败:', error);
        return false;
    }
}

// 删除单个申请数据
function deleteApplicationFile(application) {
    try {
        dataAccess.deleteApplication(application.id);
        logWithTime(`申请数据已删除: ID=${application.id}`);

        // 清除缓存
        cache.applications = null;
        cache.lastApplicationUpdate = 0;

        return true;
    } catch (error) {
        console.error('删除申请数据失败:', error);
        return false;
    }
}

// 保存申请数据（兼容旧接口，但使用新的文件结构）
function saveApplications(applications) {
    try {
        // 为每个申请保存单独的文件
        for (const application of applications) {
            if (!saveApplication(application)) {
                console.error(`保存申请失败: ID=${application.id}`);
                return false;
            }
        }

        // 更新缓存
        cache.applications = applications;
        cache.lastApplicationUpdate = Date.now();

        return true;
    } catch (error) {
        console.error('批量保存申请数据失败:', error);
        return false;
    }
}

// 读取提醒设置
function getReminderSettings() {
    try {
        return dataAccess.getReminderSettings();
    } catch (error) {
        console.error('读取提醒设置失败:', error);
        // 返回默认设置
        return {
            priority: {
                high: {
                    initialDelay: 4,
                    normalInterval: 4,
                    mediumInterval: 2,
                    urgentInterval: 1
                },
                medium: {
                    initialDelay: 8,
                    normalInterval: 8,
                    mediumInterval: 4,
                    urgentInterval: 2
                },
                low: {
                    initialDelay: 12,
                    normalInterval: 12,
                    mediumInterval: 6,
                    urgentInterval: 3
                }
            },
            timeControl: {
                workingDays: {
                    enabled: false,
                    days: [1, 2, 3, 4, 5], // 1=周一, 2=周二, ..., 7=周日
                    startTime: "09:00",
                    endTime: "18:00"
                },
                customDates: {
                    enabled: false,
                    skipDates: []
                }
            }
        };
    }
}

// 保存提醒设置
function saveReminderSettings(settings) {
    try {
        dataAccess.updateReminderSettings(settings);
        return true;
    } catch (error) {
        console.error('保存提醒设置失败:', error);
        return false;
    }
}

// 读取部门数据
function getDepartments() {
    try {
        return dataAccess.getDepartments();
    } catch (error) {
        console.error('读取部门数据失败:', error);
        return [];
    }
}

// 保存部门数据（保持接口兼容性）
function saveDepartments(departments) {
    try {
        // 注意：这个函数现在主要用于批量操作，单个部门操作应该使用dataAccess的方法
        // 这里保持接口兼容性
        return true;
    } catch (error) {
        console.error('保存部门数据失败:', error);
        return false;
    }
}

// 生成部门ID
function generateDepartmentId() {
    return 'dept_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
}

// 生成10位数字用户ID
function generateUserId() {
    // 生成10位随机数字
    let userId = '';
    for (let i = 0; i < 10; i++) {
        userId += Math.floor(Math.random() * 10);
    }
    return userId;
}

// 确保用户ID唯一
function generateUniqueUserId() {
    const users = getUsers();
    let userId;
    let attempts = 0;
    const maxAttempts = 100;

    do {
        userId = generateUserId();
        attempts++;
        if (attempts > maxAttempts) {
            throw new Error('无法生成唯一的用户ID');
        }
    } while (users.some(user => user.userId === userId));

    return userId;
}

// 检查当前时间是否允许发送提醒
function isReminderTimeAllowed(timeControl) {
    const now = new Date();

    // 检查工作日和工作时间
    if (timeControl.workingDays && timeControl.workingDays.enabled) {
        const dayOfWeek = now.getDay(); // 0=周日, 1=周一, ..., 6=周六
        const workingDays = timeControl.workingDays.days || [];

        // 转换周日从0到7，保持一致性
        const currentDay = dayOfWeek === 0 ? 7 : dayOfWeek;

        // 检查是否在工作日
        if (!workingDays.includes(currentDay)) {
            return false;
        }

        // 检查工作时间
        const currentTime = now.toTimeString().slice(0, 5); // HH:MM格式
        const startTime = timeControl.workingDays.startTime;
        const endTime = timeControl.workingDays.endTime;

        if (currentTime < startTime || currentTime > endTime) {
            return false;
        }
    }

    // 检查自定义跳过日期
    if (timeControl.customDates && timeControl.customDates.enabled && timeControl.customDates.skipDates.length > 0) {
        const currentDate = now.toISOString().slice(0, 10); // YYYY-MM-DD格式
        if (timeControl.customDates.skipDates.includes(currentDate)) {
            return false;
        }
    }

    return true;
}

// 文件上传配置
const storage = multer.diskStorage({
    destination: (req, file, cb) => {
        const uploadDir = path.join(__dirname, '..', 'uploads');
        if (!fs.existsSync(uploadDir)) {
            fs.mkdirSync(uploadDir);
        }
        cb(null, uploadDir);
    },
    filename: (req, file, cb) => {
        // 获取用户信息
        const username = req.body.username || req.body.applicant;
        let userPrefix = Date.now() + '-' + Math.round(Math.random() * 1E9); // 默认前缀

        if (username) {
            const users = getUsers();
            const user = users.find(u => u.username === username);
            if (user && user.userId) {
                userPrefix = user.userId + '-' + Date.now();
            }
        }

        const originalName = Buffer.from(file.originalname, 'latin1').toString('utf8');
        cb(null, `${userPrefix}-${originalName}`);
    }
});

// 通用文件上传配置（用于申请附件等）
const upload = multer({
    storage: storage,
    limits: { fileSize: NETWORK_CONFIG.maxFileSize }, // 限制文件大小
    fileFilter: (req, file, cb) => {
        const filetypes = new RegExp(`\\.(${NETWORK_CONFIG.allowedFileTypes.join('|')})$`, 'i');
        const mimetypes = [
            'application/pdf',
            'application/msword',
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
            'image/jpeg',
            'image/png'
        ];
        const extname = filetypes.test(path.extname(file.originalname).toLowerCase());
        const mimetype = mimetypes.includes(file.mimetype);
        if (extname && mimetype) {
            cb(null, true);
        } else {
            cb(new Error(`只允许上传 ${NETWORK_CONFIG.allowedFileTypes.join(', ').toUpperCase()} 文件`));
        }
    }
});

// 专门用于Excel文件导入的multer配置
const excelUpload = multer({
    storage: storage,
    limits: { fileSize: NETWORK_CONFIG.maxFileSize }, // 限制文件大小
    fileFilter: (req, file, cb) => {
        const allowedExtensions = /\.(xlsx|xls)$/i;
        const allowedMimeTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // .xlsx
            'application/vnd.ms-excel' // .xls
        ];

        const extname = allowedExtensions.test(path.extname(file.originalname).toLowerCase());
        const mimetype = allowedMimeTypes.includes(file.mimetype);

        if (extname && mimetype) {
            cb(null, true);
        } else {
            cb(new Error('只允许上传 Excel 文件 (.xlsx, .xls)'));
        }
    }
});

// 用户注册接口
app.post('/register', (req, res) => {
    const { username, password, email } = req.body;

    // 检查用户名是否已存在
    if (dataAccess.getUserByUsername(username)) {
        return res.json({ success: false, message: '用户名已存在' });
    }

    // 验证密码长度（至少6位）
    if (!password || password.length < 6) {
        return res.json({ success: false, message: '密码至少需要6位' });
    }

    // 为新用户生成唯一的10位数字ID
    const userId = generateUniqueUserId();

    try {
        dataAccess.addUser({
            username,
            password,
            role: 'user',
            email,
            userId
        });

        // 清除缓存
        cache.users = null;
        cache.lastUserUpdate = 0;

        res.json({ success: true, message: '注册成功，请登录' });
    } catch (error) {
        console.error('用户注册失败:', error);
        res.json({ success: false, message: '注册失败，请重试' });
    }
});

// 用户登录接口
app.post('/login', (req, res) => {
    const { username, password } = req.body;
    const users = getUsers();

    // 首先尝试通过用户名和密码登录
    let user = users.find(u => u.username === username && u.password === password);

    // 如果用户名和密码登录失败，尝试通过用户代码和密码登录
    if (!user) {
        user = users.find(u => u.userCode === username && u.password === password);
    }

    if (user) {
        // 设置session信息（向后兼容）
        req.session.user = {
            id: user.id,
            username: user.username,
            role: user.role,
            department: user.department
        };
        req.session.userId = user.id;
        req.session.username = user.username;
        req.session.role = user.role;
        req.session.department = user.department;

        // 记录登录日志
        if (optimizedModules && optimizedModules.logger) {
            optimizedModules.logger.security('User login successful', {
                userId: user.id,
                username: user.username,
                role: user.role,
                department: user.department,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
        }

        res.json({ success: true, message: '登录成功', username: user.username, role: user.role, department: user.department });
    } else {
        // 记录登录失败日志
        if (optimizedModules && optimizedModules.logger) {
            optimizedModules.logger.security('User login failed', {
                username: username,
                ip: req.ip,
                userAgent: req.get('User-Agent'),
                reason: 'Invalid credentials'
            });
        }

        res.json({ success: false, message: '账号或密码错误' });
    }
});

// 用户修改密码接口
app.post('/changePassword', (req, res) => {
    const { username, currentPassword, newPassword } = req.body;

    // 添加调试日志
    console.log(`修改密码请求: 用户=${username}`);

    try {
        let users = getUsers();

        // 查找用户并验证当前密码
        let userIndex = users.findIndex(u => u.username === username && u.password === currentPassword);

        // 如果通过用户名找不到，尝试通过用户代码验证
        if (userIndex === -1) {
            userIndex = users.findIndex(u => u.userCode === username && u.password === currentPassword);
        }

        if (userIndex === -1) {
            console.log('密码修改失败: 用户不存在或当前密码错误');

            // 记录失败日志
            if (optimizedModules && optimizedModules.logger) {
                optimizedModules.logger.security('Password change failed - invalid credentials', {
                    username,
                    ip: req.ip,
                    userAgent: req.get('User-Agent')
                });
            }

            return res.json({ success: false, message: '当前密码错误' });
        }

        // 验证新密码长度（至少6位）
        if (!newPassword || newPassword.trim().length < 6) {
            console.log('密码修改失败: 新密码至少需要6位');
            return res.json({ success: false, message: '新密码至少需要6位' });
        }

        // 更新密码
        users[userIndex].password = newPassword;

        // 保存用户数据
        console.log('正在保存用户数据...');
        const saveResult = saveUsers(users);
        if (!saveResult) {
            console.log('保存用户数据失败');
            return res.json({ success: false, message: '保存用户数据失败' });
        }

        // 获取真实用户名（如果使用了用户代码登录）
        const realUsername = users[userIndex].username;
        console.log(`用户 ${realUsername} 的密码已成功修改`);

        // 记录成功日志
        if (optimizedModules && optimizedModules.logger) {
            optimizedModules.logger.security('Password changed successfully', {
                username: realUsername,
                ip: req.ip,
                userAgent: req.get('User-Agent')
            });
        }

        res.json({ success: true, message: '密码修改成功，请使用新密码重新登录' });
    } catch (error) {
        console.error('修改密码时发生错误:', error);

        // 记录错误日志
        if (optimizedModules && optimizedModules.logger) {
            optimizedModules.logger.error('Password change error', {
                username,
                error: error.message,
                ip: req.ip
            });
        }

        res.status(500).json({ success: false, message: '服务器错误: ' + error.message });
    }
});

// 管理员添加用户接口
app.post('/addUser', (req, res) => {
    const { adminUsername, username, password, role, email, department, userCode, signature } = req.body;
    let users = getUsers();

    // 验证管理员权限
    const admin = users.find(u => u.username === adminUsername);
    if (!admin || admin.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足，只有管理员可以添加用户' });
    }

    // 检查用户名是否已存在
    if (users.find(u => u.username === username)) {
        return res.json({ success: false, message: '用户名已存在' });
    }

    // 检查用户代码是否已存在
    if (userCode && users.find(u => u.userCode === userCode)) {
        return res.json({ success: false, message: '用户代码已存在' });
    }

    // 验证用户代码格式
    if (userCode && !/^[A-Za-z0-9]{6}$/.test(userCode)) {
        return res.json({ success: false, message: '用户代码必须是6位字母或数字组合' });
    }

    // 验证密码长度（至少6位）
    if (!password || password.length < 6) {
        return res.json({ success: false, message: '密码至少需要6位' });
    }

    // 添加新用户，确保总监用户部门为"经理室"
    let finalDepartment = department;
    if (role === 'chief') {
        finalDepartment = '经理室';
    }

    // 为新用户生成唯一的10位数字ID
    const userId = generateUniqueUserId();

    const newUser = {
        username,
        password,
        role,
        email,
        department: finalDepartment,
        userCode,
        userId,
        signature: signature || null
    };

    try {
        dataAccess.addUser(newUser);

        // 清除缓存
        cache.users = null;
        cache.lastUserUpdate = 0;

        res.json({ success: true, message: '用户添加成功' });
    } catch (error) {
        console.error('添加用户失败:', error);
        res.json({ success: false, message: '添加用户失败，请重试' });
    }
});

// 获取所有用户信息（仅管理员）- 优化版本，支持搜索和筛选
app.get('/users', (req, res) => {
    const { username, search, role, department, status, page = 1, limit = 10 } = req.query;
    const users = getUsers();

    // 检查权限
    const adminUser = users.find(u => u.username === username);
    if (!adminUser || adminUser.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足' });
    }

    let filteredUsers = [...users];

    // 应用搜索筛选
    if (search) {
        const searchLower = search.toLowerCase();
        filteredUsers = filteredUsers.filter(user => {
            const searchFields = [
                user.username,
                user.userCode,
                user.email,
                user.department
            ].filter(Boolean).join(' ').toLowerCase();
            return searchFields.includes(searchLower);
        });
    }

    // 应用角色筛选
    if (role) {
        filteredUsers = filteredUsers.filter(user => user.role === role);
    }

    // 应用部门筛选
    if (department) {
        filteredUsers = filteredUsers.filter(user => user.department === department);
    }

    // 应用状态筛选
    if (status && status !== 'all') {
        filteredUsers = filteredUsers.filter(user => {
            const isActive = user.email && user.email.trim() !== '';
            return status === 'active' ? isActive : !isActive;
        });
    }

    // 排序
    filteredUsers.sort((a, b) => a.username.localeCompare(b.username));

    // 分页
    const pageNum = parseInt(page);
    const limitNum = parseInt(limit);
    const startIndex = (pageNum - 1) * limitNum;
    const endIndex = startIndex + limitNum;
    const paginatedUsers = filteredUsers.slice(startIndex, endIndex);

    // 返回结果
    res.json({
        success: true,
        data: paginatedUsers,
        pagination: {
            page: pageNum,
            limit: limitNum,
            total: filteredUsers.length,
            totalPages: Math.ceil(filteredUsers.length / limitNum)
        },
        stats: {
            totalUsers: users.length,
            activeUsers: users.filter(u => u.email && u.email.trim() !== '').length,
            adminUsers: users.filter(u => u.role === 'admin').length,
            departments: [...new Set(users.map(u => u.department).filter(Boolean))].length
        }
    });
});

// 获取所有用户的签名数据（所有已登录用户可访问）
app.get('/signatures', (req, res) => {
    const { username } = req.query;
    const users = getUsers();

    // 检查是否是已登录用户
    const requestingUser = users.find(u => u.username === username);
    if (requestingUser) {
        // 只返回用户名、角色、部门和签名数据，不包含密码等敏感信息
        const signatures = users.map(user => {
            // 确保总监用户的部门显示为"经理室"
            let department = user.department;
            if (user.role === 'chief') {
                department = user.department || '经理室';
            }

            return {
                username: user.username,
                role: user.role,
                department: department,
                userCode: user.userCode,
                signature: user.signature
            };
        });
        res.json(signatures);
    } else {
        res.status(403).json({ success: false, message: '请先登录' });
    }
});

// 更新用户角色和邮箱（仅管理员）
app.post('/updateUser', (req, res) => {
    const { adminUsername, targetUsername, newRole, email, department, userCode, signature, newPassword, deleteSignature } = req.body;

    // 添加调试日志
    console.log(`更新用户请求: 管理员=${adminUsername}, 目标用户=${targetUsername}`);
    console.log(`更新数据: 角色=${newRole}, 部门=${department}, 用户代码=${userCode}`);
    console.log(`签名数据长度: ${signature ? signature.length : 0}`);
    console.log(`是否更新密码: ${newPassword ? '是' : '否'}`);
    console.log(`是否删除签名: ${deleteSignature ? '是' : '否'}`);

    try {
        let users = getUsers();

        // 检查权限
        const adminUser = users.find(u => u.username === adminUsername);
        if (!adminUser || adminUser.role !== 'admin') {
            console.log('权限不足: 用户不是管理员');
            return res.status(403).json({ success: false, message: '权限不足' });
        }

        const userIndex = users.findIndex(u => u.username === targetUsername);
        if (userIndex === -1) {
            console.log('用户不存在');
            return res.json({ success: false, message: '用户不存在' });
        }

        // 检查用户代码是否已存在（排除当前用户）
        if (userCode && users.some(u => u.userCode === userCode && u.username !== targetUsername)) {
            console.log('用户代码已存在');
            return res.json({ success: false, message: '用户代码已存在' });
        }

        // 验证用户代码格式
        if (userCode && !/^[A-Za-z0-9]{6}$/.test(userCode)) {
            console.log('用户代码格式无效');
            return res.json({ success: false, message: '用户代码必须是6位字母或数字组合' });
        }

        // 记录原始角色，用于处理历史审批记录
        const originalRole = users[userIndex].role;

        // 更新用户信息，确保总监用户部门为"经理室"
        users[userIndex].role = newRole;
        users[userIndex].email = email;

        // 根据角色设置部门
        if (newRole === 'chief') {
            users[userIndex].department = '经理室';
        } else {
            users[userIndex].department = department;
        }

        users[userIndex].userCode = userCode;

        // 如果提供了新密码，则更新密码
        if (newPassword) {
            users[userIndex].password = newPassword;
            console.log(`用户 ${targetUsername} 的密码已更新`);
        }

        // 处理角色变更时的历史审批记录保留
        if (originalRole !== newRole) {
            console.log(`用户 ${targetUsername} 角色从 ${originalRole} 变更为 ${newRole}，开始处理历史审批记录`);
            handleRoleChangeApprovalHistory(targetUsername, originalRole, newRole);
        }

        // 处理电子签名
        if (deleteSignature) {
            // 删除电子签名
            console.log(`删除用户 ${targetUsername} 的电子签名，删除前signature存在: ${!!users[userIndex].signature}`);
            delete users[userIndex].signature;
            console.log(`删除后signature存在: ${!!users[userIndex].signature}`);
            console.log('电子签名已删除');
        } else if (signature) {
            // 更新电子签名
            try {
                // 验证签名数据格式
                if (typeof signature !== 'string') {
                    console.log('签名数据类型无效:', typeof signature);
                    return res.json({ success: false, message: '签名数据类型无效' });
                }

                if (!signature.startsWith('data:image/')) {
                    console.log('签名数据格式无效: 不是图片数据');
                    return res.json({ success: false, message: '签名数据格式无效，请使用图片文件' });
                }

                // 检查签名数据大小
                const dataSizeInKB = Math.round(signature.length / 1024);
                console.log(`签名数据大小: ${dataSizeInKB}KB`);

                if (signature.length > 5000000) { // 如果大于5MB
                    console.log('签名数据过大:', signature.length, '字节');
                    return res.json({ success: false, message: '签名图片过大，请使用小于5MB的图片' });
                }

                // 更新签名
                users[userIndex].signature = signature;
                console.log('签名数据已更新');
            } catch (signatureError) {
                console.error('处理签名数据时出错:', signatureError);
                return res.json({ success: false, message: '处理签名数据失败: ' + signatureError.message });
            }
        }

        // 保存用户数据到SQLite
        console.log('正在保存用户数据...');
        try {
            dataAccess.updateUser(targetUsername, users[userIndex]);

            // 清除缓存
            cache.users = null;
            cache.lastUserUpdate = 0;

            console.log('用户更新成功');
            res.json({ success: true, message: '用户更新成功' });
        } catch (error) {
            console.error('保存用户数据失败:', error);
            return res.json({ success: false, message: '保存用户数据失败' });
        }
    } catch (error) {
        console.error('更新用户时发生错误:', error);
        res.status(500).json({ success: false, message: '服务器错误: ' + error.message });
    }
});

// 删除用户接口（仅管理员）
app.post('/deleteUser', (req, res) => {
    const { adminUsername, targetUsername } = req.body;
    let users = getUsers();

    // 检查权限
    const adminUser = users.find(u => u.username === adminUsername);
    if (!adminUser || adminUser.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足' });
    }

    // 不允许删除自己
    if (adminUsername === targetUsername) {
        return res.json({ success: false, message: '不能删除当前登录的用户' });
    }

    // 查找用户
    const userIndex = users.findIndex(u => u.username === targetUsername);
    if (userIndex === -1) {
        return res.json({ success: false, message: '用户不存在' });
    }

    // 删除用户
    try {
        dataAccess.deleteUser(targetUsername);

        // 清除缓存
        cache.users = null;
        cache.lastUserUpdate = 0;

        res.json({ success: true, message: '用户删除成功' });
    } catch (error) {
        console.error('删除用户失败:', error);
        res.json({ success: false, message: '删除用户失败，请重试' });
    }
});

// ==================== 部门管理API ====================

// 获取所有部门
app.get('/departments', (req, res) => {
    try {
        const departments = getDepartments();

        // 计算每个部门的用户数量
        const users = getUsers();
        const departmentsWithUserCount = departments.map(dept => {
            const userCount = users.filter(user => user.department === dept.name).length;
            return {
                ...dept,
                userCount
            };
        });

        res.json({
            success: true,
            data: departmentsWithUserCount
        });
    } catch (error) {
        console.error('获取部门列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取部门列表失败'
        });
    }
});

// 添加部门
app.post('/departments', (req, res) => {
    const { adminUsername, name, code, description } = req.body;

    try {
        // 验证管理员权限
        const users = getUsers();
        const admin = users.find(u => u.username === adminUsername);
        if (!admin || admin.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '权限不足，只有管理员可以添加部门'
            });
        }

        // 验证部门名称
        if (!name || name.trim() === '') {
            return res.json({
                success: false,
                message: '部门名称不能为空'
            });
        }

        const departments = getDepartments();

        // 检查部门名称是否已存在
        const trimmedName = name.trim();
        const existingDepartment = departments.find(d => d.name === trimmedName);

        if (existingDepartment) {
            return res.json({
                success: false,
                message: '部门名称已存在'
            });
        }

        // 检查部门代码是否已存在（如果提供了代码）
        if (code && code.trim() !== '' && departments.find(d => d.code === code.trim())) {
            return res.json({
                success: false,
                message: '部门代码已存在'
            });
        }

        // 创建新部门
        const newDepartment = {
            id: generateDepartmentId(),
            name: name.trim(),
            code: code ? code.trim() : null,
            description: description ? description.trim() : null,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };

        try {
            dataAccess.addDepartment(newDepartment);
            res.json({
                success: true,
                message: '部门添加成功',
                data: newDepartment
            });
        } catch (error) {
            console.error('添加部门失败:', error);
            res.json({
                success: false,
                message: '保存部门数据失败'
            });
        }
    } catch (error) {
        console.error('添加部门失败:', error);
        res.status(500).json({
            success: false,
            message: '添加部门失败: ' + error.message
        });
    }
});

// 更新部门
app.put('/departments/:id', (req, res) => {
    const { id } = req.params;
    const { adminUsername, name, code, description } = req.body;

    try {
        // 验证管理员权限
        const users = getUsers();
        const admin = users.find(u => u.username === adminUsername);
        if (!admin || admin.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '权限不足，只有管理员可以修改部门'
            });
        }

        // 验证部门名称
        if (!name || name.trim() === '') {
            return res.json({
                success: false,
                message: '部门名称不能为空'
            });
        }

        const departments = getDepartments();
        const departmentIndex = departments.findIndex(d => d.id === id);

        if (departmentIndex === -1) {
            return res.json({
                success: false,
                message: '部门不存在'
            });
        }

        // 检查部门名称是否已存在（排除当前部门）
        if (departments.find(d => d.name === name.trim() && d.id !== id)) {
            return res.json({
                success: false,
                message: '部门名称已存在'
            });
        }

        // 检查部门代码是否已存在（排除当前部门）
        if (code && code.trim() !== '' && departments.find(d => d.code === code.trim() && d.id !== id)) {
            return res.json({
                success: false,
                message: '部门代码已存在'
            });
        }

        // 获取原部门名称，用于更新用户数据
        const oldDepartmentName = departments[departmentIndex].name;

        // 更新部门信息
        departments[departmentIndex] = {
            ...departments[departmentIndex],
            name: name.trim(),
            code: code ? code.trim() : null,
            description: description ? description.trim() : null,
            updatedAt: new Date().toISOString()
        };

        try {
            dataAccess.updateDepartment(id, departments[departmentIndex]);

            // 如果部门名称发生变化，需要更新所有用户的部门信息
            if (oldDepartmentName !== name.trim()) {
                const allUsers = getUsers();
                let usersUpdated = false;

                allUsers.forEach(user => {
                    if (user.department === oldDepartmentName) {
                        user.department = name.trim();
                        dataAccess.updateUser(user.username, user);
                        usersUpdated = true;
                    }
                });

                if (usersUpdated) {
                    // 清除用户缓存
                    cache.users = null;
                    cache.lastUserUpdate = 0;
                    console.log(`已更新用户的部门信息: ${oldDepartmentName} -> ${name.trim()}`);
                }
            }

            res.json({
                success: true,
                message: '部门更新成功',
                data: departments[departmentIndex]
            });
        } catch (error) {
            console.error('更新部门失败:', error);
            res.json({
                success: false,
                message: '保存部门数据失败'
            });
        }
    } catch (error) {
        console.error('更新部门失败:', error);
        res.status(500).json({
            success: false,
            message: '更新部门失败: ' + error.message
        });
    }
});

// 删除部门
app.delete('/departments/:id', (req, res) => {
    const { id } = req.params;
    const { adminUsername } = req.body;

    try {
        // 验证管理员权限
        const users = getUsers();
        const admin = users.find(u => u.username === adminUsername);
        if (!admin || admin.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '权限不足，只有管理员可以删除部门'
            });
        }

        const departments = getDepartments();
        const departmentIndex = departments.findIndex(d => d.id === id);

        if (departmentIndex === -1) {
            return res.json({
                success: false,
                message: '部门不存在'
            });
        }

        const departmentToDelete = departments[departmentIndex];

        // 检查是否有用户属于该部门
        const usersInDepartment = users.filter(user => user.department === departmentToDelete.name);
        if (usersInDepartment.length > 0) {
            return res.json({
                success: false,
                message: `无法删除部门，还有 ${usersInDepartment.length} 个用户属于该部门`
            });
        }

        // 删除部门
        try {
            dataAccess.deleteDepartment(id);
            res.json({
                success: true,
                message: '部门删除成功'
            });
        } catch (error) {
            console.error('删除部门失败:', error);
            res.json({
                success: false,
                message: '删除部门失败'
            });
        }
    } catch (error) {
        console.error('删除部门失败:', error);
        res.status(500).json({
            success: false,
            message: '删除部门失败: ' + error.message
        });
    }
});

// 文件上传接口
app.post('/upload', upload.array('attachments'), (req, res) => {
    try {
        const files = req.files.map(file => ({
            name: Buffer.from(file.originalname, 'latin1').toString('utf8'),
            path: file.filename
        }));
        res.json({ success: true, files });
    } catch (error) {
        res.status(500).json({ success: false, message: error.message });
    }
});

// 获取美元兑人民币汇率
async function getUSDtoCNYRate() {
    return new Promise((resolve, reject) => {
        // 由于实际环境中可能需要付费API，这里使用固定汇率作为示例
        // 实际应用中可以替换为真实的汇率API
        // 例如: https://api.exchangerate-api.com/v4/latest/USD

        // 模拟API调用延迟
        setTimeout(() => {
            // 使用固定汇率 1 USD = 7.2 CNY 作为示例
            // 实际应用中应该从API获取实时汇率
            const rate = 7.2;
            resolve(rate);
        }, 100);

        // 如果使用真实API，可以使用以下代码
        /*
        https.get('https://api.exchangerate-api.com/v4/latest/USD', (resp) => {
            let data = '';
            resp.on('data', (chunk) => {
                data += chunk;
            });
            resp.on('end', () => {
                try {
                    const response = JSON.parse(data);
                    const rate = response.rates.CNY;
                    resolve(rate);
                } catch (error) {
                    console.error('Error parsing exchange rate data:', error);
                    // 如果API调用失败，使用默认汇率
                    resolve(7.2);
                }
            });
        }).on('error', (error) => {
            console.error('Error fetching exchange rate:', error);
            // 如果API调用失败，使用默认汇率
            resolve(7.2);
        });
        */
    });
}

// 提交申请接口
app.post('/submit', async (req, res) => {
    const application = req.body;
    let applications = getApplications();
    application.id = Date.now();

    // 确保币种字段存在，默认为CNY
    if (!application.currency) {
        application.currency = 'CNY';
    }

    // 如果是美元，获取汇率并计算人民币等值
    if (application.currency === 'USD' && application.amount) {
        try {
            const rate = await getUSDtoCNYRate();
            application.exchangeRate = rate;
            application.cnyEquivalent = parseFloat(application.amount) * rate;
            console.log(`美元金额: ${application.amount}, 汇率: ${rate}, 人民币等值: ${application.cnyEquivalent}`);
        } catch (error) {
            console.error('获取汇率失败:', error);
            // 使用默认汇率
            application.exchangeRate = 7.2;
            application.cnyEquivalent = parseFloat(application.amount) * 7.2;
        }
    }

    // 处理选中的厂长/总监
    const selectedDirectors = application.selectedDirectors || [];
    delete application.selectedDirectors;

    // 初始化审批结构
    application.approvals = {
        directors: {}, // 多个厂长审批
        chief: { status: 'pending', attachments: [] },
        managers: {} // 多个经理审批
    };

    // 初始化提醒相关字段
    application.reminderInfo = {
        lastReminderTime: null, // 上次提醒时间
        reminderCount: 0, // 已发送提醒次数
        escalationLevel: 'normal' // 提醒级别：normal, medium, urgent
    };

    // 生成唯一编号：年月日+序号（在发送邮件之前生成）
    const today = new Date();
    const year = today.getFullYear();
    const month = String(today.getMonth() + 1).padStart(2, '0');
    const day = String(today.getDate()).padStart(2, '0');
    const datePrefix = `${year}${month}${day}`;

    // 查找今天已有的申请数量，确定序号
    const todayApplications = applications.filter(app => {
        if (!app.applicationCode) return false;
        return app.applicationCode.startsWith(datePrefix);
    });

    // 序号从001开始，根据今天已有的申请数量递增
    const serialNumber = String(todayApplications.length + 1).padStart(3, '0');
    application.applicationCode = `${datePrefix}${serialNumber}`;

    // 检查是否只选择了总监用户
    const users = getUsers();
    let directorsList = [];

    // 安全解析selectedDirectors，处理不同的数据格式
    if (selectedDirectors) {
        try {
            if (Array.isArray(selectedDirectors)) {
                // 如果已经是数组，直接使用
                directorsList = selectedDirectors;
            } else if (typeof selectedDirectors === 'string') {
                // 如果是字符串，尝试解析为JSON
                if (selectedDirectors.startsWith('[') && selectedDirectors.endsWith(']')) {
                    // 看起来像JSON数组字符串
                    directorsList = JSON.parse(selectedDirectors);
                } else {
                    // 单个字符串值，转换为数组
                    directorsList = [selectedDirectors];
                }
            }
        } catch (parseError) {
            console.error('解析selectedDirectors失败:', parseError, '原始值:', selectedDirectors);
            // 如果解析失败，尝试将其作为单个值处理
            directorsList = typeof selectedDirectors === 'string' ? [selectedDirectors] : [];
        }
    }
    const selectedOnlyChiefs = directorsList.length > 0 &&
        directorsList.every(director => {
            const user = users.find(u => u.username === director);
            return user && user.role === 'chief';
        });

    // 初始化审批层级时间戳
    application.stageTimestamps = {};

    // 如果只选择了总监，直接设置状态为"待总监审批"，否则为"待厂长审核"
    if (selectedOnlyChiefs) {
        application.status = '待总监审批';
        application.stageTimestamps.chiefStageStartTime = new Date().toISOString();

        // 只添加总监到通知列表
        directorsList.forEach(director => {
            // 总监不需要添加到directors审批列表中
            const user = users.find(u => u.username === director);
            if (user && user.role === 'chief') {
                // 不添加到directors对象中，因为这是给厂长用的
            }
        });
    } else {
        application.status = '待厂长审核';
        application.stageTimestamps.directorStageStartTime = new Date().toISOString();

        // 设置选中的厂长/总监
        let directorsToNotify = [];
        directorsList.forEach(director => {
            const user = users.find(u => u.username === director);
            // 只有角色为director的用户才添加到directors审批对象中
            if (user && user.role === 'director') {
                application.approvals.directors[director] = { status: 'pending', attachments: [] };
                directorsToNotify.push(director);
            }
        });

        // 发送邮件通知厂长
        const directorEmails = getUserEmails(directorsToNotify);
        if (directorEmails.length > 0) {
            const emailSubject = `新申请待审核 - ${application.applicant}`;
            const emailContent = generateEmailTemplate({
                title: '您有一份新的申请需要审批',
                applicant: application.applicant,
                applicationCode: application.applicationCode,
                department: application.department,
                date: application.date,
                content: application.content,
                priority: application.priority,
                status: getEmailStatusDescription(application.status, application),
                actionText: '立即审批',
                actionUrl: SERVER_CONFIG.url
            });
            sendEmailNotification(directorEmails, emailSubject, emailContent, application.applicationCode)
                .then(success => {
                    logWithTime('厂长邮件通知状态:', success ? '成功' : '失败');
                });
        }
    }

    // 如果是待总监审批状态，发送邮件通知总监
    if (application.status === '待总监审批') {
        // 获取所有选中的总监的邮箱
        const chiefEmails = getUserEmails(directorsList.filter(director => {
            const user = users.find(u => u.username === director);
            return user && user.role === 'chief';
        }));

        if (chiefEmails.length > 0) {
            const emailSubject = `新申请待审核 - ${application.applicant}`;
            const emailContent = generateEmailTemplate({
                title: '您有一份新的申请需要审批',
                applicant: application.applicant,
                applicationCode: application.applicationCode,
                department: application.department,
                date: application.date,
                content: application.content,
                priority: application.priority,
                status: getEmailStatusDescription(application.status, application),
                actionText: '立即审批',
                actionUrl: SERVER_CONFIG.url
            });
            sendEmailNotification(chiefEmails, emailSubject, emailContent, application.applicationCode)
                .then(success => {
                    logWithTime('总监邮件通知状态:', success ? '成功' : '失败');
                });
        }
    }

    // 使用新的保存方式：保存单个申请文件
    if (saveApplication(application)) {
        // 清除缓存以确保下次读取时获取最新数据
        cache.applications = null;
        cache.lastApplicationUpdate = 0;

        res.json({ success: true, message: '申请提交成功', application: application });
    } else {
        res.status(500).json({ success: false, message: '保存申请数据失败，请稍后重试' });
    }
});

// 修改申请接口
app.post('/modify', upload.array('attachments'), async (req, res) => {
    const { id, username, applicant, department, date, content, amount, currency, priority, selectedDirectors, role } = req.body;
    let applications = getApplications();
    const appIndex = applications.findIndex(a => a.id === parseInt(id));
    if (appIndex === -1) {
        return res.json({ success: false, message: '申请不存在' });
    }
    const app = applications[appIndex];

    // 检查权限：只有申请人自己或管理员可以修改
    // 只读角色不能修改申请
    const isAdmin = role === 'admin';
    const isReadOnly = role === 'readonly';
    const isApplicant = app.username === username;

    // 检查申请是否已经最终审核完成
    const isFinalStatus = app.status === '已通过' || app.status === '已拒绝';

    // 检查是否没有初始附件
    const hasNoInitialAttachments = !app.attachments || app.attachments.length === 0;

    // 申请人可以修改的条件：
    // 1. 申请处于待厂长审核状态，或
    // 2. 申请流程进行中（未最终审核完成）且一开始没有添加初始附件
    const canApplicantModify = isApplicant &&
        (app.status === '待厂长审核' || (hasNoInitialAttachments && !isFinalStatus));

    if (isReadOnly || (!isAdmin && !canApplicantModify)) {
        return res.json({ success: false, message: '无权修改此申请或申请已进入审批流程' });
    }

    // 更新基本信息
    app.applicant = applicant;
    app.department = department;
    app.date = date;
    app.content = content;
    app.amount = amount || app.amount;
    app.currency = currency || app.currency || 'CNY';
    app.priority = priority;

    // 确保提醒信息字段存在（兼容旧数据）
    if (!app.reminderInfo) {
        app.reminderInfo = {
            lastReminderTime: null,
            reminderCount: 0,
            escalationLevel: 'normal'
        };
    }

    // 如果是美元，获取汇率并计算人民币等值
    if (app.currency === 'USD' && app.amount) {
        try {
            const rate = await getUSDtoCNYRate();
            app.exchangeRate = rate;
            app.cnyEquivalent = parseFloat(app.amount) * rate;
            console.log(`美元金额: ${app.amount}, 汇率: ${rate}, 人民币等值: ${app.cnyEquivalent}`);
        } catch (error) {
            console.error('获取汇率失败:', error);
            // 使用默认汇率
            app.exchangeRate = 7.2;
            app.cnyEquivalent = parseFloat(app.amount) * 7.2;
        }
    }

    // 处理选中的厂长/总监
    if (selectedDirectors) {
        let directorsList = [];

        // 安全解析selectedDirectors，处理不同的数据格式
        try {
            if (Array.isArray(selectedDirectors)) {
                // 如果已经是数组，直接使用
                directorsList = selectedDirectors;
            } else if (typeof selectedDirectors === 'string') {
                // 如果是字符串，尝试解析为JSON
                if (selectedDirectors.startsWith('[') && selectedDirectors.endsWith(']')) {
                    // 看起来像JSON数组字符串
                    directorsList = JSON.parse(selectedDirectors);
                } else {
                    // 单个字符串值，转换为数组
                    directorsList = [selectedDirectors];
                }
            }
        } catch (parseError) {
            console.error('解析selectedDirectors失败:', parseError, '原始值:', selectedDirectors);
            // 如果解析失败，尝试将其作为单个值处理
            directorsList = typeof selectedDirectors === 'string' ? [selectedDirectors] : [];
        }

        // 获取用户数据以检查角色
        const users = getUsers();

        // 检查是否只选择了总监用户
        const selectedOnlyChiefs = directorsList.length > 0 &&
            directorsList.every(director => {
                const user = users.find(u => u.username === director);
                return user && user.role === 'chief';
            });

        // 如果只选择了总监，直接设置状态为"待总监审批"
        if (selectedOnlyChiefs) {
            app.status = '待总监审批';
            // 重置厂长审批状态，因为不需要厂长审批了
            app.approvals.directors = {};

            // 重置并记录总监审批阶段开始时间
            if (!app.stageTimestamps) {
                app.stageTimestamps = {};
            }
            app.stageTimestamps.chiefStageStartTime = new Date().toISOString();

            // 发送邮件通知总监
            const chiefEmails = getUserEmails(directorsList.filter(director => {
                const user = users.find(u => u.username === director);
                return user && user.role === 'chief';
            }));

            if (chiefEmails.length > 0) {
                const emailSubject = `修改后的申请待审核 - ${app.applicant}`;
                const emailContent = generateEmailTemplate({
                    title: '您有一份修改后的申请需要审批',
                    applicant: app.applicant,
                    applicationCode: app.applicationCode || '无编号',
                    department: app.department,
                    date: app.date,
                    content: app.content,
                    priority: app.priority,
                    status: getEmailStatusDescription(app.status, app),
                    actionText: '立即审批',
                    actionUrl: SERVER_CONFIG.url,
                    additionalInfo: '此申请已被申请人修改，请重新审核。'
                });
                sendEmailNotification(chiefEmails, emailSubject, emailContent, app.applicationCode)
                    .then(success => {
                        console.log('总监邮件通知状态:', success ? '成功' : '失败');
                    });
            }
        } else {
            // 重置厂长审批状态
            app.approvals.directors = {};

            // 设置申请状态为待厂长审核
            app.status = '待厂长审核';

            // 重置并记录厂长审批阶段开始时间
            if (!app.stageTimestamps) {
                app.stageTimestamps = {};
            }
            app.stageTimestamps.directorStageStartTime = new Date().toISOString();

            // 获取所有厂长用户名
            let directorsToNotify = [];

            // 只添加角色为director的用户到厂长审批列表
            directorsList.forEach(director => {
                const user = users.find(u => u.username === director);
                if (user && user.role === 'director') {
                    app.approvals.directors[director] = { status: 'pending', attachments: [] };
                    directorsToNotify.push(director);
                }
            });

            // 发送邮件通知厂长
            const directorEmails = getUserEmails(directorsToNotify);
            if (directorEmails.length > 0) {
                const emailSubject = `修改后的申请待审核 - ${app.applicant}`;
                const emailContent = generateEmailTemplate({
                    title: '您有一份修改后的申请需要审批',
                    applicant: app.applicant,
                    applicationCode: app.applicationCode || '无编号',
                    department: app.department,
                    date: app.date,
                    content: app.content,
                    priority: app.priority,
                    status: getEmailStatusDescription(app.status, app),
                    actionText: '立即审批',
                    actionUrl: SERVER_CONFIG.url,
                    additionalInfo: '此申请已被申请人修改，请重新审核。'
                });
                sendEmailNotification(directorEmails, emailSubject, emailContent, app.applicationCode)
                    .then(success => {
                        console.log('厂长邮件通知状态:', success ? '成功' : '失败');
                    });
            }
        }
    }

    // 处理新上传的附件
    if (req.files && req.files.length > 0) {
        const newAttachments = req.files.map(file => ({
            name: Buffer.from(file.originalname, 'latin1').toString('utf8'),
            path: file.filename
        }));
        app.attachments = newAttachments;
    }

    // 使用新的保存方式：保存单个申请文件
    if (saveApplication(app)) {
        // 清除缓存以确保下次读取时获取最新数据
        cache.applications = null;
        cache.lastApplicationUpdate = 0;

        res.json({ success: true, message: '申请修改成功', application: app });
    } else {
        res.status(500).json({ success: false, message: '保存申请数据失败，请稍后重试' });
    }
});

// 获取申请列表
app.get('/applications', (req, res) => {
    const applications = getApplications();
    res.json(applications);
});

// 删除申请接口
app.post('/deleteApplication', (req, res) => {
    const { id, username, role } = req.body;
    let applications = getApplications();
    const appIndex = applications.findIndex(a => a.id === parseInt(id));

    if (appIndex === -1) {
        return res.json({ success: false, message: '申请不存在' });
    }

    const app = applications[appIndex];

    // 检查权限：只有申请人自己或管理员可以删除，且只能删除待厂长审核的申请（管理员可以删除任何状态）
    // 只读角色不能删除申请
    const isAdmin = role === 'admin';
    const isReadOnly = role === 'readonly';
    if (isReadOnly || (app.username !== username && !isAdmin) || (!isAdmin && app.status !== '待厂长审核')) {
        return res.json({ success: false, message: '无权删除此申请或申请已进入审批流程' });
    }

    // 删除附件
    if (app.attachments && app.attachments.length > 0) {
        app.attachments.forEach(attachment => {
            const filePath = path.join(__dirname, '..', 'uploads', attachment.path);
            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }
        });
    }

    // 删除申请文件
    if (deleteApplicationFile(app)) {
        // 清除缓存以确保下次读取时获取最新数据
        cache.applications = null;
        cache.lastApplicationUpdate = 0;

        res.json({ success: true, message: '申请删除成功' });
    } else {
        res.status(500).json({ success: false, message: '删除申请文件失败，请稍后重试' });
    }
});

// 审批申请接口（含归档逻辑）
app.post('/approve', upload.array('newAttachments'), async (req, res) => {
    const { id, role, status, username, selectedManagers, comment, signature } = req.body;
    let applications = getApplications();
    const appIndex = applications.findIndex(a => a.id === parseInt(id));
    if (appIndex === -1) {
        return res.json({ success: false, message: '申请不存在' });
    }
    const app = applications[appIndex];

    // 处理附件
    const newAttachments = req.files && req.files.length > 0 ?
        req.files.map(file => ({
            name: Buffer.from(file.originalname, 'latin1').toString('utf8'),
            path: file.filename
        })) : [];

    console.log(`收到审批请求: ID=${id}, 角色=${role}, 状态=${status}, 用户=${username}`);
    console.log(`新上传附件数量: ${newAttachments.length}`);

    // 管理员和只读角色不能进行任何审批操作
    if (role === 'admin' || role === 'readonly') {
        return res.status(403).json({ success: false, message: '您无权进行审批操作' });
    }

    // 根据角色处理不同的审批逻辑
    if (role === 'director') {
        // 厂长审批
        if (!app.approvals.directors[username] || app.approvals.directors[username].status !== 'pending') {
            return res.json({ success: false, message: '您不是该申请的审批人或已完成审批' });
        }

        // 确保附件数组存在
        if (!app.approvals.directors[username].attachments) {
            app.approvals.directors[username].attachments = [];
        }

        // 更新当前厂长的审批状态和附件
        app.approvals.directors[username].status = status;
        if (newAttachments.length > 0) {
            app.approvals.directors[username].attachments = newAttachments;
        }
        app.approvals.directors[username].comment = comment || '';
        app.approvals.directors[username].date = new Date().toISOString();
        app.approvals.directors[username].signature = signature || '';

        console.log(`厂长 ${username} 审批完成，状态: ${status}, 附件数: ${app.approvals.directors[username].attachments.length}`);

        if (status === 'approved') {
            // 检查是否所有厂长都已审批通过
            const directorsEntries = Object.entries(app.approvals.directors);

            console.log(`申请ID=${id} 审批状态检查: 共有${directorsEntries.length}个厂长需要审批`);

            // 输出每个厂长的审批状态
            directorsEntries.forEach(([dirName, dirStatus]) => {
                console.log(`- 厂长 ${dirName} 的审批状态: ${dirStatus.status}`);
            });

            const allDirectorsApproved = directorsEntries.length > 0 &&
                directorsEntries.every(([_, d]) => d.status === 'approved');

            console.log(`申请ID=${id} 是否所有厂长都已审批通过: ${allDirectorsApproved}`);

            if (allDirectorsApproved) {
                // 所有厂长都通过，流转到总监
                console.log(`申请ID=${id} 从"${app.status}"状态更改为"待总监审批"`);
                app.status = '待总监审批';

                // 重置提醒信息，因为进入了新的审批阶段
                if (app.reminderInfo) {
                    app.reminderInfo.lastReminderTime = null;
                    app.reminderInfo.reminderCount = 0;
                    app.reminderInfo.escalationLevel = 'normal';
                }

                // 记录当前审批层级的开始时间
                if (!app.stageTimestamps) {
                    app.stageTimestamps = {};
                }
                app.stageTimestamps.chiefStageStartTime = new Date().toISOString();

                // 发送邮件通知总监
                const users = getUsers();
                const chiefs = users.filter(user => user.role === 'chief').map(user => user.username);
                const chiefEmails = getUserEmails(chiefs);

                console.log(`发现${chiefs.length}个总监，邮箱地址数量: ${chiefEmails.length}`);

                if (chiefEmails.length > 0) {
                    const emailSubject = `新申请待审核 - ${app.applicant}`;
                    const emailContent = generateEmailTemplate({
                        title: '您有一份申请需要审批',
                        applicant: app.applicant,
                        applicationCode: app.applicationCode || '无编号',
                        department: app.department,
                        date: app.date,
                        content: app.content,
                        priority: app.priority,
                        status: getEmailStatusDescription(app.status, app),
                        actionText: '立即审批',
                        actionUrl: SERVER_CONFIG.url,
                        additionalInfo: '此申请已通过厂长审核，现需要您的审批。'
                    });
                    sendEmailNotification(chiefEmails, emailSubject, emailContent, app.applicationCode)
                        .then(success => {
                            console.log('总监邮件通知状态:', success ? '成功' : '失败');
                        });
                }
            } else {
                console.log(`申请ID=${id} 尚有厂长未通过审批，保持当前状态: ${app.status}`);
            }
        } else if (status === 'rejected') {
            // 任一厂长拒绝，整个申请被拒绝
            app.status = '已拒绝';

            // 发送邮件通知申请人
            const users = getUsers();
            // 优先使用申请人的用户名查找，如果找不到则尝试使用申请人姓名查找
            let applicantUser = users.find(user => user.username === app.username);
            if (!applicantUser) {
                applicantUser = users.find(user => user.username === app.applicant);
            }

            if (applicantUser && applicantUser.email) {
                const emailSubject = `您的申请已被驳回 - ${app.applicationCode || ''}`;
                const emailContent = generateEmailTemplate({
                    title: '您的申请已被驳回',
                    applicant: app.applicant,
                    applicationCode: app.applicationCode || '无编号',
                    department: app.department,
                    date: app.date,
                    content: app.content,
                    priority: app.priority,
                    status: '已驳回',
                    actionText: '查看详情',
                    actionUrl: SERVER_CONFIG.url,
                    additionalInfo: `<strong>驳回原因:</strong> ${comment || '无'}<br><strong>驳回人:</strong> 厂长 ${username}`
                });
                sendEmailNotification(applicantUser.email, emailSubject, emailContent, app.applicationCode)
                    .then(success => {
                        console.log('申请人驳回通知邮件状态:', success ? '成功' : '失败');
                    });
            } else {
                console.log(`无法发送邮件通知申请人：找不到申请人 ${app.username} 的邮箱信息`);
            }
        }
    } else if (role === 'chief') {
        // 总监审批
        // 确保附件数组存在
        if (!app.approvals.chief.attachments) {
            app.approvals.chief.attachments = [];
        }

        app.approvals.chief.status = status;
        if (newAttachments.length > 0) {
            app.approvals.chief.attachments = newAttachments;
        }
        app.approvals.chief.comment = comment || '';
        app.approvals.chief.date = new Date().toISOString();
        app.approvals.chief.signature = signature || '';
        app.approvals.chief.approverUsername = username; // 记录审批人用户名，便于个人效率计算

        console.log(`总监审批完成，状态: ${status}, 附件数: ${app.approvals.chief.attachments.length}`);

        if (status === 'approved') {
            // 处理选中的经理
            let managersToNotify = [];

            if (selectedManagers && selectedManagers.length > 0) {
                let managersList = [];

                // 安全解析selectedManagers，处理不同的数据格式
                try {
                    if (Array.isArray(selectedManagers)) {
                        // 如果已经是数组，直接使用
                        managersList = selectedManagers;
                    } else if (typeof selectedManagers === 'string') {
                        // 如果是字符串，尝试解析为JSON
                        if (selectedManagers.startsWith('[') && selectedManagers.endsWith(']')) {
                            // 看起来像JSON数组字符串
                            managersList = JSON.parse(selectedManagers);
                        } else {
                            // 单个字符串值，转换为数组
                            managersList = [selectedManagers];
                        }
                    }
                } catch (parseError) {
                    console.error('解析selectedManagers失败:', parseError, '原始值:', selectedManagers);
                    // 如果解析失败，尝试将其作为单个值处理
                    managersList = typeof selectedManagers === 'string' ? [selectedManagers] : [];
                }
                if (managersList.length > 0) {
                    managersList.forEach(manager => {
                        app.approvals.managers[manager] = { status: 'pending', attachments: [] };
                    });
                    managersToNotify = managersList;
                    app.status = '待经理审批';
                } else {
                    // 如果解析后的数组为空，流转到CEO审批
                    app.status = '待CEO审批';

                    // 初始化CEO审批结构
                    if (!app.approvals.ceo) {
                        app.approvals.ceo = { status: 'pending', attachments: [] };
                    }
                }

                // 重置提醒信息，因为进入了新的审批阶段
                if (app.reminderInfo) {
                    app.reminderInfo.lastReminderTime = null;
                    app.reminderInfo.reminderCount = 0;
                    app.reminderInfo.escalationLevel = 'normal';
                }

                // 记录当前审批层级的开始时间
                if (!app.stageTimestamps) {
                    app.stageTimestamps = {};
                }
                if (app.status === '待经理审批') {
                    app.stageTimestamps.managerStageStartTime = new Date().toISOString();
                } else if (app.status === '待CEO审批') {
                    app.stageTimestamps.ceoStageStartTime = new Date().toISOString();
                }

                // 发送邮件通知经理或CEO
                if (managersToNotify.length > 0) {
                    // 发送邮件通知经理
                    const managerEmails = getUserEmails(managersToNotify);
                    if (managerEmails.length > 0) {
                        const emailSubject = `新申请待审核 - ${app.applicant}`;
                        const emailContent = generateEmailTemplate({
                            title: '您有一份申请需要审批',
                            applicant: app.applicant,
                            applicationCode: app.applicationCode || '无编号',
                            department: app.department,
                            date: app.date,
                            content: app.content,
                            priority: app.priority,
                            status: getEmailStatusDescription(app.status, app),
                            actionText: '立即审批',
                            actionUrl: SERVER_CONFIG.url,
                            additionalInfo: '此申请已通过厂长和总监审核，现需要您的审批。'
                        });
                        sendEmailNotification(managerEmails, emailSubject, emailContent, app.applicationCode)
                            .then(success => {
                                console.log('经理邮件通知状态:', success ? '成功' : '失败');
                            });
                    }
                } else if (app.status === '待CEO审批') {
                    // 发送邮件通知CEO
                    const users = getUsers();
                    const ceoUsers = users.filter(user => user.role === 'ceo');
                    const ceoEmails = ceoUsers.map(user => user.email).filter(email => email);

                    if (ceoEmails.length > 0) {
                        const emailSubject = `新申请待审核 - ${app.applicant}`;
                        const emailContent = generateEmailTemplate({
                            title: '您有一份申请需要审批',
                            applicant: app.applicant,
                            applicationCode: app.applicationCode || '无编号',
                            department: app.department,
                            date: app.date,
                            content: app.content,
                            priority: app.priority,
                            status: getEmailStatusDescription(app.status, app),
                            actionText: '立即审批',
                            actionUrl: SERVER_CONFIG.url,
                            additionalInfo: '此申请已通过厂长和总监审核，现需要您的审批。'
                        });
                        sendEmailNotification(ceoEmails, emailSubject, emailContent, app.applicationCode)
                            .then(success => {
                                console.log('CEO邮件通知状态:', success ? '成功' : '失败');
                            });
                    }

                    console.log('总监审批通过，未选择经理，申请流转到CEO审批');
                }
            } else {
                // 如果没有选择经理，申请流转到CEO审批
                app.status = '待CEO审批';

                // 初始化CEO审批结构
                if (!app.approvals.ceo) {
                    app.approvals.ceo = { status: 'pending', attachments: [] };
                }

                // 重置提醒信息，因为进入了新的审批阶段
                if (app.reminderInfo) {
                    app.reminderInfo.lastReminderTime = null;
                    app.reminderInfo.reminderCount = 0;
                    app.reminderInfo.escalationLevel = 'normal';
                }

                // 记录当前审批层级的开始时间
                if (!app.stageTimestamps) {
                    app.stageTimestamps = {};
                }
                app.stageTimestamps.ceoStageStartTime = new Date().toISOString();

                // 发送邮件通知CEO
                const users = getUsers();
                const ceoUsers = users.filter(user => user.role === 'ceo');
                const ceoEmails = ceoUsers.map(user => user.email).filter(email => email);

                if (ceoEmails.length > 0) {
                    const emailSubject = `新申请待审核 - ${app.applicant}`;
                    const emailContent = generateEmailTemplate({
                        title: '您有一份申请需要审批',
                        applicant: app.applicant,
                        applicationCode: app.applicationCode || '无编号',
                        department: app.department,
                        date: app.date,
                        content: app.content,
                        priority: app.priority,
                        status: getEmailStatusDescription(app.status, app),
                        actionText: '立即审批',
                        actionUrl: SERVER_CONFIG.url,
                        additionalInfo: '此申请已通过厂长和总监审核，现需要您的审批。'
                    });
                    sendEmailNotification(ceoEmails, emailSubject, emailContent, app.applicationCode)
                        .then(success => {
                            console.log('CEO邮件通知状态:', success ? '成功' : '失败');
                        });
                }

                console.log('总监审批通过，未选择经理，申请流转到CEO审批');
            }
        } else if (status === 'rejected') {
            app.status = '已拒绝';

            // 发送邮件通知申请人
            const users = getUsers();
            // 优先使用申请人的用户名查找，如果找不到则尝试使用申请人姓名查找
            let applicantUser = users.find(user => user.username === app.username);
            if (!applicantUser) {
                applicantUser = users.find(user => user.username === app.applicant);
            }

            if (applicantUser && applicantUser.email) {
                const emailSubject = `您的申请已被驳回 - ${app.applicationCode || ''}`;
                const emailContent = generateEmailTemplate({
                    title: '您的申请已被驳回',
                    applicant: app.applicant,
                    applicationCode: app.applicationCode || '无编号',
                    department: app.department,
                    date: app.date,
                    content: app.content,
                    priority: app.priority,
                    status: '已驳回',
                    actionText: '查看详情',
                    actionUrl: SERVER_CONFIG.url,
                    additionalInfo: `<strong>驳回原因:</strong> ${comment || '无'}<br><strong>驳回人:</strong> 总监`
                });
                sendEmailNotification(applicantUser.email, emailSubject, emailContent, app.applicationCode)
                    .then(success => {
                        console.log('申请人驳回通知邮件状态:', success ? '成功' : '失败');
                    });
            } else {
                console.log(`无法发送邮件通知申请人：找不到申请人 ${app.username} 的邮箱信息`);
            }
        }
    } else if (role === 'manager') {
        // 经理审批
        if (!app.approvals.managers[username] || app.approvals.managers[username].status !== 'pending') {
            return res.json({ success: false, message: '您不是该申请的审批人或已完成审批' });
        }

        // 确保附件数组存在
        if (!app.approvals.managers[username].attachments) {
            app.approvals.managers[username].attachments = [];
        }

        // 更新当前经理的审批状态和附件
        app.approvals.managers[username].status = status;
        if (newAttachments.length > 0) {
            app.approvals.managers[username].attachments = newAttachments;
        }
        app.approvals.managers[username].comment = comment || '';
        app.approvals.managers[username].date = new Date().toISOString();
        app.approvals.managers[username].signature = signature || '';

        console.log(`经理 ${username} 审批完成，状态: ${status}, 附件数: ${app.approvals.managers[username].attachments.length}`);

        if (status === 'approved') {
            // 检查是否所有经理都已审批通过
            const allManagersApproved = Object.values(app.approvals.managers)
                .every(m => m.status === 'approved');

            if (allManagersApproved) {
                // 获取用户数据以检查经理的用户代码
                const users = getUsers();

                // 检查参与审批的经理中是否有用户代码为"E10002"的经理
                const managerUsernames = Object.keys(app.approvals.managers);
                const hasE10002Manager = managerUsernames.some(managerUsername => {
                    const managerUser = users.find(u => u.username === managerUsername);
                    return managerUser && managerUser.userCode === 'E10002';
                });

                console.log(`经理审批完成检查: 参与审批的经理: ${managerUsernames.join(', ')}`);
                console.log(`是否包含用户代码E10002的经理: ${hasE10002Manager}`);

                if (hasE10002Manager) {
                    // 如果有用户代码为E10002的经理参与审批，直接完成申请
                    app.status = '已通过';

                    // 归档所有附件
                    archiveAttachments(app);

                    console.log('检测到用户代码E10002的经理参与审批，申请直接通过，跳过CEO审批');

                    // 发送邮件通知申请人申请已通过
                    // 优先使用申请人的用户名查找，如果找不到则尝试使用申请人姓名查找
                    let applicantUser = users.find(user => user.username === app.username);
                    if (!applicantUser) {
                        applicantUser = users.find(user => user.username === app.applicant);
                    }

                    // 准备申请人的邮件内容
                    const applicantEmailSubject = `您的申请已通过 - ${app.applicationCode || ''}`;
                    const applicantEmailContent = generateEmailTemplate({
                        title: '恭喜！您的申请已通过',
                        applicant: app.applicant,
                        applicationCode: app.applicationCode || '无编号',
                        department: app.department,
                        date: app.date,
                        content: app.content,
                        priority: app.priority,
                        status: '已通过',
                        actionText: '查看详情',
                        actionUrl: SERVER_CONFIG.url,
                        additionalInfo: '您的申请已完成全部审批流程并获得通过。'
                    });

                    // 发送邮件给申请人
                    if (applicantUser && applicantUser.email) {
                        sendEmailNotification(applicantUser.email, applicantEmailSubject, applicantEmailContent, app.applicationCode)
                            .then(success => {
                                console.log('申请人通过通知邮件状态:', success ? '成功' : '失败');
                            });
                    } else {
                        console.log(`无法发送邮件通知申请人：找不到申请人 ${app.username} 的邮箱信息`);
                    }

                    // 准备只读用户的邮件内容
                    const readonlyEmailSubject = `新的已审批通过申请提醒 - ${app.applicationCode || ''}`;
                    const readonlyEmailContent = generateEmailTemplate({
                        title: '有新的申请已完成审批流程',
                        applicant: app.applicant,
                        applicationCode: app.applicationCode || '无编号',
                        department: app.department,
                        date: app.date,
                        content: app.content,
                        priority: app.priority,
                        status: '已通过',
                        actionText: '查看详情',
                        actionUrl: SERVER_CONFIG.url,
                        additionalInfo: '该申请已完成全部审批流程并获得通过。'
                    });

                    // 检查申请金额是否超过100000人民币，只有超过才发送邮件给只读用户
                    let cnyAmount = 0;

                    // 如果是美元，使用提交时记录的汇率和人民币等值
                    if (app.currency === 'USD') {
                        // 确保使用提交时记录的汇率进行换算
                        if (app.exchangeRate && app.amount) {
                            cnyAmount = parseFloat(app.amount) * app.exchangeRate;
                            console.log(`美元申请: ${app.amount} USD, 提交时汇率: ${app.exchangeRate}, 人民币等值: ${cnyAmount} CNY`);
                        } else {
                            // 如果没有记录汇率，使用已换算金额或默认汇率
                            cnyAmount = app.cnyEquivalent || (parseFloat(app.amount || 0) * 7.2);
                            console.log(`美元申请: ${app.amount} USD, 使用默认汇率计算人民币等值: ${cnyAmount} CNY`);
                        }
                    } else {
                        // 如果是人民币，直接使用金额
                        cnyAmount = parseFloat(app.amount || 0);
                    }

                    if (cnyAmount > 100000) {
                        console.log(`申请金额 ${app.currency === 'USD' ? app.amount + ' USD (' + cnyAmount + ' CNY)' : cnyAmount + ' CNY'} 超过100000人民币，发送邮件给只读用户`);
                        // 发送邮件给所有只读用户
                        const readonlyUsers = users.filter(user => user.role === 'readonly' && user.email);
                        if (readonlyUsers.length > 0) {
                            const readonlyEmails = readonlyUsers.map(user => user.email);
                            sendEmailNotification(readonlyEmails, readonlyEmailSubject, readonlyEmailContent, app.applicationCode)
                                .then(success => {
                                    console.log('只读用户通知邮件状态:', success ? '成功' : '失败');
                                });
                        }
                    } else {
                        console.log(`申请金额 ${app.currency === 'USD' ? app.amount + ' USD (' + cnyAmount + ' CNY)' : cnyAmount + ' CNY'} 不超过100000人民币，不发送邮件给只读用户`);
                    }
                } else {
                    // 没有用户代码为E10002的经理，按原有流程流转到CEO审批
                    app.status = '待CEO审批';

                    // 初始化CEO审批结构
                    if (!app.approvals.ceo) {
                        app.approvals.ceo = { status: 'pending', attachments: [] };
                    }

                    // 重置提醒信息，因为进入了新的审批阶段
                    if (app.reminderInfo) {
                        app.reminderInfo.lastReminderTime = null;
                        app.reminderInfo.reminderCount = 0;
                        app.reminderInfo.escalationLevel = 'normal';
                    }

                    // 记录当前审批层级的开始时间
                    if (!app.stageTimestamps) {
                        app.stageTimestamps = {};
                    }
                    app.stageTimestamps.ceoStageStartTime = new Date().toISOString();

                    // 发送邮件通知CEO
                    const users = getUsers();
                    const ceoUsers = users.filter(user => user.role === 'ceo');
                    const ceoEmails = ceoUsers.map(user => user.email).filter(email => email);

                    if (ceoEmails.length > 0) {
                        const emailSubject = `新申请待审核 - ${app.applicant}`;
                        const emailContent = generateEmailTemplate({
                            title: '您有一份申请需要审批',
                            applicant: app.applicant,
                            applicationCode: app.applicationCode || '无编号',
                            department: app.department,
                            date: app.date,
                            content: app.content,
                            priority: app.priority,
                            status: getEmailStatusDescription(app.status, app),
                            actionText: '立即审批',
                            actionUrl: SERVER_CONFIG.url,
                            additionalInfo: '此申请已通过所有前置审核，现需要您的审批。'
                        });
                        sendEmailNotification(ceoEmails, emailSubject, emailContent, app.applicationCode)
                            .then(success => {
                                console.log('CEO邮件通知状态:', success ? '成功' : '失败');
                            });
                    }

                    console.log('经理审批通过，申请流转到CEO审批');
                }
            } else {
                console.log('还有经理未完成审批，等待其他经理审批');
            }
        } else if (status === 'rejected') {
            // 任一经理拒绝，整个申请被拒绝
            app.status = '已拒绝';

            // 发送邮件通知申请人
            const users = getUsers();
            // 优先使用申请人的用户名查找，如果找不到则尝试使用申请人姓名查找
            let applicantUser = users.find(user => user.username === app.username);
            if (!applicantUser) {
                applicantUser = users.find(user => user.username === app.applicant);
            }

            if (applicantUser && applicantUser.email) {
                const emailSubject = `您的申请已被驳回 - ${app.applicationCode || ''}`;
                const emailContent = generateEmailTemplate({
                    title: '您的申请已被驳回',
                    applicant: app.applicant,
                    applicationCode: app.applicationCode || '无编号',
                    department: app.department,
                    date: app.date,
                    content: app.content,
                    priority: app.priority,
                    status: '已驳回',
                    actionText: '查看详情',
                    actionUrl: SERVER_CONFIG.url,
                    additionalInfo: `<strong>驳回原因:</strong> ${comment || '无'}<br><strong>驳回人:</strong> 经理 ${username}`
                });
                sendEmailNotification(applicantUser.email, emailSubject, emailContent, app.applicationCode)
                    .then(success => {
                        console.log('申请人驳回通知邮件状态:', success ? '成功' : '失败');
                    });
            } else {
                console.log(`无法发送邮件通知申请人：找不到申请人 ${app.username} 的邮箱信息`);
            }
        }
    } else if (role === 'ceo') {
        // CEO审批
        if (!app.approvals.ceo || app.approvals.ceo.status !== 'pending') {
            return res.json({ success: false, message: '您不是该申请的审批人或已完成审批' });
        }

        // 确保附件数组存在
        if (!app.approvals.ceo.attachments) {
            app.approvals.ceo.attachments = [];
        }

        // 更新CEO的审批状态和附件
        app.approvals.ceo.status = status;
        if (newAttachments.length > 0) {
            app.approvals.ceo.attachments = newAttachments;
        }
        app.approvals.ceo.comment = comment || '';
        app.approvals.ceo.date = new Date().toISOString();
        app.approvals.ceo.signature = signature || '';
        app.approvals.ceo.approverUsername = username; // 记录审批人用户名，便于历史数据处理

        console.log(`CEO审批完成，状态: ${status}, 附件数: ${app.approvals.ceo.attachments.length}`);
        console.log(`CEO签名信息: ${signature ? '有签名 (长度: ' + signature.length + ')' : '无签名'}`);
        console.log(`CEO审批人用户名: ${username}`);

        if (status === 'approved') {
            // CEO审批通过，申请最终通过
            app.status = '已通过';
            // 归档所有附件
            archiveAttachments(app);

            // 发送邮件通知申请人申请已通过
            const users = getUsers();
            // 优先使用申请人的用户名查找，如果找不到则尝试使用申请人姓名查找
            let applicantUser = users.find(user => user.username === app.username);
            if (!applicantUser) {
                applicantUser = users.find(user => user.username === app.applicant);
            }

            // 准备申请人的邮件内容
            const applicantEmailSubject = `您的申请已通过 - ${app.applicationCode || ''}`;
            const applicantEmailContent = generateEmailTemplate({
                title: '恭喜！您的申请已通过',
                applicant: app.applicant,
                applicationCode: app.applicationCode || '无编号',
                department: app.department,
                date: app.date,
                content: app.content,
                priority: app.priority,
                status: '已通过',
                actionText: '查看详情',
                actionUrl: SERVER_CONFIG.url,
                additionalInfo: '您的申请已完成全部审批流程并获得通过。'
            });

            // 发送邮件给申请人
            if (applicantUser && applicantUser.email) {
                sendEmailNotification(applicantUser.email, applicantEmailSubject, applicantEmailContent, app.applicationCode)
                    .then(success => {
                        console.log('申请人通过通知邮件状态:', success ? '成功' : '失败');
                    });
            } else {
                console.log(`无法发送邮件通知申请人：找不到申请人 ${app.username} 的邮箱信息`);
            }

            // 准备只读用户的邮件内容
            const readonlyEmailSubject = `新的已审批通过申请提醒 - ${app.applicationCode || ''}`;
            const readonlyEmailContent = generateEmailTemplate({
                title: '有新的申请已完成审批流程',
                applicant: app.applicant,
                applicationCode: app.applicationCode || '无编号',
                department: app.department,
                date: app.date,
                content: app.content,
                priority: app.priority,
                status: '已通过',
                actionText: '查看详情',
                actionUrl: SERVER_CONFIG.url,
                additionalInfo: '该申请已完成全部审批流程并获得通过。'
            });

            // 检查申请金额是否超过100000人民币，只有超过才发送邮件给只读用户
            let cnyAmount = 0;

            // 如果是美元，使用提交时记录的汇率和人民币等值
            if (app.currency === 'USD') {
                // 确保使用提交时记录的汇率进行换算
                if (app.exchangeRate && app.amount) {
                    cnyAmount = parseFloat(app.amount) * app.exchangeRate;
                    console.log(`美元申请: ${app.amount} USD, 提交时汇率: ${app.exchangeRate}, 人民币等值: ${cnyAmount} CNY`);
                } else {
                    // 如果没有记录汇率，使用已换算金额或默认汇率
                    cnyAmount = app.cnyEquivalent || (parseFloat(app.amount || 0) * 7.2);
                    console.log(`美元申请: ${app.amount} USD, 使用默认汇率计算人民币等值: ${cnyAmount} CNY`);
                }
            } else {
                // 如果是人民币，直接使用金额
                cnyAmount = parseFloat(app.amount || 0);
            }

            if (cnyAmount > 100000) {
                console.log(`申请金额 ${app.currency === 'USD' ? app.amount + ' USD (' + cnyAmount + ' CNY)' : cnyAmount + ' CNY'} 超过100000人民币，发送邮件给只读用户`);
                // 发送邮件给所有只读用户
                const readonlyUsers = users.filter(user => user.role === 'readonly' && user.email);
                if (readonlyUsers.length > 0) {
                    const readonlyEmails = readonlyUsers.map(user => user.email);
                    sendEmailNotification(readonlyEmails, readonlyEmailSubject, readonlyEmailContent, app.applicationCode)
                        .then(success => {
                            console.log('只读用户通知邮件状态:', success ? '成功' : '失败');
                        });
                }
            } else {
                console.log(`申请金额 ${app.currency === 'USD' ? app.amount + ' USD (' + cnyAmount + ' CNY)' : cnyAmount + ' CNY'} 不超过100000人民币，不发送邮件给只读用户`);
            }
        } else if (status === 'rejected') {
            // CEO拒绝，整个申请被拒绝
            app.status = '已拒绝';

            // 发送邮件通知申请人
            const users = getUsers();
            // 优先使用申请人的用户名查找，如果找不到则尝试使用申请人姓名查找
            let applicantUser = users.find(user => user.username === app.username);
            if (!applicantUser) {
                applicantUser = users.find(user => user.username === app.applicant);
            }

            if (applicantUser && applicantUser.email) {
                const emailSubject = `您的申请已被驳回 - ${app.applicationCode || ''}`;
                const emailContent = generateEmailTemplate({
                    title: '您的申请已被驳回',
                    applicant: app.applicant,
                    applicationCode: app.applicationCode || '无编号',
                    department: app.department,
                    date: app.date,
                    content: app.content,
                    priority: app.priority,
                    status: '已驳回',
                    actionText: '查看详情',
                    actionUrl: SERVER_CONFIG.url,
                    additionalInfo: `<strong>驳回原因:</strong> ${comment || '无'}<br><strong>驳回人:</strong> CEO`
                });
                sendEmailNotification(applicantUser.email, emailSubject, emailContent, app.applicationCode)
                    .then(success => {
                        console.log('申请人驳回通知邮件状态:', success ? '成功' : '失败');
                    });
            } else {
                console.log(`无法发送邮件通知申请人：找不到申请人 ${app.username} 的邮箱信息`);
            }
        }
    } else {
        return res.json({ success: false, message: '无效的审批角色或申请状态' });
    }

    // 添加调试日志
    console.log(`准备保存申请ID=${id}的审批数据，当前状态=${app.status}`);

    // 厂长审批时特别记录所有厂长的审批状态
    if (role === 'director') {
        const directorsStatus = Object.entries(app.approvals.directors).map(([name, d]) =>
            `${name}: ${d.status}`).join(', ');
        console.log(`厂长审批状态: ${directorsStatus}`);
    }

    // 使用新的保存方式：保存单个申请文件
    if (saveApplication(app)) {
        // 清除缓存以确保下次读取时获取最新数据
        cache.applications = null;
        cache.lastApplicationUpdate = 0;

        console.log(`申请审批数据已保存成功: ID=${id}, 状态=${app.status}`);
        res.json({ success: true, message: '审批完成', application: app });
    } else {
        console.error(`保存申请审批数据失败: ID=${id}`);
        res.status(500).json({ success: false, message: '保存数据失败，请稍后重试' });
    }
});

// 文件下载接口
app.get('/download/:filename', (req, res) => {
    const filename = req.params.filename;
    const originalName = req.query.name; // 获取原始文件名
    const filePath = path.join(__dirname, '..', 'uploads', filename);

    if (fs.existsSync(filePath)) {
        if (originalName) {
            // 如果提供了原始文件名，使用它作为下载文件名
            const decodedName = decodeURIComponent(originalName);
            res.download(filePath, decodedName);
        } else {
            // 如果没有提供原始文件名，尝试从存储的文件名中提取
            // 存储格式：timestamp-randomNumber-originalName
            const parts = filename.split('-');
            if (parts.length >= 3) {
                // 移除前两部分（时间戳和随机数），保留原始文件名
                const extractedName = parts.slice(2).join('-');
                res.download(filePath, extractedName);
            } else {
                // 如果无法提取，使用存储的文件名
                res.download(filePath);
            }
        }
    } else {
        res.status(404).json({ success: false, message: '文件不存在' });
    }
});

// 文件预览接口
app.get('/preview/:filename', (req, res) => {
    const filename = req.params.filename;
    const filePath = path.join(__dirname, '..', 'uploads', filename);

    if (fs.existsSync(filePath)) {
        // 获取文件扩展名
        const ext = path.extname(filename).toLowerCase();

        // 根据文件类型设置适当的Content-Type
        if (ext === '.pdf') {
            res.setHeader('Content-Type', 'application/pdf');
        } else if (ext === '.doc' || ext === '.docx') {
            res.setHeader('Content-Type', 'application/msword');
        } else if (ext === '.jpg' || ext === '.jpeg') {
            res.setHeader('Content-Type', 'image/jpeg');
        } else if (ext === '.png') {
            res.setHeader('Content-Type', 'image/png');
        } else {
            // 默认二进制流
            res.setHeader('Content-Type', 'application/octet-stream');
        }

        // 设置内联显示而非下载
        res.setHeader('Content-Disposition', `inline; filename="${encodeURIComponent(filename)}"`);

        // 发送文件
        fs.createReadStream(filePath).pipe(res);
    } else {
        res.status(404).json({ success: false, message: '文件不存在' });
    }
});

// 处理角色变更时的历史审批记录保留
function handleRoleChangeApprovalHistory(username, originalRole, newRole) {
    try {
        console.log(`开始处理用户 ${username} 的角色变更：${originalRole} -> ${newRole}`);

        // 获取所有申请数据
        let applications = getApplications();
        let hasChanges = false;

        // 遍历所有申请，查找该用户的历史审批记录
        applications.forEach(app => {
            if (!app.approvals) return;

            // 处理从经理角色变更为其他角色的情况
            if (originalRole === 'manager' && app.approvals.managers && app.approvals.managers[username]) {
                const managerApproval = app.approvals.managers[username];
                console.log(`发现用户 ${username} 在申请 ${app.id} 中的经理审批记录`);

                // 在审批记录中添加角色变更标记，但保留原始审批记录
                managerApproval.originalRole = 'manager';
                managerApproval.roleChangedTo = newRole;
                managerApproval.roleChangeDate = new Date().toISOString();

                hasChanges = true;
            }

            // 处理从厂长角色变更为其他角色的情况
            if (originalRole === 'director' && app.approvals.directors && app.approvals.directors[username]) {
                const directorApproval = app.approvals.directors[username];
                console.log(`发现用户 ${username} 在申请 ${app.id} 中的厂长审批记录`);

                // 在审批记录中添加角色变更标记，但保留原始审批记录
                directorApproval.originalRole = 'director';
                directorApproval.roleChangedTo = newRole;
                directorApproval.roleChangeDate = new Date().toISOString();

                hasChanges = true;
            }

            // 处理从总监角色变更为其他角色的情况
            if (originalRole === 'chief' && app.approvals.chief &&
                (app.approvals.chief.status === 'approved' || app.approvals.chief.status === 'rejected')) {
                console.log(`发现用户 ${username} 在申请 ${app.id} 中的总监审批记录`);

                // 在审批记录中添加角色变更标记，但保留原始审批记录
                app.approvals.chief.originalRole = 'chief';
                app.approvals.chief.roleChangedTo = newRole;
                app.approvals.chief.roleChangeDate = new Date().toISOString();
                app.approvals.chief.approverUsername = username; // 记录审批人用户名

                hasChanges = true;
            }

            // 处理从CEO角色变更为其他角色的情况
            if (originalRole === 'ceo' && app.approvals.ceo &&
                (app.approvals.ceo.status === 'approved' || app.approvals.ceo.status === 'rejected')) {
                console.log(`发现用户 ${username} 在申请 ${app.id} 中的CEO审批记录`);

                // 在审批记录中添加角色变更标记，但保留原始审批记录
                app.approvals.ceo.originalRole = 'ceo';
                app.approvals.ceo.roleChangedTo = newRole;
                app.approvals.ceo.roleChangeDate = new Date().toISOString();
                app.approvals.ceo.approverUsername = username; // 记录审批人用户名

                hasChanges = true;
            }
        });

        // 如果有变更，保存申请数据
        if (hasChanges) {
            // 保存所有有变更的申请
            let saveSuccess = true;
            for (const app of applications) {
                if (!saveApplication(app)) {
                    console.error(`保存申请失败: ID=${app.id}`);
                    saveSuccess = false;
                }
            }

            if (saveSuccess) {
                // 清除缓存
                cache.applications = null;
                cache.lastApplicationUpdate = 0;
                console.log(`用户 ${username} 的历史审批记录已成功更新并保留`);
            } else {
                console.error(`保存用户 ${username} 的历史审批记录时出错`);
            }
        } else {
            console.log(`用户 ${username} 没有需要更新的历史审批记录`);
        }

    } catch (error) {
        console.error(`处理用户 ${username} 角色变更时的历史审批记录出错:`, error);
    }
}

// 归档附件函数
function archiveAttachments(application) {
    try {
        console.log('开始归档附件，申请ID:', application.id);

        // 收集所有附件
        const attachments = [
            ...(application.attachments || [])
        ];

        // 收集所有厂长的附件
        if (application.approvals.directors) {
            Object.values(application.approvals.directors).forEach(director => {
                if (director.attachments && director.attachments.length > 0) {
                    console.log(`收集厂长附件: ${director.attachments.length}个`);
                    attachments.push(...director.attachments);
                }
            });
        }

        // 收集总监的附件
        if (application.approvals.chief && application.approvals.chief.attachments) {
            console.log(`收集总监附件: ${application.approvals.chief.attachments.length}个`);
            attachments.push(...application.approvals.chief.attachments);
        }

        // 收集所有经理的附件
        if (application.approvals.managers) {
            Object.values(application.approvals.managers).forEach(manager => {
                if (manager.attachments && manager.attachments.length > 0) {
                    console.log(`收集经理附件: ${manager.attachments.length}个`);
                    attachments.push(...manager.attachments);
                }
            });
        }

        // 收集CEO的附件
        if (application.approvals.ceo && application.approvals.ceo.attachments) {
            console.log(`收集CEO附件: ${application.approvals.ceo.attachments.length}个`);
            attachments.push(...application.approvals.ceo.attachments);
        }

        console.log(`总共收集到 ${attachments.length} 个附件`);

        // 创建归档目录
        if (!fs.existsSync(archiveDir)) {
            fs.mkdirSync(archiveDir, { recursive: true });
        }

        // 为每个申请创建一个子目录
        const appDir = path.join(archiveDir, `app_${application.id}`);
        if (!fs.existsSync(appDir)) {
            fs.mkdirSync(appDir, { recursive: true });
        }

        // 复制所有附件到归档目录
        let successCount = 0;
        attachments.forEach(attachment => {
            try {
                if (!attachment || !attachment.path) {
                    console.log('跳过无效附件:', attachment);
                    return;
                }

                const sourcePath = path.join(__dirname, '..', 'uploads', attachment.path);
                const destPath = path.join(appDir, attachment.path);

                if (fs.existsSync(sourcePath)) {
                    fs.copyFileSync(sourcePath, destPath);
                    successCount++;
                } else {
                    console.log(`附件文件不存在: ${sourcePath}`);
                }
            } catch (attachError) {
                console.error('处理单个附件时出错:', attachError);
            }
        });

        console.log(`成功归档 ${successCount}/${attachments.length} 个附件`);
    } catch (error) {
        console.error('归档附件时出错:', error);
    }
}

// 获取所有厂长和经理用户接口
app.get('/approvers', (req, res) => {
    try {
        const users = getUsers();
        const directors = users.filter(user => user.role === 'director' || user.role === 'chief').map(user => ({
            username: user.username,
            department: user.role === 'chief' ? (user.department || '经理室') : user.department
        }));
        const managers = users.filter(user => user.role === 'manager').map(user => ({
            username: user.username
        }));

        res.json({
            success: true,
            directors,
            managers
        });
    } catch (error) {
        res.status(500).json({ success: false, message: '获取审批人列表失败' });
    }
});

// 导出用户数据为CSV格式（仅管理员）
app.get('/exportUsers', (req, res) => {
    const { username } = req.query;
    const users = getUsers();

    // 检查权限
    const adminUser = users.find(u => u.username === username);
    if (!adminUser || adminUser.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足，只有管理员可以导出用户数据' });
    }

    // 创建CSV内容
    let csvContent = 'username,password,role,email,department,userCode\n';
    users.forEach(user => {
        // 过滤掉签名数据，因为它是base64格式，不适合CSV
        const { signature, ...userData } = user;
        const row = [
            userData.username || '',
            userData.password || '',
            userData.role || '',
            userData.email || '',
            userData.department || '',
            userData.userCode || ''
        ].map(field => {
            // 处理可能包含逗号、引号或换行符的字段
            const fieldStr = String(field);
            if (fieldStr.includes(',') || fieldStr.includes('"') || fieldStr.includes('\n')) {
                return `"${fieldStr.replace(/"/g, '""')}"`;
            }
            return fieldStr;
        }).join(',');
        csvContent += row + '\n';
    });

    // 设置响应头，使浏览器下载文件
    res.setHeader('Content-Type', 'text/csv; charset=utf-8');
    res.setHeader('Content-Disposition', 'attachment; filename=users.csv');

    // 添加BOM标记以确保Excel正确识别UTF-8编码
    const BOM = '\uFEFF';
    res.send(BOM + csvContent);
});

// 导入CSV用户数据（仅管理员）
app.post('/importUsers', express.text({ type: 'text/csv', limit: '1mb' }), (req, res) => {
    const { username } = req.query;
    let users = getUsers();

    // 检查权限
    const adminUser = users.find(u => u.username === username);
    if (!adminUser || adminUser.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足，只有管理员可以导入用户数据' });
    }

    try {
        // 移除BOM标记（如果存在）
        let csvData = req.body;
        if (csvData.charCodeAt(0) === 0xFEFF) {
            csvData = csvData.substring(1);
        }

        // 解析CSV数据
        const lines = csvData.split(/\r?\n/);
        const headers = lines[0].split(',').map(header => header.trim());

        // 验证CSV格式
        const requiredHeaders = ['username', 'password', 'role'];
        for (const header of requiredHeaders) {
            if (!headers.includes(header)) {
                return res.status(400).json({
                    success: false,
                    message: `CSV格式错误：缺少必要的列 "${header}"`
                });
            }
        }

        // 解析CSV数据
        const importedUsers = [];
        const errors = [];

        for (let i = 1; i < lines.length; i++) {
            if (!lines[i].trim()) continue; // 跳过空行

            // 解析CSV行，正确处理引号包裹的字段
            const values = [];
            let currentValue = '';
            let insideQuotes = false;

            for (let j = 0; j < lines[i].length; j++) {
                const char = lines[i][j];

                if (char === '"') {
                    if (insideQuotes && j + 1 < lines[i].length && lines[i][j + 1] === '"') {
                        // 处理双引号转义 ("" -> ")
                        currentValue += '"';
                        j++; // 跳过下一个引号
                    } else {
                        // 切换引号状态
                        insideQuotes = !insideQuotes;
                    }
                } else if (char === ',' && !insideQuotes) {
                    // 字段结束
                    values.push(currentValue);
                    currentValue = '';
                } else {
                    // 普通字符
                    currentValue += char;
                }
            }

            // 添加最后一个字段
            values.push(currentValue);

            if (values.length !== headers.length) {
                errors.push(`第${i}行: 列数不匹配，期望${headers.length}列，实际${values.length}列`);
                continue;
            }

            const user = {};
            headers.forEach((header, index) => {
                user[header] = values[index];
            });

            // 验证必填字段
            if (!user.username || !user.password || !user.role) {
                errors.push(`第${i}行: 用户名、密码和角色为必填项`);
                continue;
            }

            // 验证用户代码格式（如果提供）
            if (user.userCode) {
                if (!/^[A-Za-z0-9]{6}$/.test(user.userCode)) {
                    errors.push(`第${i}行: 用户代码必须是6位字母或数字组合`);
                    continue;
                }
            }

            // 验证角色是否有效
            const validRoles = ['user', 'director', 'chief', 'manager', 'ceo', 'admin', 'readonly'];
            if (!validRoles.includes(user.role)) {
                errors.push(`第${i}行: 无效的角色 "${user.role}"`);
                continue;
            }

            importedUsers.push(user);
        }

        // 检查用户代码是否重复（在导入数据内部）
        const importUserCodes = new Set();

        for (const user of importedUsers) {
            if (user.userCode && importUserCodes.has(user.userCode)) {
                errors.push(`导入数据中用户代码 "${user.userCode}" 重复`);
                continue;
            }

            if (user.userCode) {
                importUserCodes.add(user.userCode);
            }
        }

        // 如果有错误，返回错误信息
        if (errors.length > 0) {
            return res.status(400).json({
                success: false,
                message: '导入过程中发现错误',
                errors
            });
        }

        // 处理用户数据更新和添加
        const updatedUsers = [...users]; // 复制现有用户数组
        let addedCount = 0;
        let updatedCount = 0;

        for (const importedUser of importedUsers) {
            // 检查用户名是否已存在
            const existingUserIndex = updatedUsers.findIndex(u => u.username === importedUser.username);

            if (existingUserIndex !== -1) {
                // 用户已存在，更新信息（保留电子签名）
                const existingSignature = updatedUsers[existingUserIndex].signature;

                // 检查是否有其他用户已经使用了这个用户代码
                if (importedUser.userCode &&
                    updatedUsers.some(u => u.userCode === importedUser.userCode && u.username !== importedUser.username)) {
                    errors.push(`用户代码 "${importedUser.userCode}" 已被其他用户使用`);
                    continue;
                }

                // 更新用户信息，保留电子签名
                updatedUsers[existingUserIndex] = {
                    ...importedUser,
                    signature: existingSignature
                };
                updatedCount++;
            } else {
                // 新用户，检查用户代码是否已被使用
                if (importedUser.userCode &&
                    updatedUsers.some(u => u.userCode === importedUser.userCode)) {
                    errors.push(`用户代码 "${importedUser.userCode}" 已被其他用户使用`);
                    continue;
                }

                // 添加新用户，生成唯一的用户ID
                const newUser = {
                    ...importedUser,
                    userId: generateUniqueUserId(),
                    createdAt: new Date().toISOString()
                    // 不设置signature字段，让它保持undefined
                };
                updatedUsers.push(newUser);
                addedCount++;
            }
        }

        // 如果有错误，返回错误信息
        if (errors.length > 0) {
            return res.status(400).json({
                success: false,
                message: '导入过程中发现错误',
                errors
            });
        }

        // 保存更新后的用户数据
        saveUsers(updatedUsers);

        res.json({
            success: true,
            message: `导入成功：添加了 ${addedCount} 个新用户，更新了 ${updatedCount} 个现有用户`
        });
    } catch (error) {
        console.error('导入用户数据失败:', error);
        res.status(500).json({
            success: false,
            message: '导入用户数据失败: ' + error.message
        });
    }
});

// 根路径路由
app.get('/', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'frontend', 'index.html'));
});

// 测试页面路由
app.get('/test-frontend-settings.html', (req, res) => {
    res.sendFile(path.join(__dirname, '..', 'test-frontend-settings.html'));
});

// 优先级转换为中文函数
function getPriorityInChinese(priority) {
    switch(priority) {
        case 'high':
            return '紧急';
        case 'medium':
            return '中等';
        case 'normal':
            return '普通';
        default:
            return priority;
    }
}



// 根据申请的审批流程生成动态的邮件状态描述
function getEmailStatusDescription(status, app) {
    switch (status) {
        case '待厂长审核':
            return '待您审批';

        case '待总监审批':
            // 检查是否经过了厂长审批
            const hasDirectorApprovals = app.approvals && app.approvals.directors &&
                Object.keys(app.approvals.directors).length > 0 &&
                Object.values(app.approvals.directors).some(d => d.status === 'approved');

            if (hasDirectorApprovals) {
                return '已通过厂长审核，待您审批';
            } else {
                return '待您审批';
            }

        case '待经理审批':
            // 检查审批流程
            const hasDirectorApprovalsForManager = app.approvals && app.approvals.directors &&
                Object.values(app.approvals.directors).some(d => d.status === 'approved');
            const hasChiefApprovalForManager = app.approvals && app.approvals.chief &&
                app.approvals.chief.status === 'approved';

            if (hasDirectorApprovalsForManager && hasChiefApprovalForManager) {
                return '已通过厂长和总监审核，待您审批';
            } else if (hasChiefApprovalForManager) {
                return '已通过总监审核，待您审批';
            } else {
                return '待您审批';
            }

        case '待CEO审批':
            // 检查审批流程
            const hasDirectorApprovalsForCEO = app.approvals && app.approvals.directors &&
                Object.values(app.approvals.directors).some(d => d.status === 'approved');
            const hasChiefApprovalForCEO = app.approvals && app.approvals.chief &&
                app.approvals.chief.status === 'approved';
            const hasManagerApprovals = app.approvals && app.approvals.managers &&
                Object.keys(app.approvals.managers).length > 0 &&
                Object.values(app.approvals.managers).some(m => m.status === 'approved');

            if (hasManagerApprovals) {
                if (hasDirectorApprovalsForCEO) {
                    return '已通过厂长、总监和经理审核，待您审批';
                } else {
                    return '已通过总监和经理审核，待您审批';
                }
            } else if (hasChiefApprovalForCEO) {
                if (hasDirectorApprovalsForCEO) {
                    return '已通过厂长和总监审核，待您审批';
                } else {
                    return '已通过总监审核，待您审批';
                }
            } else {
                return '待您审批';
            }

        default:
            return status;
    }
}

// 生成统一的邮件HTML模板
function generateEmailTemplate(data) {
    const {
        title,
        applicant,
        applicationCode,
        department,
        date,
        content,
        priority,
        status,
        actionText = '立即审批',
        actionUrl = SERVER_CONFIG.url,
        urgencyNote = '',
        additionalInfo = '',
        footerNote = ''
    } = data;

    // 获取优先级的中文显示和颜色
    const priorityInfo = getPriorityInfo(priority);

    return `
<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml" xmlns:v="urn:schemas-microsoft-com:vml" xmlns:o="urn:schemas-microsoft-com:office:office">
<head>
    <meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0"/>
    <meta name="x-apple-disable-message-reformatting" />
    <meta name="color-scheme" content="light">
    <meta name="supported-color-schemes" content="light">
    <title>${title}</title>
    <!--[if mso]>
    <xml>
        <o:OfficeDocumentSettings>
            <o:AllowPNG/>
            <o:PixelsPerInch>96</o:PixelsPerInch>
        </o:OfficeDocumentSettings>
    </xml>
    <![endif]-->
    <style type="text/css">
        /* 基础样式 */
        #outlook a {padding:0;}
        body {
            margin: 0;
            padding: 0;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            line-height: 1.5;
            color: #333333;
        }
        table, td {
            border-collapse: collapse;
            mso-table-lspace: 0pt;
            mso-table-rspace: 0pt;
        }
        img {
            border: 0;
            height: auto;
            line-height: 100%;
            outline: none;
            text-decoration: none;
            -ms-interpolation-mode: bicubic;
        }
        p {
            display: block;
            margin: 13px 0;
        }
        /* 容器样式 */
        .email-container {
            width: 100%;
            max-width: 560px;
            margin: 0 auto;
            border: 1px solid #dddddd;
        }
        .email-header {
            background-color: #3b82f6;
            color: #ffffff;
            padding: 20px 24px;
            text-align: center;
        }
        .email-header h1 {
            margin: 0;
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
        }
        .email-body {
            padding: 20px 24px;
            background-color: #ffffff;
        }
        .application-card {
            background-color: #f8f9fa;
            border: 1px solid #dddddd;
            border-left: 3px solid #3b82f6;
            padding: 16px;
            margin-bottom: 16px;
        }
        .info-row {
            margin-bottom: 8px;
        }
        .info-label {
            font-weight: 600;
            color: #495057;
            width: 80px;
            padding-right: 10px;
            font-size: 14px;
            vertical-align: top;
        }
        .info-value {
            color: #212529;
            font-size: 14px;
            vertical-align: top;
        }
        .priority-badge {
            display: inline-block;
            padding: 3px 10px;
            font-size: 11px;
            font-weight: 600;
            border-radius: 3px;
        }
        .priority-high {
            background-color: #dc3545;
            color: #ffffff;
        }
        .priority-medium {
            background-color: #fd7e14;
            color: #ffffff;
        }
        .priority-low {
            background-color: #e8f5e8;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .urgency-alert {
            background-color: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 12px;
            margin-bottom: 16px;
            color: #856404;
            font-size: 14px;
        }
        .urgency-alert strong {
            color: #dc3545;
        }
        .action-button {
            background-color: #3b82f6;
            color: #ffffff !important;
            text-decoration: none;
            padding: 14px 24px;
            font-weight: 600;
            font-size: 14px;
            display: inline-block;
            text-align: center;
            line-height: 1.5;
            border-radius: 4px;
            mso-padding-alt: 14px 24px;
        }
        .additional-info {
            background-color: #e9ecef;
            padding: 12px;
            margin: 16px 0;
            font-size: 13px;
        }
        .email-footer {
            background-color: #f8f9fa;
            padding: 16px 24px;
            border-top: 1px solid #dee2e6;
            text-align: center;
            font-size: 11px;
            color: #6c757d;
        }
        .system-note {
            font-style: italic;
            color: #6c757d;
            margin-top: 12px;
            padding-top: 12px;
            border-top: 1px solid #dee2e6;
            font-size: 13px;
        }
        /* Outlook特定样式 */
        .ExternalClass {
            width: 100%;
        }
        .ExternalClass,
        .ExternalClass p,
        .ExternalClass span,
        .ExternalClass font,
        .ExternalClass td,
        .ExternalClass div {
            line-height: 100%;
        }
        .ReadMsgBody {
            width: 100%;
        }
    </style>
    <!--[if mso]>
    <style type="text/css">
        /* Outlook特定样式 */
        body, table, td, p, a, li, blockquote {
            font-family: Arial, sans-serif !important;
        }
        .action-button {
            font-family: Arial, sans-serif !important;
            font-size: 14px !important;
            font-weight: 600 !important;
            text-decoration: none !important;
        }
        table {
            border-collapse: collapse !important;
        }
        .priority-badge {
            font-family: Arial, sans-serif !important;
            font-size: 11px !important;
            font-weight: 600 !important;
            padding: 3px 10px !important;
        }
    </style>
    <![endif]-->
</head>
<body style="margin:0; padding:0; background-color:#f5f5f5;">
    <!--[if mso | IE]>
    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color: #f5f5f5;">
    <tr>
    <td align="center" valign="top">
    <![endif]-->

    <div style="max-width:600px; margin:0 auto; padding:16px; text-align:center;">
        <table role="presentation" align="center" border="0" cellpadding="0" cellspacing="0" width="100%" style="max-width:560px; margin:0 auto; border:1px solid #dddddd; background-color:#ffffff; text-align:left;">
            <!-- 邮件头部 -->
            <tr>
                <td style="background-color:#3b82f6; padding:20px 24px; text-align:center;">
                    <h1 style="margin:0; font-size:20px; font-weight:600; color:#ffffff;">${title}</h1>
                </td>
            </tr>

            <!-- 邮件正文 -->
            <tr>
                <td style="padding:20px 24px; background-color:#ffffff;">
                    ${urgencyNote ? `<div style="background-color:#fff3cd; border:1px solid #ffeaa7; padding:12px; margin-bottom:16px; color:#856404; font-size:14px;">${urgencyNote}</div>` : ''}

                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%" style="background-color:#f8f9fa; border:1px solid #dddddd; border-left:3px solid #3b82f6; padding:16px; margin-bottom:16px;">
                        <tr>
                            <td>
                                <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                                    <tr>
                                        <td style="font-weight:600; color:#495057; width:80px; padding-right:10px; font-size:14px; vertical-align:top; padding-bottom:8px;">申请编号:</td>
                                        <td style="color:#212529; font-size:14px; vertical-align:top; padding-bottom:8px;">${applicationCode || '无编号'}</td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight:600; color:#495057; width:80px; padding-right:10px; font-size:14px; vertical-align:top; padding-bottom:8px;">申请人:</td>
                                        <td style="color:#212529; font-size:14px; vertical-align:top; padding-bottom:8px;">${applicant}</td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight:600; color:#495057; width:80px; padding-right:10px; font-size:14px; vertical-align:top; padding-bottom:8px;">部门:</td>
                                        <td style="color:#212529; font-size:14px; vertical-align:top; padding-bottom:8px;">${department}</td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight:600; color:#495057; width:80px; padding-right:10px; font-size:14px; vertical-align:top; padding-bottom:8px;">日期:</td>
                                        <td style="color:#212529; font-size:14px; vertical-align:top; padding-bottom:8px;">${date}</td>
                                    </tr>
                                    <tr>
                                        <td style="font-weight:600; color:#495057; width:80px; padding-right:10px; font-size:14px; vertical-align:top; padding-bottom:8px;">内容:</td>
                                        <td style="color:#212529; font-size:14px; vertical-align:top; padding-bottom:8px;">${content}</td>
                                    </tr>
                                    ${priority ? `
                                    <tr>
                                        <td style="font-weight:600; color:#495057; width:80px; padding-right:10px; font-size:14px; vertical-align:top; padding-bottom:8px;">紧急程度:</td>
                                        <td style="color:#212529; font-size:14px; vertical-align:top; padding-bottom:8px;">
                                            ${priorityInfo.cssClass === 'high' ?
                                                `<span style="display:inline-block; padding:3px 10px; font-size:11px; font-weight:600; background-color:#dc3545; color:#ffffff; border-radius:3px;">${priorityInfo.text}</span>` :
                                                priorityInfo.cssClass === 'medium' ?
                                                `<span style="display:inline-block; padding:3px 10px; font-size:11px; font-weight:600; background-color:#fd7e14; color:#ffffff; border-radius:3px;">${priorityInfo.text}</span>` :
                                                `<span style="display:inline-block; padding:3px 10px; font-size:11px; font-weight:600; background-color:#e8f5e8; color:#155724; border:1px solid #c3e6cb; border-radius:3px;">${priorityInfo.text}</span>`
                                            }
                                        </td>
                                    </tr>
                                    ` : ''}
                                    ${status ? `
                                    <tr>
                                        <td style="font-weight:600; color:#495057; width:80px; padding-right:10px; font-size:14px; vertical-align:top; padding-bottom:8px;">当前状态:</td>
                                        <td style="color:#212529; font-size:14px; vertical-align:top; padding-bottom:8px;">${status}</td>
                                    </tr>
                                    ` : ''}
                                </table>
                            </td>
                        </tr>
                    </table>

                    ${additionalInfo ? `<div style="background-color:#e9ecef; padding:12px; margin:16px 0; font-size:13px;">${additionalInfo}</div>` : ''}

                    <table role="presentation" border="0" cellpadding="0" cellspacing="0" width="100%">
                        <tr>
                            <td align="center" style="padding:16px 0;">
                                <!--[if mso]>
                                <v:roundrect xmlns:v="urn:schemas-microsoft-com:vml" xmlns:w="urn:schemas-microsoft-com:office:word" href="${actionUrl}" style="height:50px;v-text-anchor:middle;width:200px;" arcsize="10%" strokecolor="#3b82f6" fillcolor="#3b82f6">
                                    <w:anchorlock/>
                                    <center style="color:#ffffff;font-family:Arial,sans-serif;font-size:14px;font-weight:bold;">${actionText}</center>
                                </v:roundrect>
                                <![endif]-->
                                <!--[if !mso]><!-->
                                <a href="${actionUrl}" style="background-color:#3b82f6; color:#ffffff; text-decoration:none; padding:14px 24px; font-weight:600; font-size:14px; display:inline-block; text-align:center; line-height:1.5; border-radius:4px;">${actionText}</a>
                                <!--<![endif]-->
                            </td>
                        </tr>
                    </table>

                    ${footerNote ? `<div style="font-style:italic; color:#6c757d; margin-top:12px; padding-top:12px; border-top:1px solid #dee2e6; font-size:13px;">${footerNote}</div>` : ''}
                </td>
            </tr>

            <!-- 邮件底部 -->
            <tr>
                <td style="background-color:#f8f9fa; padding:16px 24px; border-top:1px solid #dee2e6; text-align:center; font-size:11px; color:#6c757d;">
                    此邮件由系统自动发送，请勿直接回复。<br>
                    如有问题，请直接登录系统或联系管理员。
                </td>
            </tr>
        </table>
    </div>

    <!--[if mso | IE]>
    </td>
    </tr>
    </table>
    <![endif]-->
</body>
</html>
    `;
}

// 获取优先级信息
function getPriorityInfo(priority) {
    switch (priority) {
        case 'high':
            return { text: '紧急', cssClass: 'high' };
        case 'medium':
            return { text: '中等', cssClass: 'medium' };
        case 'normal':
        case 'low':
        default:
            return { text: '普通', cssClass: 'low' };
    }
}

// 发送邮件提醒函数
async function sendEmailNotification(recipients, subject, content, applicationCode = null) {
    // 如果没有收件人，直接返回失败
    if (!recipients || (Array.isArray(recipients) && recipients.length === 0) || recipients === '') {
        const logPrefix = applicationCode ? `申请 ${applicationCode}: ` : '';
        logWithTime(`${logPrefix}邮件发送失败: 没有有效的收件人`);
        return false;
    }

    // 重试计数器
    let retryCount = 0;
    let hasLoggedFailure = false; // 标记是否已记录失败日志

    // 定义发送函数
    const sendMail = async () => {
        try {
            // 如果收件人是数组，则转换为逗号分隔的字符串
            const to = Array.isArray(recipients) ? recipients.join(',') : recipients;

            const mailOptions = {
                from: '"申请审批系统" <<EMAIL>>',
                to: to,
                subject: subject,
                html: content
            };

            const info = await transporter.sendMail(mailOptions);
            const logPrefix = applicationCode ? `申请 ${applicationCode}: ` : '';
            logWithTime(`${logPrefix}邮件发送成功:`, info.messageId);
            return true;
        } catch (error) {
            // 只在第一次失败时记录详细错误信息
            if (!hasLoggedFailure) {
                const logPrefix = applicationCode ? `申请 ${applicationCode}: ` : '';
                errorWithTime(`${logPrefix}邮件发送失败，开始重试中...`, error.message);
                hasLoggedFailure = true;
            }

            retryCount++;

            // 等待一段时间后重试（无限重试直到成功）
            await new Promise(resolve => setTimeout(resolve, SERVER_CONFIG.emailRetryDelay));
            return await sendMail();
        }
    };

    // 开始发送邮件
    return await sendMail();
}

// 获取用户邮箱函数
function getUserEmails(usernames) {
    try {
        const users = getUsers();
        const emails = [];

        usernames.forEach(username => {
            const user = users.find(u => u.username === username);
            if (user && user.email) {
                emails.push(user.email);
            }
        });

        return emails;
    } catch (error) {
        console.error('获取用户邮箱失败:', error);
        return [];
    }
}

// 总监撤回审批接口
app.post('/withdrawApproval', (req, res) => {
    const { id, username, role } = req.body;
    let applications = getApplications();
    const appIndex = applications.findIndex(a => a.id === parseInt(id));

    if (appIndex === -1) {
        return res.json({ success: false, message: '申请不存在' });
    }

    const app = applications[appIndex];

    // 验证请求用户是否为总监，只读角色不能撤回审批
    if (role !== 'chief') {
        return res.status(403).json({ success: false, message: '权限不足，只有总监可以撤回审批' });
    }

    // 验证申请状态是否为"已通过"且是由总监直接通过的（未经过经理和CEO审批）
    if (app.status !== '已通过' ||
        !app.approvals.chief ||
        app.approvals.chief.status !== 'approved' ||
        (app.approvals.managers && Object.keys(app.approvals.managers).length > 0) ||
        (app.approvals.ceo && app.approvals.ceo.status && app.approvals.ceo.status !== 'pending')) {
        return res.json({
            success: false,
            message: '只能撤回由总监直接通过且未经过经理和CEO审批的申请'
        });
    }

    // 将申请状态重置为"待总监审批"
    app.status = '待总监审批';

    // 重置总监审批状态
    app.approvals.chief.status = 'pending';

    // 重置提醒信息，因为重新进入审批阶段
    if (app.reminderInfo) {
        app.reminderInfo.lastReminderTime = null;
        app.reminderInfo.reminderCount = 0;
        app.reminderInfo.escalationLevel = 'normal';
    }

    // 重置并记录总监审批阶段开始时间
    if (!app.stageTimestamps) {
        app.stageTimestamps = {};
    }
    app.stageTimestamps.chiefStageStartTime = new Date().toISOString();

    // 记录撤回操作
    app.approvals.chief.withdrawnAt = new Date().toISOString();
    app.approvals.chief.withdrawnBy = username;

    // 保存更新后的申请
    saveApplications(applications);

    // 发送邮件通知申请人
    const users = getUsers();
    let applicantUser = users.find(user => user.username === app.username);
    if (!applicantUser) {
        applicantUser = users.find(user => user.username === app.applicant);
    }

    if (applicantUser && applicantUser.email) {
        const emailSubject = `您的申请被总监撤回重审 - ${app.applicationCode || ''}`;
        const emailContent = generateEmailTemplate({
            title: '您的申请被总监撤回进行重新审批',
            applicant: app.applicant,
            applicationCode: app.applicationCode || '无编号',
            department: app.department,
            date: app.date,
            content: app.content,
            priority: app.priority,
            status: '待总监审批',
            actionText: '查看详情',
            actionUrl: SERVER_CONFIG.url,
            additionalInfo: '总监已撤回此前的审批决定，申请将重新进入总监审批环节。'
        });
        sendEmailNotification(applicantUser.email, emailSubject, emailContent, app.applicationCode)
            .then(success => {
                console.log('申请人撤回通知邮件状态:', success ? '成功' : '失败');
            });
    }

    res.json({ success: true, message: '审批已成功撤回，申请已重新进入总监审批环节', application: app });
});

// 导出申请记录为Excel格式
app.get('/exportApplications', async (req, res) => {
    const { username, role, timeRange, startDate, endDate } = req.query;
    const applications = getApplications();

    // 检查用户是否存在
    const users = getUsers();
    const user = users.find(u => u.username === username);
    if (!user) {
        return res.status(403).json({ success: false, message: '用户不存在' });
    }

    // 根据用户角色筛选可见的申请
    let filteredApps = [];

    if (role === 'admin' || role === 'readonly') {
        // 管理员和只读用户可以看到所有申请
        filteredApps = applications;
    } else {
        // 所有其他用户（包括普通用户、厂长、总监、经理）只能看到自己提交的申请
        filteredApps = applications.filter(app => app.username === username);
    }

    // 检查是否提供了自定义日期范围
    if (startDate && endDate) {
        // 使用自定义日期范围筛选
        const start = new Date(startDate);
        // 将结束日期设置为当天的23:59:59，以包含整个结束日期
        const end = new Date(endDate);
        end.setHours(23, 59, 59, 999);

        filteredApps = filteredApps.filter(app => {
            const appDate = new Date(app.date);
            return appDate >= start && appDate <= end;
        });
    }
    // 如果没有提供自定义日期范围，则使用预设的时间范围
    else if (timeRange && timeRange !== 'all') {
        const now = new Date();
        const startOfDay = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        let startDate;
        if (timeRange === 'week') {
            // 本周（从本周一开始）
            const day = startOfDay.getDay() || 7; // 如果是周日，getDay()返回0，我们将其视为7
            startDate = new Date(startOfDay);
            startDate.setDate(startOfDay.getDate() - day + 1);
        } else if (timeRange === 'month') {
            // 本月（从1号开始）
            startDate = new Date(now.getFullYear(), now.getMonth(), 1);
        } else if (timeRange === 'year') {
            // 本年（从1月1日开始）
            startDate = new Date(now.getFullYear(), 0, 1);
        }

        if (startDate) {
            filteredApps = filteredApps.filter(app => {
                const appDate = new Date(app.date);
                return appDate >= startDate;
            });
        }
    }

    // 按日期倒序排序
    filteredApps.sort((a, b) => new Date(b.date) - new Date(a.date));

    // 创建Excel工作簿
    const workbook = new Excel.Workbook();
    const worksheet = workbook.addWorksheet('申请记录');

    // 设置列
    worksheet.columns = [
        { header: '申请编号', key: 'applicationCode', width: 15 },
        { header: '申请人', key: 'applicant', width: 12 },
        { header: '部门', key: 'department', width: 15 },
        { header: '申请日期', key: 'date', width: 15 },
        { header: '紧急程度', key: 'priority', width: 10 },
        { header: '申请内容', key: 'content', width: 40 },
        { header: '申请金额', key: 'amount', width: 12 },
        { header: '币种', key: 'currency', width: 8 },
        { header: '状态', key: 'status', width: 12 }
    ];

    // 设置表头样式
    worksheet.getRow(1).font = { bold: true };
    worksheet.getRow(1).alignment = { vertical: 'middle', horizontal: 'center' };

    // 添加数据
    filteredApps.forEach(app => {
        // 格式化金额
        const amount = app.amount ? parseFloat(app.amount).toFixed(2) : '0.00';
        // 格式化优先级
        const priority = getPriorityInChinese(app.priority);

        worksheet.addRow({
            applicationCode: app.applicationCode || '',
            applicant: app.applicant || '',
            department: app.department || '',
            date: app.date || '',
            priority: priority || '普通',
            content: app.content || '',
            amount: amount,
            currency: app.currency || 'CNY',
            status: app.status || ''
        });
    });

    // 设置金额列的格式
    worksheet.getColumn('amount').numFmt = '#,##0.00';

    // 设置所有单元格的对齐方式
    worksheet.eachRow((row, rowNumber) => {
        if (rowNumber > 1) { // 跳过表头
            row.eachCell((cell) => {
                cell.alignment = { vertical: 'middle', wrapText: true };
            });
        }
    });

    // 设置响应头，使浏览器下载文件
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

    // 设置文件名
    let fileName = '';

    // 检查是否使用了自定义日期范围
    if (startDate && endDate) {
        fileName = `申请记录_${startDate}_至_${endDate}.xlsx`;
    } else {
        // 根据时间范围设置文件名
        let timeRangeText = '';
        switch(timeRange) {
            case 'week':
                timeRangeText = '本周';
                break;
            case 'month':
                timeRangeText = '本月';
                break;
            case 'year':
                timeRangeText = '本年';
                break;
            default:
                timeRangeText = '全部';
        }

        // 设置文件名，包含当前日期
        const today = new Date().toISOString().slice(0, 10);
        fileName = `申请记录_${timeRangeText}_${today}.xlsx`;
    }

    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(fileName)}`);

    // 将工作簿写入响应
    await workbook.xlsx.write(res);
    res.end();
});

// 静默模式标志，启动后第一次检查后设为true
let silentMode = false;

// 定期提醒功能
function checkAndSendReminders() {
    try {
        const applications = getApplications();
        const users = getUsers();
        const reminderSettings = getReminderSettings();
        const now = new Date();

        // 检查时间控制设置
        if (!isReminderTimeAllowed(reminderSettings.timeControl || {})) {
            // 只在非静默模式下显示时间控制提示
            if (!silentMode) {
                logWithTime('当前时间不允许发送提醒邮件（时间控制设置）');
                // 第一次检查完成后启用静默模式
                silentMode = true;
                logWithTime('提醒检查系统已进入静默模式，后续检查将静默运行');
            }
            return;
        }

        // 筛选出需要提醒的申请（待审批状态）
        const pendingApplications = applications.filter(app =>
            app.status === '待厂长审核' ||
            app.status === '待总监审批' ||
            app.status === '待经理审批' ||
            app.status === '待CEO审批'
        );

        // 只在非静默模式下显示检查信息
        if (!silentMode) {
            logWithTime(`检查提醒任务: 找到 ${pendingApplications.length} 个待审批申请`);
        }

        pendingApplications.forEach(app => {
            // 确保提醒信息字段存在（兼容旧数据）
            if (!app.reminderInfo) {
                app.reminderInfo = {
                    lastReminderTime: null,
                    reminderCount: 0,
                    escalationLevel: 'normal'
                };
            }

            // 计算当前审批层级的等待时间
            let stageStartTime;
            if (app.status === '待厂长审核') {
                stageStartTime = app.stageTimestamps?.directorStageStartTime || app.id;
            } else if (app.status === '待总监审批') {
                stageStartTime = app.stageTimestamps?.chiefStageStartTime || app.id;
            } else if (app.status === '待经理审批') {
                stageStartTime = app.stageTimestamps?.managerStageStartTime || app.id;
            } else if (app.status === '待CEO审批') {
                stageStartTime = app.stageTimestamps?.ceoStageStartTime || app.id;
            } else {
                // 如果状态不匹配，使用申请提交时间作为后备
                stageStartTime = app.id;
            }

            const stageStart = new Date(stageStartTime);
            const waitingHours = (now - stageStart) / (1000 * 60 * 60);

            // 获取该申请的提醒策略
            const strategy = getApplicationReminderStrategy(app, reminderSettings);

            // 确定当前应该的提醒级别
            let currentLevel = 'normal';
            if (waitingHours >= 48) {
                currentLevel = 'urgent';
            } else if (waitingHours >= 24) {
                currentLevel = 'medium';
            }

            // 根据策略确定提醒间隔（小时）
            let reminderInterval = strategy.normalInterval;
            if (currentLevel === 'medium') {
                reminderInterval = strategy.mediumInterval;
            } else if (currentLevel === 'urgent') {
                reminderInterval = strategy.urgentInterval;
            }

            // 检查是否需要发送提醒
            let shouldSendReminder = false;
            let reasonForReminder = '';

            if (!app.reminderInfo.lastReminderTime) {
                // 如果从未发送过提醒，且申请已等待超过初始延迟时间，则发送
                if (waitingHours >= strategy.initialDelay) {
                    shouldSendReminder = true;
                    reasonForReminder = `首次提醒 (等待${Math.floor(waitingHours)}小时 >= 初始延迟${strategy.initialDelay}小时)`;
                } else {
                    reasonForReminder = `等待时间不足 (等待${Math.floor(waitingHours)}小时 < 初始延迟${strategy.initialDelay}小时)`;
                }
            } else {
                // 检查距离上次提醒是否已超过间隔时间
                const lastReminderTime = new Date(app.reminderInfo.lastReminderTime);
                const hoursSinceLastReminder = (now - lastReminderTime) / (1000 * 60 * 60);

                if (hoursSinceLastReminder >= reminderInterval) {
                    shouldSendReminder = true;
                    reasonForReminder = `间隔提醒 (距上次${Math.floor(hoursSinceLastReminder)}小时 >= 间隔${reminderInterval}小时)`;
                } else {
                    reasonForReminder = `间隔时间不足 (距上次${Math.floor(hoursSinceLastReminder)}小时 < 间隔${reminderInterval}小时)`;
                }
            }

            // 只在非静默模式下或需要发送提醒时记录详细日志
            if (!silentMode || shouldSendReminder) {
                logWithTime(`申请 ${app.applicationCode || app.id}: ${reasonForReminder}, 级别=${currentLevel}, 策略=${JSON.stringify(strategy)}`);
            }

            if (shouldSendReminder) {
                sendReminderEmail(app, users, currentLevel, waitingHours);

                // 更新提醒信息
                app.reminderInfo.lastReminderTime = now.toISOString();
                app.reminderInfo.reminderCount += 1;
                app.reminderInfo.escalationLevel = currentLevel;
            }
        });

        // 保存更新后的申请数据
        if (pendingApplications.length > 0) {
            saveApplications(applications);
        }

        // 第一次检查完成后启用静默模式
        if (!silentMode) {
            silentMode = true;
            logWithTime('提醒检查系统已进入静默模式，后续检查将静默运行');
        }

    } catch (error) {
        errorWithTime('检查提醒任务出错:', error);
    }
}

// 获取申请的提醒策略
function getApplicationReminderStrategy(app, reminderSettings) {
    // 只根据优先级策略确定提醒频率
    let priorityStrategy = null;
    if (app.priority === 'high') {
        priorityStrategy = reminderSettings.priority.high;
    } else if (app.priority === 'medium') {
        priorityStrategy = reminderSettings.priority.medium;
    } else {
        priorityStrategy = reminderSettings.priority.low;
    }

    // 直接返回优先级策略，不再考虑金额因素
    return {
        initialDelay: priorityStrategy.initialDelay,
        normalInterval: priorityStrategy.normalInterval,
        mediumInterval: priorityStrategy.mediumInterval,
        urgentInterval: priorityStrategy.urgentInterval
    };
}

// 发送提醒邮件
function sendReminderEmail(app, users, level, waitingHours) {
    try {
        // 确定收件人
        let recipients = [];

        if (app.status === '待厂长审核') {
            // 获取仍然处于pending状态的厂长
            const pendingDirectors = [];
            if (app.approvals.directors) {
                Object.entries(app.approvals.directors).forEach(([username, approval]) => {
                    if (approval.status === 'pending') {
                        pendingDirectors.push(username);
                    }
                });
            }
            recipients = getUserEmails(pendingDirectors);
        } else if (app.status === '待总监审批') {
            // 获取所有总监
            const chiefs = users.filter(user => user.role === 'chief').map(user => user.username);
            recipients = getUserEmails(chiefs);
        } else if (app.status === '待经理审批') {
            // 获取仍然处于pending状态的经理
            const pendingManagers = [];
            if (app.approvals.managers) {
                Object.entries(app.approvals.managers).forEach(([username, approval]) => {
                    if (approval.status === 'pending') {
                        pendingManagers.push(username);
                    }
                });
            }
            recipients = getUserEmails(pendingManagers);
        } else if (app.status === '待CEO审批') {
            // 获取所有CEO用户
            const ceos = users.filter(user => user.role === 'ceo').map(user => user.username);
            recipients = getUserEmails(ceos);
        }

        if (recipients.length === 0) {
            logWithTime(`申请 ${app.applicationCode} 没有找到有效的待审批人邮箱`);
            return;
        }

        // 生成邮件主题
        let subject = '';
        const waitingDays = Math.floor(waitingHours / 24);

        if (level === 'normal') {
            subject = `【待审批】${app.applicant}的申请等待您审批`;
        } else if (level === 'medium') {
            subject = `【请尽快处理】${app.applicant}的申请已等待超过1天`;
        } else if (level === 'urgent') {
            subject = `【紧急】${app.applicant}的申请已延迟${waitingDays}天，请立即处理`;
        }

        // 生成邮件内容
        const waitingTimeText = waitingDays > 0 ?
            `${waitingDays}天${Math.floor(waitingHours % 24)}小时` :
            `${Math.floor(waitingHours)}小时`;

        const urgencyNote = level === 'urgent' ?
            '<strong style="color: red;">⚠️ 此申请已被标记为紧急，请优先处理！</strong>' : '';

        // 根据申请的审批流程生成更准确的状态描述
        let statusDescription = getEmailStatusDescription(app.status, app);

        const content = generateEmailTemplate({
            title: '申请审批提醒',
            applicant: app.applicant,
            applicationCode: app.applicationCode || '无编号',
            department: app.department,
            date: app.date,
            content: app.content,
            priority: app.priority,
            status: statusDescription,
            actionText: '立即审批',
            actionUrl: SERVER_CONFIG.url,
            urgencyNote: urgencyNote,
            additionalInfo: `<strong>当前层级等待时间:</strong> ${waitingTimeText}`,
            footerNote: `这是第${app.reminderInfo.reminderCount + 1}次提醒邮件`
        });

        // 发送邮件
        sendEmailNotification(recipients, subject, content, app.applicationCode)
            .then(success => {
                if (success) {
                    logWithTime(`提醒邮件发送成功: 申请 ${app.applicationCode}, 级别 ${level}, 收件人 ${recipients.length} 人`);
                } else {
                    logWithTime(`提醒邮件发送失败: 申请 ${app.applicationCode}`);
                }
            });

    } catch (error) {
        errorWithTime('发送提醒邮件出错:', error);
    }
}

// 启动定时提醒任务
function startReminderScheduler() {
    logWithTime('启动申请提醒系统...');

    // 立即执行第一次检查
    logWithTime('开始第一次提醒检查...');
    checkAndSendReminders();

    // 之后每30分钟检查一次
    setInterval(checkAndSendReminders, 30 * 60 * 1000);
    logWithTime('定期提醒任务已启动，每30分钟检查一次');
}

// 获取提醒设置API
app.get('/getReminderSettings', (req, res) => {
    const { username } = req.query;
    const users = getUsers();

    // 检查权限
    const user = users.find(u => u.username === username);
    if (!user || user.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足，只有管理员可以查看提醒设置' });
    }

    try {
        const settings = getReminderSettings();
        res.json({ success: true, settings });
    } catch (error) {
        console.error('获取提醒设置失败:', error);
        res.status(500).json({ success: false, message: '获取提醒设置失败' });
    }
});



// 保存提醒策略API
app.post('/saveReminderStrategies', (req, res) => {
    const { username, strategies } = req.body;
    const users = getUsers();

    // 检查权限
    const user = users.find(u => u.username === username);
    if (!user || user.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足，只有管理员可以修改提醒策略' });
    }

    try {
        // 验证策略数据
        if (!strategies || typeof strategies !== 'object') {
            return res.status(400).json({ success: false, message: '无效的策略数据' });
        }

        // 获取当前设置
        const currentSettings = getReminderSettings();

        // 更新优先级策略
        if (strategies.priority) {
            ['high', 'medium', 'low'].forEach(priority => {
                if (strategies.priority[priority]) {
                    const p = strategies.priority[priority];
                    currentSettings.priority[priority] = {
                        initialDelay: Math.max(1, parseInt(p.initialDelay) || 8),
                        normalInterval: Math.max(1, parseInt(p.normalInterval) || 8),
                        mediumInterval: Math.max(1, parseInt(p.mediumInterval) || 4),
                        urgentInterval: Math.max(1, parseInt(p.urgentInterval) || 2)
                    };
                }
            });
        }

        // 更新时间控制设置
        if (strategies.timeControl) {
            currentSettings.timeControl = currentSettings.timeControl || {};

            // 工作日设置
            if (strategies.timeControl.workingDays) {
                currentSettings.timeControl.workingDays = {
                    enabled: Boolean(strategies.timeControl.workingDays.enabled),
                    days: Array.isArray(strategies.timeControl.workingDays.days) ?
                          strategies.timeControl.workingDays.days.filter(d => d >= 1 && d <= 7) :
                          [1, 2, 3, 4, 5],
                    startTime: strategies.timeControl.workingDays.startTime || '09:00',
                    endTime: strategies.timeControl.workingDays.endTime || '18:00'
                };
            }

            // 自定义日期设置
            if (strategies.timeControl.customDates) {
                currentSettings.timeControl.customDates = {
                    enabled: Boolean(strategies.timeControl.customDates.enabled),
                    skipDates: Array.isArray(strategies.timeControl.customDates.skipDates) ?
                              strategies.timeControl.customDates.skipDates :
                              []
                };
            }
        }

        // 保存设置
        if (saveReminderSettings(currentSettings)) {
            logWithTime(`管理员 ${username} 更新了提醒策略`);

            // 立即应用新的提醒策略，触发一次提醒检查
            logWithTime('立即应用新的提醒策略，触发提醒检查...');
            // 重置静默模式，让下次检查显示详细信息
            silentMode = false;
            // 异步执行提醒检查，避免阻塞响应
            setTimeout(() => {
                checkAndSendReminders();
            }, 100);

            res.json({
                success: true,
                message: '提醒策略保存成功并已立即生效'
            });
        } else {
            res.status(500).json({ success: false, message: '保存策略失败' });
        }
    } catch (error) {
        console.error('保存提醒策略失败:', error);
        res.status(500).json({ success: false, message: '保存策略失败' });
    }
});

// 手动触发提醒检查API
app.post('/triggerReminderCheck', (req, res) => {
    const { username } = req.body;
    const users = getUsers();

    // 检查权限
    const user = users.find(u => u.username === username);
    if (!user || user.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足，只有管理员可以触发提醒检查' });
    }

    try {
        logWithTime(`管理员 ${username} 手动触发提醒检查`);
        // 重置静默模式，显示详细检查信息
        silentMode = false;
        // 异步执行提醒检查
        setTimeout(() => {
            checkAndSendReminders();
        }, 100);

        res.json({ success: true, message: '提醒检查已触发，请查看服务器日志了解详细信息' });
    } catch (error) {
        console.error('触发提醒检查失败:', error);
        res.status(500).json({ success: false, message: '触发提醒检查失败' });
    }
});

// Dashboard API接口

// 获取Dashboard概览数据
app.get('/api/dashboard/overview', (req, res) => {
    const { username, role } = req.query;
    const users = getUsers();

    // 验证用户
    const user = users.find(u => u.username === username);
    if (!user) {
        return res.status(403).json({ success: false, message: '用户不存在' });
    }

    try {
        const allApplications = getApplications();
        const allDevices = getDevices();

        // 根据用户权限过滤数据
        let visibleApplications = [];
        let visibleDevices = [];

        if (role === 'admin' || role === 'readonly') {
            // 管理员和只读用户可以看到所有数据
            visibleApplications = allApplications;
            visibleDevices = allDevices;
        } else if (['director', 'chief', 'manager', 'ceo'].includes(role)) {
            // 审批人员可以看到所有申请数据（用于统计）和所有设备数据
            visibleApplications = allApplications;
            visibleDevices = allDevices;
        } else {
            // 普通用户只能看到自己的申请，但可以看到所有设备数据
            visibleApplications = allApplications.filter(app => app.username === username);
            visibleDevices = allDevices;
        }

        // 计算概览数据
        const overviewData = {
            totalApplications: visibleApplications.length,
            applicationTrend: calculateApplicationTrend(visibleApplications),
            totalDevices: visibleDevices.length,
            deviceTrend: calculateDeviceTrend(visibleDevices),
            approvalEfficiency: calculateApprovalEfficiency(allApplications, role, username),
            efficiencyTrend: calculateEfficiencyTrend(allApplications, role, username)
        };

        res.json({ success: true, data: overviewData });
    } catch (error) {
        console.error('获取Dashboard概览数据失败:', error);
        res.status(500).json({ success: false, message: '获取概览数据失败' });
    }
});

// 获取Dashboard图表数据
app.get('/api/dashboard/charts', (req, res) => {
    const { username, role } = req.query;
    const users = getUsers();

    // 验证用户
    const user = users.find(u => u.username === username);
    if (!user) {
        return res.status(403).json({ success: false, message: '用户不存在' });
    }

    try {
        const allApplications = getApplications();
        const allDevices = getDevices();

        // 根据用户权限过滤数据
        let visibleApplications = [];
        let visibleDevices = [];

        if (role === 'admin' || role === 'readonly') {
            // 管理员和只读用户可以看到所有数据
            visibleApplications = allApplications;
            visibleDevices = allDevices;
        } else if (['director', 'chief', 'manager', 'ceo'].includes(role)) {
            // 审批人员可以看到所有申请数据（用于趋势分析）和所有设备数据
            visibleApplications = allApplications;
            visibleDevices = allDevices;
        } else {
            // 普通用户只能看到自己的申请，但可以看到所有设备数据
            visibleApplications = allApplications.filter(app => app.username === username);
            visibleDevices = allDevices;
        }

        // 生成图表数据
        const chartData = {
            applicationTrend: generateApplicationTrendData(visibleApplications),
            deviceHealth: generateDeviceHealthData(visibleDevices),
            departmentStats: generateDepartmentStatsData(visibleApplications)
        };

        res.json({ success: true, data: chartData });
    } catch (error) {
        console.error('获取Dashboard图表数据失败:', error);
        res.status(500).json({ success: false, message: '获取图表数据失败' });
    }
});

// 获取Dashboard活动数据
app.get('/api/dashboard/activity', (req, res) => {
    const { username, role } = req.query;
    const users = getUsers();

    // 验证用户
    const user = users.find(u => u.username === username);
    if (!user) {
        return res.status(403).json({ success: false, message: '用户不存在' });
    }

    try {
        const allApplications = getApplications();

        // 根据用户权限过滤数据
        let visibleApplications = [];

        if (role === 'admin' || role === 'readonly') {
            // 管理员和只读用户可以看到所有申请活动
            visibleApplications = allApplications;
        } else if (['director', 'chief', 'manager', 'ceo'].includes(role)) {
            // 审批人员可以看到所有申请活动（用于了解系统整体状况）
            visibleApplications = allApplications;
        } else {
            // 普通用户只能看到自己的申请活动
            visibleApplications = allApplications.filter(app => app.username === username);
        }

        // 获取维修保养记录和设备数据用于生成活动
        const allDevices = getDevices();
        const allMaintenanceRecords = maintenanceManager.getAllRecords();

        // 根据权限过滤维修保养记录
        let visibleMaintenanceRecords = [];
        if (role === 'admin' || role === 'readonly') {
            visibleMaintenanceRecords = allMaintenanceRecords;
        } else if (['director', 'chief', 'manager', 'ceo'].includes(role)) {
            visibleMaintenanceRecords = allMaintenanceRecords;
        } else if (role === 'mechanical') {
            // 机电部用户可以看到所有维修保养记录
            visibleMaintenanceRecords = allMaintenanceRecords;
        } else {
            // 普通用户只能看到自己操作的维修保养记录
            visibleMaintenanceRecords = allMaintenanceRecords.filter(record => record.operator === username);
        }

        // 生成活动数据
        const activityData = {
            activities: generateRecentActivities(visibleApplications, visibleMaintenanceRecords, allDevices, username, role)
        };

        res.json({ success: true, data: activityData });
    } catch (error) {
        console.error('获取Dashboard活动数据失败:', error);
        res.status(500).json({ success: false, message: '获取活动数据失败' });
    }
});

// 获取Dashboard待办事项数据
app.get('/api/dashboard/todos', (req, res) => {
    const { username, role } = req.query;
    const users = getUsers();

    // 验证用户
    const user = users.find(u => u.username === username);
    if (!user) {
        return res.status(403).json({ success: false, message: '用户不存在' });
    }

    try {
        const allApplications = getApplications();
        const devices = getDevices();
        const maintenanceRecords = maintenanceManager.getAllRecords();

        // 生成待办事项数据
        const todoData = {
            todos: generateTodosByRole(allApplications, username, role, users, devices, maintenanceRecords, user.department)
        };

        res.json({ success: true, data: todoData });
    } catch (error) {
        console.error('获取Dashboard待办事项数据失败:', error);
        res.status(500).json({ success: false, message: '获取待办事项数据失败' });
    }
});

// 获取提醒统计API
app.get('/getReminderStats', (req, res) => {
    const { username } = req.query;
    const users = getUsers();

    // 检查权限
    const user = users.find(u => u.username === username);
    if (!user || user.role !== 'admin') {
        return res.status(403).json({ success: false, message: '权限不足，只有管理员可以查看统计信息' });
    }

    try {
        const applications = getApplications();
        const now = new Date();
        const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

        // 计算统计数据
        const stats = {
            totalReminders: 0,
            pendingApplications: 0,
            urgentApplications: 0,
            avgResponseTime: 0
        };

        // 统计待审批申请
        const pendingApps = applications.filter(app =>
            app.status === '待厂长审核' ||
            app.status === '待总监审批' ||
            app.status === '待经理审批' ||
            app.status === '待CEO审批'
        );

        stats.pendingApplications = pendingApps.length;

        // 统计紧急申请（等待超过48小时）
        stats.urgentApplications = pendingApps.filter(app => {
            const submitTime = new Date(app.id);
            const waitingHours = (now - submitTime) / (1000 * 60 * 60);
            return waitingHours >= 48;
        }).length;

        // 统计今日发送的提醒数量
        stats.totalReminders = applications.reduce((count, app) => {
            if (app.reminderInfo && app.reminderInfo.lastReminderTime) {
                const lastReminderTime = new Date(app.reminderInfo.lastReminderTime);
                if (lastReminderTime >= today) {
                    return count + 1;
                }
            }
            return count;
        }, 0);

        // 计算平均响应时间（已完成申请的平均处理时间）
        const completedApps = applications.filter(app =>
            app.status === '已通过' || app.status === '已拒绝'
        );

        if (completedApps.length > 0) {
            const totalResponseTime = completedApps.reduce((total, app) => {
                const submitTime = new Date(app.date);
                // 找到最后一次审批时间
                let lastApprovalTime = submitTime;

                // 检查各个审批阶段的时间
                if (app.approvals) {
                    // 检查厂长审批时间
                    if (app.approvals.directors) {
                        Object.values(app.approvals.directors).forEach(director => {
                            if (director.timestamp) {
                                const approvalTime = new Date(director.timestamp);
                                if (approvalTime > lastApprovalTime) {
                                    lastApprovalTime = approvalTime;
                                }
                            }
                        });
                    }

                    // 检查总监审批时间
                    if (app.approvals.chief && app.approvals.chief.timestamp) {
                        const approvalTime = new Date(app.approvals.chief.timestamp);
                        if (approvalTime > lastApprovalTime) {
                            lastApprovalTime = approvalTime;
                        }
                    }

                    // 检查经理审批时间
                    if (app.approvals.managers) {
                        Object.values(app.approvals.managers).forEach(manager => {
                            if (manager.timestamp) {
                                const approvalTime = new Date(manager.timestamp);
                                if (approvalTime > lastApprovalTime) {
                                    lastApprovalTime = approvalTime;
                                }
                            }
                        });
                    }

                    // 检查CEO审批时间
                    if (app.approvals.ceo && app.approvals.ceo.timestamp) {
                        const approvalTime = new Date(app.approvals.ceo.timestamp);
                        if (approvalTime > lastApprovalTime) {
                            lastApprovalTime = approvalTime;
                        }
                    }
                }

                const responseTime = (lastApprovalTime - submitTime) / (1000 * 60 * 60); // 小时
                return total + responseTime;
            }, 0);

            stats.avgResponseTime = Math.round(totalResponseTime / completedApps.length);
        }

        res.json({ success: true, stats });
    } catch (error) {
        console.error('获取统计信息失败:', error);
        res.status(500).json({ success: false, message: '获取统计信息失败' });
    }
});

// 网络连接状态检测API
app.get('/api/health', (req, res) => {
    const healthCheck = {
        status: 'OK',
        timestamp: new Date().toISOString(),
        uptime: process.uptime(),
        server: {
            url: SERVER_CONFIG.url,
            port: port,
            trustProxy: app.get('trust proxy')
        },
        network: {
            clientIP: req.ip,
            forwardedFor: req.headers['x-forwarded-for'],
            realIP: req.headers['x-real-ip'],
            userAgent: req.headers['user-agent']
        }
    };

    res.json(healthCheck);
});

// ==================== 设备管理API接口 ====================

// ==================== 厂区管理API接口 ====================

// 获取厂区列表
app.get('/api/factories', (req, res) => {
    try {
        const factories = deviceManager.getFactories();
        res.json({ success: true, factories });
    } catch (error) {
        console.error('获取厂区列表失败:', error);
        res.status(500).json({ success: false, message: '获取厂区列表失败' });
    }
});

// 添加厂区
app.post('/api/factories', (req, res) => {
    try {
        const { username, role } = req.body;

        // 权限检查：只有管理员可以添加厂区
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以添加厂区' });
        }

        const result = deviceManager.addFactory(req.body);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('添加厂区失败:', error);
        res.status(500).json({ success: false, message: '添加厂区失败' });
    }
});

// 更新厂区
app.put('/api/factories/:id', (req, res) => {
    try {
        const { username, role } = req.body;

        // 权限检查：只有管理员可以更新厂区
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以更新厂区' });
        }

        const result = deviceManager.updateFactory(req.params.id, req.body);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新厂区失败:', error);
        res.status(500).json({ success: false, message: '更新厂区失败' });
    }
});

// 删除厂区
app.delete('/api/factories/:id', (req, res) => {
    try {
        const { username, role } = req.body;

        // 权限检查：只有管理员可以删除厂区
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以删除厂区' });
        }

        const result = deviceManager.deleteFactory(req.params.id);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('删除厂区失败:', error);
        res.status(500).json({ success: false, message: '删除厂区失败' });
    }
});

// 获取单个厂区信息
app.get('/api/factories/:id', (req, res) => {
    try {
        const factory = deviceManager.getFactoryById(req.params.id);
        if (factory) {
            res.json({ success: true, factory });
        } else {
            res.status(404).json({ success: false, message: '厂区不存在' });
        }
    } catch (error) {
        console.error('获取厂区信息失败:', error);
        res.status(500).json({ success: false, message: '获取厂区信息失败' });
    }
});

// 获取设备列表
app.get('/api/devices', (req, res) => {
    try {
        const filters = {
            status: req.query.status,
            factory: req.query.factory,
            location: req.query.location,
            responsible: req.query.responsible,
            search: req.query.search
        };

        const devices = deviceManager.getAllDevices(filters);
        res.json({ success: true, devices });
    } catch (error) {
        console.error('获取设备列表失败:', error);
        res.status(500).json({ success: false, message: '获取设备列表失败' });
    }
});

// 获取设备详情
app.get('/api/devices/:id', (req, res) => {
    try {
        const device = deviceManager.getDeviceById(req.params.id);
        if (device) {
            res.json({ success: true, device });
        } else {
            res.status(404).json({ success: false, message: '设备不存在' });
        }
    } catch (error) {
        console.error('获取设备详情失败:', error);
        res.status(500).json({ success: false, message: '获取设备详情失败' });
    }
});

// 添加设备
app.post('/api/devices', (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员可以添加设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以添加设备' });
        }

        const result = deviceManager.addDevice(req.body);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('添加设备失败:', error);
        res.status(500).json({ success: false, message: '添加设备失败' });
    }
});

// 更新设备
app.put('/api/devices/:id', (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员可以更新设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以修改设备信息' });
        }

        const result = deviceManager.updateDevice(req.params.id, req.body);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新设备失败:', error);
        res.status(500).json({ success: false, message: '更新设备失败' });
    }
});

// 删除设备
app.delete('/api/devices/:id', (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员可以删除设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以删除设备' });
        }

        const result = deviceManager.deleteDevice(req.params.id);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('删除设备失败:', error);
        res.status(500).json({ success: false, message: '删除设备失败' });
    }
});

// 批量删除设备
app.delete('/api/devices', (req, res) => {
    try {
        const { ids, username, role, department } = req.body;

        // 权限检查：只有管理员可以批量删除设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以删除设备' });
        }

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({ success: false, message: '请提供要删除的设备ID列表' });
        }

        const result = deviceManager.deleteDevices(ids);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('批量删除设备失败:', error);
        res.status(500).json({ success: false, message: '批量删除设备失败' });
    }
});

// 获取设备统计信息
app.get('/api/devices/stats', (req, res) => {
    try {
        const stats = deviceManager.getDeviceStats();
        res.json({ success: true, stats });
    } catch (error) {
        console.error('获取设备统计失败:', error);
        res.status(500).json({ success: false, message: '获取设备统计失败' });
    }
});

// 导入设备数据
app.post('/api/devices/import', excelUpload.single('file'), async (req, res) => {
    try {
        const { username, skipDuplicates, updateExisting } = req.body;

        // 获取用户信息进行权限检查
        const users = getUsers();
        const user = users.find(u => u.username === username);

        if (!user || user.role !== 'admin') {
            return res.status(403).json({
                success: false,
                message: '权限不足，只有管理员可以导入设备数据'
            });
        }

        if (!req.file) {
            return res.status(400).json({
                success: false,
                message: '请选择要导入的Excel文件'
            });
        }

        // 验证文件类型
        const validMimeTypes = [
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
            'application/vnd.ms-excel'
        ];

        if (!validMimeTypes.includes(req.file.mimetype)) {
            return res.status(400).json({
                success: false,
                message: '文件格式不支持，请上传Excel文件(.xlsx或.xls)'
            });
        }

        // 调用设备管理器的导入方法
        const result = await deviceManager.importDevicesFromExcel(
            req.file.path,
            {
                skipDuplicates: skipDuplicates === 'true',
                updateExisting: updateExisting === 'true',
                username: username
            }
        );

        // 清理上传的临时文件
        try {
            const fs = require('fs');
            if (fs.existsSync(req.file.path)) {
                fs.unlinkSync(req.file.path);
            }
        } catch (cleanupError) {
            console.error('清理临时文件失败:', cleanupError);
        }

        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('导入设备数据失败:', error);
        res.status(500).json({
            success: false,
            message: '导入设备数据失败: ' + error.message
        });
    }
});

// ==================== 维修保养记录API接口 ====================

// 获取维修保养记录列表
app.get('/api/maintenance-records', (req, res) => {
    try {
        const filters = {
            deviceId: req.query.deviceId,
            factory: req.query.factory,
            type: req.query.type,
            operator: req.query.operator,
            startDate: req.query.startDate,
            endDate: req.query.endDate,
            search: req.query.search
        };

        const records = maintenanceManager.getAllRecords(filters);
        res.json({ success: true, records });
    } catch (error) {
        console.error('获取维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '获取维修保养记录失败' });
    }
});

// 获取维修保养记录详情
app.get('/api/maintenance-records/:id', (req, res) => {
    try {
        const record = maintenanceManager.getRecordById(req.params.id);
        if (record) {
            res.json({ success: true, record });
        } else {
            res.status(404).json({ success: false, message: '记录不存在' });
        }
    } catch (error) {
        console.error('获取记录详情失败:', error);
        res.status(500).json({ success: false, message: '获取记录详情失败' });
    }
});

// 添加维修保养记录
app.post('/api/maintenance-records', upload.array('attachments', 10), (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员和机电部用户可以添加维修保养记录
        if (role !== 'admin' && department !== '机电部') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员和机电部用户可以添加维修保养记录' });
        }

        const recordData = {
            ...req.body,
            operator: username, // 设置操作人员为当前登录用户
            attachments: req.files ? req.files.map(file => ({
                filename: file.filename,
                originalname: file.originalname,
                size: file.size,
                uploadTime: new Date().toISOString()
            })) : []
        };

        const result = maintenanceManager.addRecord(recordData);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('添加维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '添加维修保养记录失败' });
    }
});

// 更新维修保养记录
app.put('/api/maintenance-records/:id', upload.array('attachments', 10), (req, res) => {
    try {
        const { username, role, department } = req.body;
        const recordId = req.params.id;

        // 获取现有记录以检查权限
        const existingRecord = maintenanceManager.getRecordById(recordId);
        if (!existingRecord) {
            return res.status(404).json({ success: false, message: '记录不存在' });
        }

        // 权限检查：管理员可以修改所有记录，机电部用户只能修改自己创建的记录
        if (role !== 'admin') {
            if (department !== '机电部') {
                return res.status(403).json({ success: false, message: '权限不足，只有管理员和机电部用户可以修改维修保养记录' });
            }
            if (existingRecord.operator !== username) {
                return res.status(403).json({ success: false, message: '权限不足，您只能修改自己创建的记录' });
            }
        }

        const recordData = {
            ...req.body,
            attachments: req.files ? req.files.map(file => ({
                filename: file.filename,
                originalname: file.originalname,
                size: file.size,
                uploadTime: new Date().toISOString()
            })) : undefined
        };

        const result = maintenanceManager.updateRecord(recordId, recordData);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '更新维修保养记录失败' });
    }
});

// 删除维修保养记录
app.delete('/api/maintenance-records/:id', (req, res) => {
    try {
        const { username, role, department } = req.body;
        const recordId = req.params.id;

        // 获取现有记录以检查权限
        const existingRecord = maintenanceManager.getRecordById(recordId);
        if (!existingRecord) {
            return res.status(404).json({ success: false, message: '记录不存在' });
        }

        // 权限检查：管理员可以删除所有记录，机电部用户只能删除自己创建的记录
        if (role !== 'admin') {
            if (department !== '机电部') {
                return res.status(403).json({ success: false, message: '权限不足，只有管理员和机电部用户可以删除维修保养记录' });
            }
            if (existingRecord.operator !== username) {
                return res.status(403).json({ success: false, message: '权限不足，您只能删除自己创建的记录' });
            }
        }

        const result = maintenanceManager.deleteRecord(recordId);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('删除维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '删除维修保养记录失败' });
    }
});

// 批量删除维修保养记录
app.delete('/api/maintenance-records', (req, res) => {
    try {
        const { ids, username, role, department } = req.body;

        // 权限检查：只有管理员和机电部用户可以批量删除记录
        if (role !== 'admin' && department !== '机电部') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员和机电部用户可以删除维修保养记录' });
        }

        if (!ids || !Array.isArray(ids) || ids.length === 0) {
            return res.status(400).json({ success: false, message: '请提供要删除的记录ID列表' });
        }

        // 对于机电部用户，需要检查每个记录是否是自己创建的
        if (role !== 'admin' && department === '机电部') {
            for (const id of ids) {
                const record = maintenanceManager.getRecordById(id);
                if (!record) {
                    return res.status(404).json({ success: false, message: `记录 ${id} 不存在` });
                }
                if (record.operator !== username) {
                    return res.status(403).json({ success: false, message: '权限不足，您只能删除自己创建的记录' });
                }
            }
        }

        const result = maintenanceManager.deleteRecords(ids);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('批量删除维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '批量删除维修保养记录失败' });
    }
});

// 生成维修保养记录编号
app.post('/api/maintenance-records/generate-code', (req, res) => {
    try {
        const { factoryId, type } = req.body;

        if (!factoryId || !type) {
            return res.status(400).json({ success: false, message: '厂区ID和记录类型为必填项' });
        }

        const recordCode = maintenanceManager.generateRecordCode(factoryId, type);
        res.json({ success: true, recordCode });
    } catch (error) {
        console.error('生成记录编号失败:', error);
        res.status(500).json({ success: false, message: '生成记录编号失败' });
    }
});

// 获取维修保养记录统计信息
app.get('/api/maintenance-records/stats', (req, res) => {
    try {
        const stats = maintenanceManager.getRecordStats();
        res.json({ success: true, stats });
    } catch (error) {
        console.error('获取记录统计失败:', error);
        res.status(500).json({ success: false, message: '获取记录统计失败' });
    }
});

// 获取维修保养模板
app.get('/api/maintenance-templates', (req, res) => {
    try {
        const templates = maintenanceManager.getTemplates();
        res.json({ success: true, templates });
    } catch (error) {
        console.error('获取模板失败:', error);
        res.status(500).json({ success: false, message: '获取模板失败' });
    }
});

// ==================== 导出功能API接口 ====================

// 导出设备列表
app.post('/api/export/devices', async (req, res) => {
    try {
        const { filters, filename } = req.body;
        const devices = deviceManager.getAllDevices(filters || {});

        const result = await exportService.exportDevicesToExcelBuffer(devices, filename);

        if (result.success) {
            // 设置响应头，直接下载Excel文件
            const exportFilename = filename || `设备列表_${new Date().toISOString().slice(0, 10)}.xlsx`;
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(exportFilename)}"`);
            res.send(result.buffer);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('导出设备列表失败:', error);
        res.status(500).json({ success: false, message: '导出设备列表失败' });
    }
});

// 导出维修保养记录
app.post('/api/export/maintenance', async (req, res) => {
    try {
        const { filters, filename } = req.body;
        const records = maintenanceManager.getAllRecords(filters || {});
        const devices = deviceManager.getAllDevices();

        const result = await exportService.exportMaintenanceToExcelBuffer(records, devices, filename);

        if (result.success) {
            // 设置响应头，直接下载Excel文件
            const exportFilename = filename || `维修保养记录_${new Date().toISOString().slice(0, 10)}.xlsx`;
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(exportFilename)}"`);
            res.send(result.buffer);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('导出维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '导出维修保养记录失败' });
    }
});



// 导出统计报告
app.post('/api/export/stats', async (req, res) => {
    try {
        const { filename } = req.body;
        const deviceStats = deviceManager.getDeviceStats();
        const recordStats = maintenanceManager.getRecordStats();
        const devices = deviceManager.getAllDevices();
        const records = maintenanceManager.getAllRecords();

        const stats = {
            device: deviceStats,
            record: recordStats
        };

        const result = await exportService.exportDeviceStatsToExcelBuffer(stats, devices, records, filename);

        if (result.success) {
            // 设置响应头，直接下载Excel文件
            const exportFilename = filename || `设备统计报告_${new Date().toISOString().slice(0, 10)}.xlsx`;
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(exportFilename)}"`);
            res.send(result.buffer);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('导出统计报告失败:', error);
        res.status(500).json({ success: false, message: '导出统计报告失败' });
    }
});

// 文件下载接口
app.get('/download/:filename', (req, res) => {
    try {
        const filename = req.params.filename;
        const filePath = path.join(__dirname, '..', 'temp', filename);

        if (!fs.existsSync(filePath)) {
            return res.status(404).json({ success: false, message: '文件不存在' });
        }

        // 设置响应头
        res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(filename)}"`);
        res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

        // 发送文件
        const fileStream = fs.createReadStream(filePath);
        fileStream.pipe(res);

        // 文件发送完成后删除临时文件
        fileStream.on('end', () => {
            setTimeout(() => {
                try {
                    if (fs.existsSync(filePath)) {
                        fs.unlinkSync(filePath);
                    }
                } catch (error) {
                    console.error('删除临时文件失败:', error);
                }
            }, 5000); // 5秒后删除
        });

    } catch (error) {
        console.error('文件下载失败:', error);
        res.status(500).json({ success: false, message: '文件下载失败' });
    }
});

// ==================== 设备健康度评估API ====================

// 获取所有设备健康度概览 (必须在 :deviceId 路由之前)
app.get('/api/device-health/overview', (req, res) => {
    try {
        const overview = deviceHealth.getAllDevicesHealthOverview();

        res.json({
            success: true,
            data: overview
        });
    } catch (error) {
        console.error('获取设备健康度概览失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备健康度概览失败: ' + error.message
        });
    }
});

// 获取设备健康度评分
app.get('/api/device-health/:deviceId', (req, res) => {
    try {
        const { deviceId } = req.params;
        const healthData = deviceHealth.calculateHealthScore(deviceId);

        res.json({
            success: true,
            data: healthData
        });
    } catch (error) {
        console.error('获取设备健康度失败:', error);
        res.status(500).json({
            success: false,
            message: '获取设备健康度失败: ' + error.message
        });
    }
});

// 预测设备故障时间
app.get('/api/device-health/:deviceId/prediction', (req, res) => {
    try {
        const { deviceId } = req.params;
        const prediction = deviceHealth.predictNextFailure(deviceId);

        res.json({
            success: true,
            data: prediction
        });
    } catch (error) {
        console.error('预测设备故障失败:', error);
        res.status(500).json({
            success: false,
            message: '预测设备故障失败: ' + error.message
        });
    }
});

// 生成设备维护计划
app.get('/api/device-health/:deviceId/maintenance-plan', (req, res) => {
    try {
        const { deviceId } = req.params;
        const plan = deviceHealth.generateMaintenancePlan(deviceId);

        res.json({
            success: true,
            data: plan
        });
    } catch (error) {
        console.error('生成维护计划失败:', error);
        res.status(500).json({
            success: false,
            message: '生成维护计划失败: ' + error.message
        });
    }
});







// Dashboard数据处理辅助函数

// 获取设备数据
function getDevices() {
    try {
        return deviceManager.getAllDevices();
    } catch (error) {
        console.error('获取设备数据失败:', error);
        return [];
    }
}

// 计算申请趋势
function calculateApplicationTrend(applications) {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const thisMonth = applications.filter(app => new Date(app.date) >= lastMonth);
    const previousMonth = applications.filter(app => {
        const appDate = new Date(app.date);
        return appDate >= new Date(now.getFullYear(), now.getMonth() - 2, now.getDate()) &&
               appDate < lastMonth;
    });

    if (previousMonth.length === 0) return 0;
    return Math.round(((thisMonth.length - previousMonth.length) / previousMonth.length) * 100);
}

// 计算设备趋势
function calculateDeviceTrend(devices) {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());

    // 统计本月新增的设备（基于创建时间或进厂日期）
    const thisMonthDevices = devices.filter(device => {
        const deviceDate = new Date(device.entryDate || device.createTime);
        return deviceDate >= lastMonth;
    });

    // 统计上月新增的设备
    const previousMonth = devices.filter(device => {
        const deviceDate = new Date(device.entryDate || device.createTime);
        const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, now.getDate());
        return deviceDate >= twoMonthsAgo && deviceDate < lastMonth;
    });

    if (previousMonth.length === 0) {
        // 如果上月没有新增设备，但本月有新增，则显示100%增长
        return thisMonthDevices.length > 0 ? 100 : 0;
    }

    return Math.round(((thisMonthDevices.length - previousMonth.length) / previousMonth.length) * 100);
}

// 计算审批效率趋势
function calculateEfficiencyTrend(applications, role, username) {
    const now = new Date();
    const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, now.getDate());
    const twoMonthsAgo = new Date(now.getFullYear(), now.getMonth() - 2, now.getDate());

    // 获取本月的申请
    const thisMonthApps = applications.filter(app => new Date(app.date) >= lastMonth);
    // 获取上月的申请
    const previousMonthApps = applications.filter(app => {
        const appDate = new Date(app.date);
        return appDate >= twoMonthsAgo && appDate < lastMonth;
    });

    // 如果没有足够的历史数据，返回0
    if (previousMonthApps.length === 0) return 0;

    // 计算本月和上月的审批效率
    const thisMonthEfficiency = calculateApprovalEfficiency(thisMonthApps, role, username);
    const previousMonthEfficiency = calculateApprovalEfficiency(previousMonthApps, role, username);

    // 计算效率变化百分比
    if (previousMonthEfficiency === 0) return 0;

    const efficiencyChange = thisMonthEfficiency - previousMonthEfficiency;
    return Math.round(efficiencyChange); // 直接返回效率点数的变化，而不是百分比变化
}

// 计算审批效率
function calculateApprovalEfficiency(applications, role, username = null) {
    if (role === 'user' || role === 'readonly') return 100;

    // 管理员查看系统整体审批效率
    if (role === 'admin') {
        return calculateSystemApprovalEfficiency(applications);
    }

    // 审批人员查看个人审批效率
    return calculatePersonalApprovalEfficiency(applications, role, username);
}

// 计算系统整体审批效率
function calculateSystemApprovalEfficiency(applications) {
    if (applications.length === 0) return 100;

    // 统计申请的最终状态
    let approvedCount = 0;  // 已通过数量
    let rejectedCount = 0;  // 已拒绝数量
    let totalCount = applications.length;  // 总申请数量

    applications.forEach(app => {
        // 统计已通过的申请
        if (app.status === '已通过' || app.status === '通过申请') {
            approvedCount++;
        }
        // 统计已拒绝的申请
        else if (app.status === '已拒绝' || app.status === '拒绝申请') {
            rejectedCount++;
        }
    });

    // 计算系统审批效率：(已通过数量 + 已拒绝数量) / 总申请数量 × 100%
    const completedCount = approvedCount + rejectedCount;
    const efficiency = Math.round((completedCount / totalCount) * 100);

    return efficiency;
}

// 计算个人审批效率
function calculatePersonalApprovalEfficiency(applications, role, username) {
    if (!username) return 100;

    let totalPersonalApprovals = 0;
    let completedPersonalApprovals = 0;

    applications.forEach(app => {
        if (!app.approvals) return;

        let hasPersonalApproval = false;
        let isCompleted = false;

        // 根据角色检查个人审批记录
        if (role === 'director' && app.approvals.directors && app.approvals.directors[username]) {
            // 厂长：检查该用户是否在厂长审批列表中
            hasPersonalApproval = true;
            const approval = app.approvals.directors[username];
            // 对于已拒绝的申请，如果厂长状态是pending但申请状态是已拒绝，说明是厂长拒绝的
            if (app.status === '已拒绝' && approval.status === 'pending') {
                isCompleted = true; // 拒绝也算完成
            } else {
                isCompleted = approval.status === 'approved' || approval.status === 'rejected';
            }
        } else if (role === 'chief' && app.approvals.chief) {
            // 总监：检查是否是当前用户审批的
            if (app.approvals.chief.approverUsername === username) {
                hasPersonalApproval = true;
                isCompleted = app.approvals.chief.status === 'approved' || app.approvals.chief.status === 'rejected';
            } else if (!app.approvals.chief.approverUsername) {
                // 兼容旧数据：如果没有记录审批人，则认为是该总监用户参与的审批
                // 但需要确保申请确实经过了总监审批阶段
                if (app.status !== '待厂长审核' && app.status !== '申请中') {
                    hasPersonalApproval = true;
                    // 对于已拒绝的申请，如果总监状态是pending但申请状态是已拒绝，说明是总监拒绝的
                    if (app.status === '已拒绝' && app.approvals.chief.status === 'pending') {
                        isCompleted = true; // 拒绝也算完成
                    } else {
                        isCompleted = app.approvals.chief.status === 'approved' || app.approvals.chief.status === 'rejected';
                    }
                }
            }
        } else if (role === 'manager' && app.approvals.managers && app.approvals.managers[username]) {
            // 经理：检查该用户是否在经理审批列表中
            hasPersonalApproval = true;
            const approval = app.approvals.managers[username];
            // 对于已拒绝的申请，如果经理状态是pending但申请状态是已拒绝，说明是经理拒绝的
            if (app.status === '已拒绝' && approval.status === 'pending') {
                isCompleted = true; // 拒绝也算完成
            } else {
                isCompleted = approval.status === 'approved' || approval.status === 'rejected';
            }
        } else if (role === 'ceo' && app.approvals.ceo) {
            // CEO：检查是否是当前用户审批的
            if (app.approvals.ceo.approverUsername === username) {
                hasPersonalApproval = true;
                isCompleted = app.approvals.ceo.status === 'approved' || app.approvals.ceo.status === 'rejected';
            } else if (!app.approvals.ceo.approverUsername) {
                // 兼容旧数据：如果没有记录审批人，则认为是该CEO用户参与的审批
                // 但需要确保申请确实经过了CEO审批阶段
                if (app.status === '待CEO审批' || app.status === '已通过' || app.status === '已拒绝') {
                    hasPersonalApproval = true;
                    // 对于已拒绝的申请，如果CEO状态是pending但申请状态是已拒绝，说明是CEO拒绝的
                    if (app.status === '已拒绝' && app.approvals.ceo.status === 'pending') {
                        isCompleted = true; // 拒绝也算完成
                    }
                    // 对于已通过的申请，如果CEO状态是pending但申请状态是已通过，说明是CEO通过的
                    else if (app.status === '已通过' && app.approvals.ceo.status === 'pending') {
                        isCompleted = true; // 通过也算完成
                    } else {
                        isCompleted = app.approvals.ceo.status === 'approved' || app.approvals.ceo.status === 'rejected';
                    }
                }
            }
        }

        if (hasPersonalApproval) {
            totalPersonalApprovals++;
            if (isCompleted) {
                completedPersonalApprovals++;
            }
        }
    });

    if (totalPersonalApprovals === 0) return 100;
    return Math.round((completedPersonalApprovals / totalPersonalApprovals) * 100);
}

// 生成申请趋势图表数据（按月份统计）
function generateApplicationTrendData(applications) {
    const last12Months = [];
    const now = new Date();

    for (let i = 11; i >= 0; i--) {
        const date = new Date(now.getFullYear(), now.getMonth() - i, 1);
        const year = date.getFullYear();
        const month = date.getMonth() + 1; // 月份从0开始，需要+1
        const monthStr = `${year}-${month.toString().padStart(2, '0')}`;

        // 统计该月的申请数量
        const monthApps = applications.filter(app => {
            const appDate = new Date(app.date);
            return appDate.getFullYear() === year && appDate.getMonth() + 1 === month;
        });

        let count = monthApps.length;

        // 如果没有真实数据，生成一些模拟数据用于演示
        if (applications.length === 0 && i < 6) {
            count = Math.floor(Math.random() * 20) + 5; // 5-24的随机数
        }

        last12Months.push({
            date: monthStr,
            month: `${year}年${month}月`,
            count: count
        });
    }

    return last12Months;
}

// 生成设备健康度分布数据
function generateDeviceHealthData(devices) {
    // 如果没有设备数据，返回演示数据
    if (devices.length === 0) {
        return {
            excellent: 25,
            good: 18,
            fair: 12,
            poor: 5,
            critical: 2
        };
    }

    // 检查 deviceHealth 对象是否可用
    if (!deviceHealth || typeof deviceHealth.calculateHealthScore !== 'function') {
        console.warn('设备健康度模块未初始化，使用固定比例计算');
        const total = devices.length;
        return {
            excellent: Math.floor(total * 0.35),
            good: Math.floor(total * 0.25),
            fair: Math.floor(total * 0.20),
            poor: Math.floor(total * 0.15),
            critical: Math.max(0, total - Math.floor(total * 0.35) - Math.floor(total * 0.25) - Math.floor(total * 0.20) - Math.floor(total * 0.15))
        };
    }

    // 使用真实的设备健康度评分进行统计
    try {
        const healthStats = {
            excellent: 0,
            good: 0,
            fair: 0,
            poor: 0,
            critical: 0
        };

        // 遍历所有设备，计算真实的健康度评分
        devices.forEach(device => {
            try {
                const healthData = deviceHealth.calculateHealthScore(device.id);
                const score = healthData.score;

                // 根据评分分类（与设备健康度评估页面保持一致）
                if (score >= 90) {
                    healthStats.excellent++;
                } else if (score >= 80) {
                    healthStats.good++;
                } else if (score >= 70) {
                    healthStats.fair++;
                } else if (score >= 60) {
                    healthStats.poor++;
                } else {
                    healthStats.critical++;
                }
            } catch (error) {
                console.warn(`计算设备 ${device.id} 健康度失败:`, error);
                // 如果计算失败，默认归类为"一般"
                healthStats.fair++;
            }
        });

        return healthStats;
    } catch (error) {
        console.error('生成设备健康度数据失败:', error);
        // 如果出错，回退到固定比例计算
        const total = devices.length;
        const excellent = Math.floor(total * 0.35);
        const good = Math.floor(total * 0.25);
        const fair = Math.floor(total * 0.20);
        const poor = Math.floor(total * 0.15);
        const critical = Math.max(0, total - excellent - good - fair - poor);

        return {
            excellent,
            good,
            fair,
            poor,
            critical
        };
    }
}

// 生成部门统计数据
function generateDepartmentStatsData(applications) {
    const departments = {};
    applications.forEach(app => {
        const dept = app.department || '未知部门';
        departments[dept] = (departments[dept] || 0) + 1;
    });

    return Object.entries(departments).map(([name, count]) => ({
        department: name,
        count: count
    }));
}

// 生成待办事项数据
function generateTodosByRole(applications, username, role, users, devices, maintenanceRecords, department) {
    const todos = [];
    const now = new Date();

    if (role === 'admin') {
        // 管理员待办事项
        generateAdminTodos(todos, applications, users, devices, maintenanceRecords, now);
    } else if (role === 'director') {
        // 厂长待办事项
        generateDirectorTodos(todos, applications, username, devices, maintenanceRecords, now);
    } else if (role === 'chief') {
        // 总监待办事项
        generateChiefTodos(todos, applications, devices, maintenanceRecords, now);
    } else if (role === 'manager') {
        // 经理待办事项
        generateManagerTodos(todos, applications, username, devices, maintenanceRecords, now);
    } else if (role === 'ceo') {
        // CEO待办事项
        generateCeoTodos(todos, applications, devices, maintenanceRecords, now);
    } else if (role === 'user') {
        // 普通用户待办事项
        generateUserTodos(todos, applications, username, devices, maintenanceRecords, department, now);
    } else if (role === 'readonly') {
        // 只读用户待办事项（主要是通知类）
        generateReadonlyTodos(todos, applications, devices, maintenanceRecords, now);
    }

    // 按优先级和时间排序
    return todos.sort((a, b) => {
        const priorityOrder = { 'high': 0, 'medium': 1, 'normal': 2, 'low': 3 };
        const priorityDiff = (priorityOrder[a.priority] || 2) - (priorityOrder[b.priority] || 2);
        if (priorityDiff !== 0) return priorityDiff;
        return new Date(b.time) - new Date(a.time);
    });
}

// 生成管理员待办事项
function generateAdminTodos(todos, applications, users, devices, maintenanceRecords, now) {
    // 1. 系统管理相关待办事项
    const pendingApps = applications.filter(app =>
        app.status === '待厂长审核' ||
        app.status === '待总监审批' ||
        app.status === '待经理审批' ||
        app.status === '待CEO审批'
    );

    if (pendingApps.length > 0) {
        todos.push({
            id: 'admin-pending-overview',
            text: `系统中有 ${pendingApps.length} 个申请等待审批`,
            time: now.getTime(),
            priority: pendingApps.length > 10 ? 'high' : 'medium',
            icon: '📊',
            iconColor: pendingApps.length > 10 ? '#dc2626' : '#f59e0b',
            actionUrl: 'section:pendingApproval'
        });
    }

    // 2. 检查是否有长时间未处理的申请
    const urgentApps = pendingApps.filter(app => {
        const submitTime = new Date(app.id);
        const waitingHours = (now - submitTime) / (1000 * 60 * 60);
        return waitingHours >= 48;
    });

    if (urgentApps.length > 0) {
        todos.push({
            id: 'admin-urgent-apps',
            text: `有 ${urgentApps.length} 个申请已等待超过48小时`,
            time: now.getTime(),
            priority: 'high',
            icon: '🚨',
            iconColor: '#dc2626',
            actionUrl: 'section:pendingApproval'
        });
    }

    // 3. 设备健康度警告
    if (devices && devices.length > 0) {
        const unhealthyDevices = devices.filter(device => {
            const healthScore = device.healthScore || 100;
            return healthScore < 60;
        });

        if (unhealthyDevices.length > 0) {
            const criticalCount = unhealthyDevices.filter(device => device.healthScore < 30).length;

            todos.push({
                id: 'admin-unhealthy-devices',
                text: `有 ${unhealthyDevices.length} 台设备健康度低于60分${criticalCount > 0 ? `（${criticalCount}台严重）` : ''}`,
                time: now.getTime(),
                priority: criticalCount > 0 ? 'high' : 'medium',
                icon: '⚠️',
                iconColor: criticalCount > 0 ? '#dc2626' : '#f59e0b',
                actionUrl: 'page:device-management'
            });
        }

        // 4. 设备保养提醒
        const needMaintenanceDevices = devices.filter(device => {
            if (!device.lastMaintenanceDate) return true;
            const lastMaintenance = new Date(device.lastMaintenanceDate);
            const daysSinceMaintenance = (now - lastMaintenance) / (1000 * 60 * 60 * 24);
            return daysSinceMaintenance > 90; // 90天未保养
        });

        if (needMaintenanceDevices.length > 0) {
            todos.push({
                id: 'admin-maintenance-due',
                text: `有 ${needMaintenanceDevices.length} 台设备需要保养维护`,
                time: now.getTime(),
                priority: 'medium',
                icon: '🔧',
                iconColor: '#f59e0b',
                actionUrl: 'page:device-management'
            });
        }
    }

    // 5. 用户管理提醒
    const inactiveUsers = users.filter(user => {
        // 这里可以添加检查用户最后登录时间的逻辑
        return false; // 暂时不实现
    });

    if (inactiveUsers.length > 0) {
        todos.push({
            id: 'admin-inactive-users',
            text: `有 ${inactiveUsers.length} 个用户长时间未登录`,
            time: now.getTime(),
            priority: 'low',
            icon: '👥',
            iconColor: '#6b7280',
            actionUrl: 'section:manageUsers'
        });
    }
}

// 生成厂长待办事项
function generateDirectorTodos(todos, applications, username, devices, maintenanceRecords, now) {
    // 1. 待审核的申请
    const pendingApps = applications.filter(app => {
        if (app.status !== '待厂长审核') return false;

        // 检查是否分配给当前厂长
        if (app.approvals && app.approvals.directors) {
            return app.approvals.directors[username] &&
                   app.approvals.directors[username].status === 'pending';
        }
        return true; // 如果没有指定厂长，所有厂长都可以审批
    });

    if (pendingApps.length > 0) {
        const urgentCount = pendingApps.filter(app => app.priority === 'high').length;

        todos.push({
            id: 'director-pending-approval',
            text: `您有 ${pendingApps.length} 个申请待审核${urgentCount > 0 ? `（${urgentCount}个紧急）` : ''}`,
            time: now.getTime(),
            priority: urgentCount > 0 ? 'high' : 'medium',
            icon: '⏳',
            iconColor: urgentCount > 0 ? '#dc2626' : '#f59e0b',
            actionUrl: 'section:pendingApproval'
        });
    }

    // 2. 设备健康度关注
    if (devices && devices.length > 0) {
        const criticalDevices = devices.filter(device => {
            const healthScore = device.healthScore || 100;
            return healthScore < 30;
        });

        if (criticalDevices.length > 0) {
            todos.push({
                id: 'director-critical-devices',
                text: `有 ${criticalDevices.length} 台设备健康度严重偏低，需要立即处理`,
                time: now.getTime(),
                priority: 'high',
                icon: '🚨',
                iconColor: '#dc2626',
                actionUrl: 'page:device-management'
            });
        }
    }

    // 3. 自己提交的申请状态更新
    const myApps = applications.filter(app => app.username === username);
    const recentUpdates = myApps.filter(app => {
        const updateTime = new Date(app.lastUpdateTime || app.date);
        const hoursSinceUpdate = (now - updateTime) / (1000 * 60 * 60);
        return hoursSinceUpdate <= 24 && app.status !== '待厂长审核';
    });

    recentUpdates.forEach(app => {
        let priority = 'normal';
        let iconColor = '#3b82f6';
        let icon = '📄';

        if (app.status === '已通过') {
            priority = 'low';
            iconColor = '#10b981';
            icon = '✅';
        } else if (app.status === '已拒绝') {
            priority = 'medium';
            iconColor = '#dc2626';
            icon = '❌';
        }

        todos.push({
            id: `director-app-update-${app.id}`,
            text: `您的申请 ${app.applicationCode || app.id.slice(-6)} 状态已更新：${app.status}`,
            time: new Date(app.lastUpdateTime || app.date).getTime(),
            priority: priority,
            icon: icon,
            iconColor: iconColor,
            actionUrl: 'section:history'
        });
    });
}

// 生成最近活动数据
function generateRecentActivities(applications, maintenanceRecords, devices, username, role) {
    const activities = [];
    const now = new Date();
    const sevenDaysAgo = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);

    // 1. 处理申请活动
    const recentApps = applications
        .filter(app => {
            // 使用申请ID（提交时间戳）而不是用户选择的申请日期
            const submitTime = new Date(app.id);
            return submitTime >= sevenDaysAgo;
        })
        .sort((a, b) => b.id - a.id); // 按提交时间排序

    recentApps.forEach(app => {
        let activityText = '';
        let iconColor = '#3b82f6';
        let icon = '📄';
        let activityType = 'application';

        if (app.status === '已通过' || app.status === '通过申请') {
            activityText = `申请 ${app.applicationCode || app.id.slice(-6)} 已通过审批`;
            iconColor = '#10b981';
            icon = '✅';
        } else if (app.status === '已拒绝' || app.status === '拒绝申请') {
            activityText = `申请 ${app.applicationCode || app.id.slice(-6)} 被拒绝`;
            iconColor = '#ef4444';
            icon = '❌';
        } else if (app.status.includes('待')) {
            activityText = `申请 ${app.applicationCode || app.id.slice(-6)} 等待审批`;
            iconColor = '#f59e0b';
            icon = '⏳';
        } else {
            activityText = `${app.username || '用户'} 提交了新申请`;
            iconColor = '#3b82f6';
            icon = '📄';
        }

        activities.push({
            text: activityText,
            time: app.id, // 使用申请ID作为提交时间戳
            icon: icon,
            iconColor: iconColor,
            type: activityType,
            relatedId: app.id
        });
    });

    // 2. 处理维修保养记录活动
    if (maintenanceRecords && maintenanceRecords.length > 0) {
        const recentMaintenance = maintenanceRecords
            .filter(record => {
                // 使用记录创建时间而不是用户选择的维修保养日期
                const createTime = new Date(record.createTime || record.id);
                return createTime >= sevenDaysAgo;
            })
            .sort((a, b) => new Date(b.createTime || b.id) - new Date(a.createTime || a.id));

        recentMaintenance.forEach(record => {
            const device = devices.find(d => d.id === record.deviceId);
            const deviceDisplay = device ? `${device.deviceCode} - ${device.deviceName}` : `设备${record.deviceId}`;

            let activityText = '';
            let iconColor = '#8b5cf6';
            let icon = '🔧';

            if (record.type === '维修') {
                activityText = `${record.operator} 完成了 ${deviceDisplay} 的维修工作`;
                iconColor = '#ef4444';
                icon = '🔧';
            } else if (record.type === '保养') {
                activityText = `${record.operator} 完成了 ${deviceDisplay} 的保养工作`;
                iconColor = '#10b981';
                icon = '🛠️';
            }

            activities.push({
                text: activityText,
                time: new Date(record.createTime || record.id).getTime(),
                icon: icon,
                iconColor: iconColor,
                type: 'maintenance',
                relatedId: record.id
            });
        });
    }

    // 3. 处理设备相关活动（新增设备）
    if (devices && devices.length > 0) {
        const recentDevices = devices
            .filter(device => {
                // 使用设备记录创建时间来筛选最近添加的设备记录
                const createTime = new Date(device.createTime || device.id);
                return createTime >= sevenDaysAgo;
            })
            .sort((a, b) => new Date(b.createTime || b.id) - new Date(a.createTime || a.id));

        recentDevices.forEach(device => {
            const activityText = `新设备 ${device.deviceCode} - ${device.deviceName} 已入库`;

            activities.push({
                text: activityText,
                time: new Date(device.createTime || device.id).getTime(),
                icon: '📦',
                iconColor: '#3b82f6',
                type: 'device',
                relatedId: device.id
            });
        });
    }

    // 4. 按时间排序并限制数量
    return activities
        .sort((a, b) => b.time - a.time)
        .slice(0, 15); // 最多显示15条活动
}

// 生成总监待办事项
function generateChiefTodos(todos, applications, devices, maintenanceRecords, now) {
    // 1. 待审核的申请
    const pendingApps = applications.filter(app => app.status === '待总监审批');

    if (pendingApps.length > 0) {
        const urgentCount = pendingApps.filter(app => app.priority === 'high').length;

        todos.push({
            id: 'chief-pending-approval',
            text: `您有 ${pendingApps.length} 个申请待审核${urgentCount > 0 ? `（${urgentCount}个紧急）` : ''}`,
            time: now.getTime(),
            priority: urgentCount > 0 ? 'high' : 'medium',
            icon: '⏳',
            iconColor: urgentCount > 0 ? '#dc2626' : '#f59e0b',
            actionUrl: 'section:pendingApproval'
        });
    }

    // 2. 需要选择经理的申请
    const needManagerSelection = pendingApps.filter(app => {
        return app.approvals && app.approvals.chief &&
               app.approvals.chief.status === 'approved' &&
               (!app.approvals.managers || Object.keys(app.approvals.managers).length === 0);
    });

    if (needManagerSelection.length > 0) {
        todos.push({
            id: 'chief-select-managers',
            text: `有 ${needManagerSelection.length} 个申请需要您选择审批经理`,
            time: now.getTime(),
            priority: 'medium',
            icon: '👥',
            iconColor: '#f59e0b',
            actionUrl: 'section:pendingApproval'
        });
    }

    // 3. 设备保养计划监督
    if (devices && devices.length > 0) {
        const overdueMaintenanceDevices = devices.filter(device => {
            if (!device.lastMaintenanceDate) return true;
            const lastMaintenance = new Date(device.lastMaintenanceDate);
            const daysSinceMaintenance = (now - lastMaintenance) / (1000 * 60 * 60 * 24);
            return daysSinceMaintenance > 120; // 120天未保养
        });

        if (overdueMaintenanceDevices.length > 0) {
            todos.push({
                id: 'chief-overdue-maintenance',
                text: `有 ${overdueMaintenanceDevices.length} 台设备保养严重超期，需要督促处理`,
                time: now.getTime(),
                priority: 'medium',
                icon: '📋',
                iconColor: '#f59e0b',
                actionUrl: 'page:device-management'
            });
        }
    }
}

// 生成经理待办事项
function generateManagerTodos(todos, applications, username, devices, maintenanceRecords, now) {
    // 1. 待审核的申请
    const pendingApps = applications.filter(app => {
        if (app.status !== '待经理审批') return false;

        // 检查是否分配给当前经理
        if (app.approvals && app.approvals.managers) {
            return app.approvals.managers[username] &&
                   app.approvals.managers[username].status === 'pending';
        }
        return false;
    });

    if (pendingApps.length > 0) {
        const urgentCount = pendingApps.filter(app => app.priority === 'high').length;

        todos.push({
            id: 'manager-pending-approval',
            text: `您有 ${pendingApps.length} 个申请待审核${urgentCount > 0 ? `（${urgentCount}个紧急）` : ''}`,
            time: now.getTime(),
            priority: urgentCount > 0 ? 'high' : 'medium',
            icon: '⏳',
            iconColor: urgentCount > 0 ? '#dc2626' : '#f59e0b',
            actionUrl: 'section:pendingApproval'
        });
    }

    // 2. 设备状态关注（经理级别关注中等健康度设备）
    if (devices && devices.length > 0) {
        const moderateRiskDevices = devices.filter(device => {
            const healthScore = device.healthScore || 100;
            return healthScore >= 30 && healthScore < 60;
        });

        if (moderateRiskDevices.length > 0) {
            todos.push({
                id: 'manager-moderate-risk-devices',
                text: `有 ${moderateRiskDevices.length} 台设备健康度偏低，建议关注`,
                time: now.getTime(),
                priority: 'normal',
                icon: '⚠️',
                iconColor: '#f59e0b',
                actionUrl: 'page:device-management'
            });
        }
    }
}

// 生成CEO待办事项
function generateCeoTodos(todos, applications, devices, maintenanceRecords, now) {
    // 1. 待审核的申请
    const pendingApps = applications.filter(app => app.status === '待CEO审批');

    if (pendingApps.length > 0) {
        const urgentCount = pendingApps.filter(app => app.priority === 'high').length;
        const highValueCount = pendingApps.filter(app => {
            const amount = parseFloat(app.amount) || 0;
            return amount >= 100000;
        }).length;

        todos.push({
            id: 'ceo-pending-approval',
            text: `您有 ${pendingApps.length} 个申请待审批${urgentCount > 0 ? `（${urgentCount}个紧急）` : ''}${highValueCount > 0 ? `（${highValueCount}个高金额）` : ''}`,
            time: now.getTime(),
            priority: urgentCount > 0 || highValueCount > 0 ? 'high' : 'medium',
            icon: '👑',
            iconColor: urgentCount > 0 || highValueCount > 0 ? '#dc2626' : '#f59e0b',
            actionUrl: 'section:pendingApproval'
        });
    }

    // 2. 设备投资决策提醒
    if (devices && devices.length > 0) {
        const oldDevices = devices.filter(device => {
            if (!device.entryDate) return false;
            const entryDate = new Date(device.entryDate);
            const yearsInUse = (now - entryDate) / (1000 * 60 * 60 * 24 * 365);
            return yearsInUse > 10; // 使用超过10年的设备
        });

        if (oldDevices.length > 0) {
            todos.push({
                id: 'ceo-old-devices',
                text: `有 ${oldDevices.length} 台设备使用年限超过10年，建议考虑更新换代`,
                time: now.getTime(),
                priority: 'low',
                icon: '💼',
                iconColor: '#6b7280',
                actionUrl: 'page:device-management'
            });
        }
    }
}

// 生成普通用户待办事项
function generateUserTodos(todos, applications, username, devices, maintenanceRecords, department, now) {
    // 1. 自己申请的状态更新
    const myApps = applications.filter(app => app.username === username);

    // 被拒绝需要重新提交的申请
    const rejectedApps = myApps.filter(app => app.status === '已拒绝');
    if (rejectedApps.length > 0) {
        todos.push({
            id: 'user-rejected-apps',
            text: `您有 ${rejectedApps.length} 个申请被拒绝，可以重新提交`,
            time: now.getTime(),
            priority: 'medium',
            icon: '🔄',
            iconColor: '#f59e0b',
            actionUrl: 'section:history'
        });
    }

    // 最近24小时内状态有更新的申请
    const recentUpdates = myApps.filter(app => {
        const updateTime = new Date(app.lastUpdateTime || app.date);
        const hoursSinceUpdate = (now - updateTime) / (1000 * 60 * 60);
        return hoursSinceUpdate <= 24 && app.status !== '待厂长审核';
    });

    recentUpdates.forEach(app => {
        let priority = 'normal';
        let iconColor = '#3b82f6';
        let icon = '📄';

        if (app.status === '已通过') {
            priority = 'low';
            iconColor = '#10b981';
            icon = '✅';
        } else if (app.status === '已拒绝') {
            priority = 'medium';
            iconColor = '#dc2626';
            icon = '❌';
        }

        todos.push({
            id: `user-app-update-${app.id}`,
            text: `您的申请 ${app.applicationCode || app.id.slice(-6)} 状态已更新：${app.status}`,
            time: new Date(app.lastUpdateTime || app.date).getTime(),
            priority: priority,
            icon: icon,
            iconColor: iconColor,
            actionUrl: 'section:history'
        });
    });

    // 2. 机电部用户的设备维修保养提醒
    if (department === '机电部' && devices && devices.length > 0) {
        const needAttentionDevices = devices.filter(device => {
            const healthScore = device.healthScore || 100;
            return healthScore < 70;
        });

        if (needAttentionDevices.length > 0) {
            todos.push({
                id: 'user-devices-attention',
                text: `有 ${needAttentionDevices.length} 台设备需要您关注维护`,
                time: now.getTime(),
                priority: 'medium',
                icon: '🔧',
                iconColor: '#f59e0b',
                actionUrl: 'page:device-management'
            });
        }

        // 检查是否有待处理的维修记录
        if (maintenanceRecords && maintenanceRecords.length > 0) {
            const myPendingRecords = maintenanceRecords.filter(record =>
                record.operator === username && record.status === 'pending'
            );

            if (myPendingRecords.length > 0) {
                todos.push({
                    id: 'user-pending-maintenance',
                    text: `您有 ${myPendingRecords.length} 个维修保养记录待完成`,
                    time: now.getTime(),
                    priority: 'medium',
                    icon: '📋',
                    iconColor: '#f59e0b',
                    actionUrl: 'page:device-management'
                });
            }
        }
    }
}

// 生成只读用户待办事项
function generateReadonlyTodos(todos, applications, devices, maintenanceRecords, now) {
    // 1. 高金额申请通知
    const highValueApps = applications.filter(app => {
        const amount = parseFloat(app.amount) || 0;
        const updateTime = new Date(app.lastUpdateTime || app.date);
        const hoursSinceUpdate = (now - updateTime) / (1000 * 60 * 60);

        return amount >= 100000 &&
               app.status === '已通过' &&
               hoursSinceUpdate <= 24;
    });

    highValueApps.forEach(app => {
        todos.push({
            id: `readonly-high-value-${app.id}`,
            text: `高金额申请已通过：${app.applicant} 申请 ${app.amount} ${app.currency === 'USD' ? '美元' : '人民币'}`,
            time: new Date(app.lastUpdateTime || app.date).getTime(),
            priority: 'normal',
            icon: '💰',
            iconColor: '#10b981',
            actionUrl: 'section:approved'
        });
    });

    // 2. 系统统计信息
    const pendingCount = applications.filter(app =>
        app.status === '待厂长审核' ||
        app.status === '待总监审批' ||
        app.status === '待经理审批' ||
        app.status === '待CEO审批'
    ).length;

    if (pendingCount > 0) {
        todos.push({
            id: 'readonly-system-overview',
            text: `系统概览：当前有 ${pendingCount} 个申请在审批流程中`,
            time: now.getTime(),
            priority: 'low',
            icon: '📊',
            iconColor: '#6b7280',
            actionUrl: 'section:approved'
        });
    }

    // 3. 设备状态概览
    if (devices && devices.length > 0) {
        const totalDevices = devices.length;
        const healthyDevices = devices.filter(device => (device.healthScore || 100) >= 80).length;
        const unhealthyDevices = totalDevices - healthyDevices;

        if (unhealthyDevices > 0) {
            todos.push({
                id: 'readonly-device-overview',
                text: `设备概览：${totalDevices} 台设备中有 ${unhealthyDevices} 台健康度偏低`,
                time: now.getTime(),
                priority: 'low',
                icon: '🏭',
                iconColor: '#6b7280',
                actionUrl: 'page:device-management'
            });
        }
    }

    // 4. 最近维修保养活动
    if (maintenanceRecords && maintenanceRecords.length > 0) {
        const recentMaintenance = maintenanceRecords.filter(record => {
            const recordDate = new Date(record.date);
            const hoursSinceRecord = (now - recordDate) / (1000 * 60 * 60);
            return hoursSinceRecord <= 24;
        });

        if (recentMaintenance.length > 0) {
            todos.push({
                id: 'readonly-recent-maintenance',
                text: `最近24小时内完成了 ${recentMaintenance.length} 项设备维修保养工作`,
                time: now.getTime(),
                priority: 'low',
                icon: '🔧',
                iconColor: '#6b7280',
                actionUrl: 'page:device-management'
            });
        }
    }
}

// 添加错误处理中间件（必须在所有路由之后）
if (optimizedModules && optimizedModules.errorHandler) {
    app.use(optimizedModules.errorHandler.notFoundHandler());
    app.use(optimizedModules.errorHandler.expressErrorHandler());
}

// 启动服务器
app.listen(port, () => {
    logWithTime(`Server running at http://localhost:${port}`);

    // 启动提醒系统
    startReminderScheduler();

    // 定期清理临时文件
    setInterval(() => {
        exportService.cleanupTempFiles();
    }, 24 * 60 * 60 * 1000); // 每24小时清理一次
});