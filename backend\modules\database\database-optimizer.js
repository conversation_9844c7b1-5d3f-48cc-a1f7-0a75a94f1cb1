/**
 * 数据库查询优化模块
 * 提供SQL查询优化、索引管理、查询性能监控功能
 * 确保数据库操作的高效性和稳定性
 */

// 导入统一日志工具
const consoleLogger = require('../../utils/console-logger');

class DatabaseOptimizer {
    constructor(database) {
        this.db = database;
        this.queryStats = new Map(); // 查询性能统计
        this.slowQueryThreshold = 1000; // 慢查询阈值（毫秒）
        this.enableQueryLogging = process.env.NODE_ENV !== 'production';
        
        // 初始化优化索引
        this.initializeOptimizedIndexes();
    }

    /**
     * 初始化优化的数据库索引
     * 根据常用查询模式创建高效索引
     */
    initializeOptimizedIndexes() {
        try {
            // 静默创建索引，详细信息记录到日志

            // 用户表索引优化
            const userIndexes = [
                'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
                'CREATE INDEX IF NOT EXISTS idx_users_role ON users(role)',
                'CREATE INDEX IF NOT EXISTS idx_users_department ON users(department)',
                'CREATE INDEX IF NOT EXISTS idx_users_userId ON users(userId)',
                'CREATE INDEX IF NOT EXISTS idx_users_email ON users(email)',
                'CREATE INDEX IF NOT EXISTS idx_users_created_at ON users(createdAt)',
                // 复合索引用于常见查询组合
                'CREATE INDEX IF NOT EXISTS idx_users_role_department ON users(role, department)',
                'CREATE INDEX IF NOT EXISTS idx_users_username_role ON users(username, role)'
            ];

            // 申请表索引优化（根据实际表结构）
            const applicationIndexes = [
                'CREATE INDEX IF NOT EXISTS idx_applications_applicant ON applications(applicant)',
                'CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status)',
                'CREATE INDEX IF NOT EXISTS idx_applications_createdAt ON applications(createdAt)',
                'CREATE INDEX IF NOT EXISTS idx_applications_updatedAt ON applications(updatedAt)',
                'CREATE INDEX IF NOT EXISTS idx_applications_department ON applications(department)',
                'CREATE INDEX IF NOT EXISTS idx_applications_date ON applications(date)',
                'CREATE INDEX IF NOT EXISTS idx_applications_username ON applications(username)',
                'CREATE INDEX IF NOT EXISTS idx_applications_applicationCode ON applications(applicationCode)',
                // 复合索引用于分页和筛选
                'CREATE INDEX IF NOT EXISTS idx_applications_status_created ON applications(status, createdAt DESC)',
                'CREATE INDEX IF NOT EXISTS idx_applications_applicant_status ON applications(applicant, status)',
                'CREATE INDEX IF NOT EXISTS idx_applications_department_status ON applications(department, status)',
                'CREATE INDEX IF NOT EXISTS idx_applications_date_status ON applications(date, status)'
            ];

            // 设备表索引优化（根据实际表结构）
            const deviceIndexes = [
                'CREATE INDEX IF NOT EXISTS idx_devices_deviceName ON devices(deviceName)',
                'CREATE INDEX IF NOT EXISTS idx_devices_deviceCode ON devices(deviceCode)',
                'CREATE INDEX IF NOT EXISTS idx_devices_location ON devices(location)',
                'CREATE INDEX IF NOT EXISTS idx_devices_status ON devices(status)',
                'CREATE INDEX IF NOT EXISTS idx_devices_factory ON devices(factory)',
                'CREATE INDEX IF NOT EXISTS idx_devices_responsible ON devices(responsible)',
                'CREATE INDEX IF NOT EXISTS idx_devices_createTime ON devices(createTime)',
                'CREATE INDEX IF NOT EXISTS idx_devices_entryDate ON devices(entryDate)',
                // 复合索引用于设备管理查询
                'CREATE INDEX IF NOT EXISTS idx_devices_factory_status ON devices(factory, status)',
                'CREATE INDEX IF NOT EXISTS idx_devices_location_status ON devices(location, status)',
                'CREATE INDEX IF NOT EXISTS idx_devices_responsible_status ON devices(responsible, status)'
            ];

            // 维修记录表索引优化（根据实际表结构）
            const maintenanceIndexes = [
                'CREATE INDEX IF NOT EXISTS idx_maintenance_deviceId ON maintenance_records(deviceId)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_deviceCode ON maintenance_records(deviceCode)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_maintenanceType ON maintenance_records(maintenanceType)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_reportDate ON maintenance_records(reportDate)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_reportedBy ON maintenance_records(reportedBy)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_status ON maintenance_records(status)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_cost ON maintenance_records(cost)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_factory ON maintenance_records(factory)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_assignedTo ON maintenance_records(assignedTo)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_completedBy ON maintenance_records(completedBy)',
                // 复合索引用于维修历史查询
                'CREATE INDEX IF NOT EXISTS idx_maintenance_device_date ON maintenance_records(deviceId, reportDate DESC)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_device_type ON maintenance_records(deviceId, maintenanceType)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_date_type ON maintenance_records(reportDate, maintenanceType)',
                'CREATE INDEX IF NOT EXISTS idx_maintenance_assigned_date ON maintenance_records(assignedTo, reportDate DESC)'
            ];

            // 部门表索引优化
            const departmentIndexes = [
                'CREATE INDEX IF NOT EXISTS idx_departments_name ON departments(name)',
                'CREATE INDEX IF NOT EXISTS idx_departments_code ON departments(code)',
                'CREATE INDEX IF NOT EXISTS idx_departments_created_at ON departments(createdAt)'
            ];

            // 厂区表索引优化（根据实际表结构）
            const factoryIndexes = [
                'CREATE INDEX IF NOT EXISTS idx_factories_name ON factories(name)',
                'CREATE INDEX IF NOT EXISTS idx_factories_createTime ON factories(createTime)'
            ];

            // 执行所有索引创建
            const allIndexes = [
                ...userIndexes,
                ...applicationIndexes,
                ...deviceIndexes,
                ...maintenanceIndexes,
                ...departmentIndexes,
                ...factoryIndexes
            ];

            allIndexes.forEach(indexSql => {
                try {
                    this.db.exec(indexSql);
                } catch (error) {
                    consoleLogger.error('创建索引失败:', indexSql, error.message);
                }
            });

            // 索引创建完成，详细信息已记录到日志
            consoleLogger.systemSuccess(`数据库优化完成 (${allIndexes.length}个索引)`);

            // 更新表统计信息
            this.updateTableStatistics();

        } catch (error) {
            consoleLogger.error('初始化数据库索引失败:', error);
        }
    }

    /**
     * 更新表统计信息
     * SQLite会自动维护统计信息，这里主要是触发分析
     */
    updateTableStatistics() {
        try {
            // 分析所有表以更新查询优化器统计信息
            const tables = ['users', 'applications', 'devices', 'maintenance_records', 'departments', 'factories'];
            
            tables.forEach(table => {
                try {
                    this.db.exec(`ANALYZE ${table}`);
                } catch (error) {
                    consoleLogger.error(`分析表 ${table} 失败:`, error.message);
                }
            });

            // 表统计信息更新完成，详细信息已记录到日志
        } catch (error) {
            consoleLogger.error('更新表统计信息失败:', error);
        }
    }

    /**
     * 执行优化的查询
     * @param {string} sql - SQL查询语句
     * @param {Array} params - 查询参数
     * @param {string} queryName - 查询名称（用于统计）
     * @returns {any} 查询结果
     */
    executeOptimizedQuery(sql, params = [], queryName = 'unknown') {
        const startTime = Date.now();
        
        try {
            // 准备语句以提高性能
            const stmt = this.db.prepare(sql);
            const result = params.length > 0 ? stmt.all(...params) : stmt.all();
            
            const executionTime = Date.now() - startTime;
            
            // 记录查询性能
            this.recordQueryPerformance(queryName, sql, executionTime, params.length);
            
            return result;
        } catch (error) {
            const executionTime = Date.now() - startTime;
            consoleLogger.error(`查询执行失败 [${queryName}]:`, error.message);
            consoleLogger.error('SQL:', sql);
            consoleLogger.error('参数:', params);
            
            // 记录失败的查询
            this.recordQueryPerformance(queryName, sql, executionTime, params.length, error.message);
            
            throw error;
        }
    }

    /**
     * 执行优化的单行查询
     */
    executeOptimizedQuerySingle(sql, params = [], queryName = 'unknown') {
        const startTime = Date.now();
        
        try {
            const stmt = this.db.prepare(sql);
            const result = params.length > 0 ? stmt.get(...params) : stmt.get();
            
            const executionTime = Date.now() - startTime;
            this.recordQueryPerformance(queryName, sql, executionTime, params.length);
            
            return result;
        } catch (error) {
            const executionTime = Date.now() - startTime;
            consoleLogger.error(`单行查询执行失败 [${queryName}]:`, error.message);
            
            this.recordQueryPerformance(queryName, sql, executionTime, params.length, error.message);
            throw error;
        }
    }

    /**
     * 执行优化的写入操作
     */
    executeOptimizedWrite(sql, params = [], queryName = 'unknown') {
        const startTime = Date.now();
        
        try {
            const stmt = this.db.prepare(sql);
            const result = params.length > 0 ? stmt.run(...params) : stmt.run();
            
            const executionTime = Date.now() - startTime;
            this.recordQueryPerformance(queryName, sql, executionTime, params.length);
            
            return result;
        } catch (error) {
            const executionTime = Date.now() - startTime;
            consoleLogger.error(`写入操作执行失败 [${queryName}]:`, error.message);
            
            this.recordQueryPerformance(queryName, sql, executionTime, params.length, error.message);
            throw error;
        }
    }

    /**
     * 记录查询性能统计
     */
    recordQueryPerformance(queryName, sql, executionTime, paramCount, error = null) {
        if (!this.enableQueryLogging) return;

        if (!this.queryStats.has(queryName)) {
            this.queryStats.set(queryName, {
                count: 0,
                totalTime: 0,
                avgTime: 0,
                maxTime: 0,
                minTime: Infinity,
                errorCount: 0,
                lastExecuted: null,
                sql: sql
            });
        }

        const stats = this.queryStats.get(queryName);
        stats.count++;
        stats.totalTime += executionTime;
        stats.avgTime = stats.totalTime / stats.count;
        stats.maxTime = Math.max(stats.maxTime, executionTime);
        stats.minTime = Math.min(stats.minTime, executionTime);
        stats.lastExecuted = new Date();

        if (error) {
            stats.errorCount++;
        }

        // 记录慢查询
        if (executionTime > this.slowQueryThreshold) {
            consoleLogger.warn(`慢查询检测 [${queryName}]: ${executionTime}ms`);
            consoleLogger.warn('SQL:', sql);
            consoleLogger.warn('参数数量:', paramCount);
        }
    }

    /**
     * 获取查询性能统计
     */
    getQueryStats() {
        const stats = {};
        for (const [queryName, data] of this.queryStats.entries()) {
            stats[queryName] = { ...data };
        }
        return stats;
    }

    /**
     * 获取慢查询列表
     */
    getSlowQueries() {
        const slowQueries = [];
        for (const [queryName, stats] of this.queryStats.entries()) {
            if (stats.maxTime > this.slowQueryThreshold) {
                slowQueries.push({
                    queryName,
                    maxTime: stats.maxTime,
                    avgTime: stats.avgTime,
                    count: stats.count,
                    sql: stats.sql
                });
            }
        }
        return slowQueries.sort((a, b) => b.maxTime - a.maxTime);
    }

    /**
     * 清理查询统计
     */
    clearQueryStats() {
        this.queryStats.clear();
        consoleLogger.log('查询统计已清理');
    }

    /**
     * 获取数据库性能信息
     */
    getDatabasePerformanceInfo() {
        try {
            // 获取数据库大小信息
            const sizeInfo = this.db.prepare('PRAGMA page_count').get();
            const pageSize = this.db.prepare('PRAGMA page_size').get();
            const dbSize = sizeInfo.page_count * pageSize.page_size;

            // 获取缓存信息
            const cacheInfo = this.db.prepare('PRAGMA cache_size').get();

            // 获取索引信息
            const indexList = this.db.prepare(`
                SELECT name, tbl_name 
                FROM sqlite_master 
                WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
                ORDER BY tbl_name, name
            `).all();

            return {
                databaseSize: dbSize,
                pageCount: sizeInfo.page_count,
                pageSize: pageSize.page_size,
                cacheSize: cacheInfo.cache_size,
                indexCount: indexList.length,
                indexes: indexList,
                queryStats: this.getQueryStats(),
                slowQueries: this.getSlowQueries()
            };
        } catch (error) {
            consoleLogger.error('获取数据库性能信息失败:', error);
            return null;
        }
    }

    /**
     * 优化数据库配置
     */
    optimizeDatabaseSettings() {
        try {
            // 设置优化的PRAGMA参数
            const optimizations = [
                'PRAGMA journal_mode = WAL',           // 使用WAL模式提高并发性能
                'PRAGMA synchronous = NORMAL',         // 平衡安全性和性能
                'PRAGMA cache_size = -64000',          // 设置64MB缓存
                'PRAGMA temp_store = MEMORY',          // 临时表存储在内存中
                'PRAGMA mmap_size = 268435456',        // 256MB内存映射
                'PRAGMA optimize'                      // 触发查询优化器
            ];

            optimizations.forEach(pragma => {
                try {
                    this.db.exec(pragma);
                } catch (error) {
                    consoleLogger.error('执行PRAGMA失败:', pragma, error.message);
                }
            });

            consoleLogger.log('数据库配置优化完成');
        } catch (error) {
            consoleLogger.error('优化数据库配置失败:', error);
        }
    }

    /**
     * 定期维护任务
     */
    performMaintenance() {
        try {
            consoleLogger.log('开始数据库维护...');

            // 更新统计信息
            this.updateTableStatistics();

            // 优化数据库
            this.db.exec('PRAGMA optimize');

            // 清理旧的查询统计（保留最近1000条）
            if (this.queryStats.size > 1000) {
                const entries = Array.from(this.queryStats.entries());
                entries.sort((a, b) => b[1].lastExecuted - a[1].lastExecuted);
                
                this.queryStats.clear();
                entries.slice(0, 1000).forEach(([key, value]) => {
                    this.queryStats.set(key, value);
                });
            }

            consoleLogger.log('数据库维护完成');
        } catch (error) {
            consoleLogger.error('数据库维护失败:', error);
        }
    }

    /**
     * 启动定期维护任务
     */
    startMaintenanceSchedule() {
        // 每小时执行一次维护
        setInterval(() => {
            this.performMaintenance();
        }, 60 * 60 * 1000);

        consoleLogger.log('数据库维护调度已启动');
    }

    /**
     * 分析查询计划
     */
    analyzeQueryPlan(sql, params = []) {
        try {
            const explainSql = `EXPLAIN QUERY PLAN ${sql}`;
            const stmt = this.db.prepare(explainSql);
            const plan = params.length > 0 ? stmt.all(...params) : stmt.all();

            return {
                success: true,
                plan: plan,
                analysis: this.interpretQueryPlan(plan)
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 解释查询计划
     */
    interpretQueryPlan(plan) {
        const analysis = {
            hasIndexScan: false,
            hasTableScan: false,
            usedIndexes: [],
            recommendations: []
        };

        for (const step of plan) {
            const detail = step.detail.toLowerCase();

            // 检查是否使用了索引
            if (detail.includes('using index')) {
                analysis.hasIndexScan = true;
                const indexMatch = detail.match(/using index (\w+)/);
                if (indexMatch) {
                    analysis.usedIndexes.push(indexMatch[1]);
                }
            }

            // 检查是否有表扫描
            if (detail.includes('scan table') && !detail.includes('using index')) {
                analysis.hasTableScan = true;
                analysis.recommendations.push('考虑为查询条件添加索引以避免全表扫描');
            }

            // 检查临时表使用
            if (detail.includes('use temp b-tree')) {
                analysis.recommendations.push('查询使用了临时表，考虑优化ORDER BY或GROUP BY子句');
            }
        }

        return analysis;
    }

    /**
     * 获取表统计信息
     */
    getTableStatistics() {
        try {
            const tables = ['users', 'applications', 'devices', 'maintenance_records', 'departments', 'factories'];
            const statistics = {};

            for (const table of tables) {
                try {
                    // 获取行数
                    const countResult = this.db.prepare(`SELECT COUNT(*) as count FROM ${table}`).get();

                    // 获取表大小信息
                    const sizeResult = this.db.prepare(`
                        SELECT
                            name,
                            (SELECT COUNT(*) FROM sqlite_master WHERE type='index' AND tbl_name=?) as index_count
                        FROM sqlite_master
                        WHERE type='table' AND name=?
                    `).get(table, table);

                    statistics[table] = {
                        rowCount: countResult.count,
                        indexCount: sizeResult ? sizeResult.index_count : 0,
                        lastAnalyzed: new Date().toISOString()
                    };
                } catch (error) {
                    statistics[table] = {
                        error: error.message
                    };
                }
            }

            return statistics;
        } catch (error) {
            consoleLogger.error('获取表统计信息失败:', error);
            return {};
        }
    }

    /**
     * 检测缺失的索引
     */
    detectMissingIndexes() {
        const recommendations = [];

        try {
            // 分析慢查询，寻找可能需要索引的模式
            const slowQueries = this.getSlowQueries();

            for (const query of slowQueries) {
                const sql = query.sql.toLowerCase();

                // 检查WHERE子句中的列
                const whereMatch = sql.match(/where\s+(\w+)\s*[=<>]/);
                if (whereMatch) {
                    const column = whereMatch[1];
                    recommendations.push({
                        type: 'missing_index',
                        table: this.extractTableFromQuery(sql),
                        column: column,
                        reason: `WHERE子句中的${column}列可能需要索引`,
                        queryName: query.queryName,
                        avgTime: query.avgTime
                    });
                }

                // 检查ORDER BY子句中的列
                const orderMatch = sql.match(/order\s+by\s+(\w+)/);
                if (orderMatch) {
                    const column = orderMatch[1];
                    recommendations.push({
                        type: 'missing_index',
                        table: this.extractTableFromQuery(sql),
                        column: column,
                        reason: `ORDER BY子句中的${column}列可能需要索引`,
                        queryName: query.queryName,
                        avgTime: query.avgTime
                    });
                }
            }

            return recommendations;
        } catch (error) {
            consoleLogger.error('检测缺失索引失败:', error);
            return [];
        }
    }

    /**
     * 从SQL查询中提取表名
     */
    extractTableFromQuery(sql) {
        const fromMatch = sql.match(/from\s+(\w+)/);
        return fromMatch ? fromMatch[1] : 'unknown';
    }

    /**
     * 优化查询建议
     */
    getOptimizationSuggestions() {
        const suggestions = [];

        try {
            // 检查缺失的索引
            const missingIndexes = this.detectMissingIndexes();
            suggestions.push(...missingIndexes);

            // 检查未使用的索引
            const unusedIndexes = this.detectUnusedIndexes();
            suggestions.push(...unusedIndexes);

            // 检查数据库配置
            const configSuggestions = this.analyzeConfiguration();
            suggestions.push(...configSuggestions);

            return suggestions;
        } catch (error) {
            consoleLogger.error('获取优化建议失败:', error);
            return [];
        }
    }

    /**
     * 检测未使用的索引
     */
    detectUnusedIndexes() {
        const suggestions = [];

        try {
            // 获取所有索引
            const indexes = this.db.prepare(`
                SELECT name, tbl_name, sql
                FROM sqlite_master
                WHERE type = 'index' AND name NOT LIKE 'sqlite_%'
            `).all();

            // 这里可以实现更复杂的逻辑来检测未使用的索引
            // 目前只是一个基础框架

            return suggestions;
        } catch (error) {
            consoleLogger.error('检测未使用索引失败:', error);
            return [];
        }
    }

    /**
     * 分析数据库配置
     */
    analyzeConfiguration() {
        const suggestions = [];

        try {
            // 检查缓存大小
            const cacheSize = this.db.prepare('PRAGMA cache_size').get();
            if (Math.abs(cacheSize.cache_size) < 32000) {
                suggestions.push({
                    type: 'configuration',
                    category: 'cache',
                    current: cacheSize.cache_size,
                    recommended: -64000,
                    reason: '增加缓存大小可以提高查询性能'
                });
            }

            // 检查日志模式
            const journalMode = this.db.prepare('PRAGMA journal_mode').get();
            if (journalMode.journal_mode !== 'wal') {
                suggestions.push({
                    type: 'configuration',
                    category: 'journal_mode',
                    current: journalMode.journal_mode,
                    recommended: 'WAL',
                    reason: 'WAL模式可以提高并发性能'
                });
            }

            return suggestions;
        } catch (error) {
            consoleLogger.error('分析数据库配置失败:', error);
            return [];
        }
    }

    /**
     * 执行性能基准测试
     */
    async runPerformanceBenchmark() {
        const benchmark = {
            timestamp: new Date().toISOString(),
            tests: {}
        };

        try {
            // 测试简单查询性能
            const simpleQueryStart = Date.now();
            this.db.prepare('SELECT COUNT(*) FROM users').get();
            benchmark.tests.simpleQuery = Date.now() - simpleQueryStart;

            // 测试复杂查询性能
            const complexQueryStart = Date.now();
            this.db.prepare(`
                SELECT u.username, COUNT(a.id) as app_count
                FROM users u
                LEFT JOIN applications a ON u.username = a.username
                GROUP BY u.username
                LIMIT 10
            `).all();
            benchmark.tests.complexQuery = Date.now() - complexQueryStart;

            // 测试插入性能
            const insertStart = Date.now();
            const insertStmt = this.db.prepare('INSERT INTO users (username, password, role, userId) VALUES (?, ?, ?, ?)');
            const testUser = `test_${Date.now()}`;
            insertStmt.run(testUser, 'test', 'test', testUser);
            benchmark.tests.insert = Date.now() - insertStart;

            // 清理测试数据
            this.db.prepare('DELETE FROM users WHERE username = ?').run(testUser);

            return {
                success: true,
                benchmark
            };
        } catch (error) {
            return {
                success: false,
                error: error.message,
                benchmark
            };
        }
    }
}

module.exports = DatabaseOptimizer;
