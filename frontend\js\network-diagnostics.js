/**
 * 网络诊断工具
 * 用于检测网络连接状态和代理兼容性
 * 确保代理网络和VPN用户能够完全访问系统
 */

// 网络诊断配置常量
const NETWORK_DIAGNOSTICS_CONFIG = {
    // 健康检查端点
    healthCheckUrl: '/api/health',
    // 网络质量检测间隔（毫秒）
    qualityCheckInterval: 30000,
    // 连接超时时间（毫秒）
    connectionTimeout: 10000,
    // 通知显示时间（毫秒）
    notificationDuration: 3000,
    // 调试模式
    debug: true
};

class NetworkDiagnostics {
    constructor() {
        this.isOnline = navigator.onLine;
        this.connectionType = this.getConnectionType();
        this.config = NETWORK_DIAGNOSTICS_CONFIG;
        this.setupEventListeners();
    }

    /**
     * 获取连接类型
     */
    getConnectionType() {
        if ('connection' in navigator) {
            return navigator.connection.effectiveType || 'unknown';
        }
        return 'unknown';
    }

    /**
     * 设置网络状态监听器
     */
    setupEventListeners() {
        window.addEventListener('online', () => {
            this.isOnline = true;
            this.onNetworkStatusChange('online');
        });

        window.addEventListener('offline', () => {
            this.isOnline = false;
            this.onNetworkStatusChange('offline');
        });

        // 监听连接变化
        if ('connection' in navigator) {
            navigator.connection.addEventListener('change', () => {
                this.connectionType = this.getConnectionType();
                this.onConnectionTypeChange(this.connectionType);
            });
        }
    }

    /**
     * 网络状态变化回调
     */
    onNetworkStatusChange(status) {
        console.log(`网络状态变化: ${status}`);

        // 显示用户通知
        if (status === 'offline') {
            this.showNetworkNotification('网络连接已断开，请检查您的网络设置', 'error');
        } else {
            this.showNetworkNotification('网络连接已恢复', 'success');
            // 网络恢复后重新加载数据
            this.retryFailedRequests();
        }
    }

    /**
     * 连接类型变化回调
     */
    onConnectionTypeChange(type) {
        console.log(`连接类型变化: ${type}`);
    }

    /**
     * 检测网络连接质量
     */
    async checkNetworkQuality() {
        const startTime = performance.now();

        try {
            // 创建AbortController用于超时控制（兼容性更好）
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, this.config.connectionTimeout);

            const response = await fetch(this.config.healthCheckUrl, {
                method: 'GET',
                cache: 'no-cache',
                signal: controller.signal
            });

            // 清除超时
            clearTimeout(timeoutId);

            const endTime = performance.now();
            const latency = endTime - startTime;

            if (response.ok) {
                const data = await response.json();
                return {
                    status: 'good',
                    latency: Math.round(latency),
                    serverInfo: data
                };
            } else {
                return {
                    status: 'poor',
                    latency: Math.round(latency),
                    error: `HTTP ${response.status}`
                };
            }
        } catch (error) {
            const endTime = performance.now();
            const latency = endTime - startTime;

            return {
                status: 'failed',
                latency: Math.round(latency),
                error: error.message
            };
        }
    }

    /**
     * 检测代理设置
     */
    async detectProxy() {
        try {
            // 创建AbortController用于超时控制（兼容性更好）
            const controller = new AbortController();
            const timeoutId = setTimeout(() => {
                controller.abort();
            }, this.config.connectionTimeout);

            const response = await fetch(this.config.healthCheckUrl, {
                signal: controller.signal
            });

            // 清除超时
            clearTimeout(timeoutId);

            const data = await response.json();

            const hasProxy = data.network.forwardedFor ||
                           data.network.realIP ||
                           data.network.clientIP !== window.location.hostname;

            return {
                hasProxy,
                clientIP: data.network.clientIP,
                forwardedFor: data.network.forwardedFor,
                realIP: data.network.realIP
            };
        } catch (error) {
            console.error('代理检测失败:', error);
            return { hasProxy: false, error: error.message };
        }
    }

    /**
     * 运行完整的网络诊断
     */
    async runDiagnostics() {
        console.log('开始网络诊断...');

        const results = {
            timestamp: new Date().toISOString(),
            online: this.isOnline,
            connectionType: this.connectionType,
            quality: await this.checkNetworkQuality(),
            proxy: await this.detectProxy()
        };

        console.log('网络诊断结果:', results);
        return results;
    }

    /**
     * 显示网络通知
     */
    showNetworkNotification(message, type = 'info') {
        // 创建通知元素
        const notification = document.createElement('div');
        notification.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'error' ? 'bg-red-500 text-white' :
            type === 'success' ? 'bg-green-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        notification.textContent = message;

        document.body.appendChild(notification);

        // 自动移除通知
        setTimeout(() => {
            if (notification.parentNode) {
                notification.parentNode.removeChild(notification);
            }
        }, this.config.notificationDuration);
    }

    /**
     * 重试失败的请求
     */
    retryFailedRequests() {
        // 这里可以实现重试逻辑
        console.log('网络恢复，准备重试失败的请求...');

        // 触发自定义事件，让其他模块知道可以重试了
        window.dispatchEvent(new CustomEvent('networkRecovered'));
    }

    /**
     * 获取网络状态摘要
     */
    getNetworkSummary() {
        return {
            online: this.isOnline,
            connectionType: this.connectionType,
            timestamp: new Date().toISOString()
        };
    }
}

// 创建全局网络诊断实例
window.networkDiagnostics = new NetworkDiagnostics();

// 页面加载完成后运行初始诊断
document.addEventListener('DOMContentLoaded', async () => {
    // 减少延迟时间，提升加载速度
    setTimeout(async () => {
        const results = await window.networkDiagnostics.runDiagnostics();

        // 如果网络质量差，显示提示
        if (results.quality.status === 'poor' || results.quality.status === 'failed') {
            window.networkDiagnostics.showNetworkNotification(
                `网络连接质量较差 (延迟: ${results.quality.latency}ms)，可能影响使用体验`,
                'error'
            );
        }

        // 如果检测到代理，记录信息
        if (results.proxy.hasProxy) {
            console.log('检测到代理连接:', results.proxy);
        }
    }, 200); // 从1000ms减少到200ms
});
