/**
 * 全局错误处理模块
 * 提供统一的错误处理、错误分类、错误响应格式化
 * 支持错误日志记录、错误统计和错误恢复机制
 */

// 导入统一日志工具
const consoleLogger = require('../../utils/console-logger');

class ErrorHandler {
    constructor(logger = null) {
        this.logger = logger;
        
        // 错误类型定义
        this.errorTypes = {
            VALIDATION_ERROR: 'VALIDATION_ERROR',
            AUTHENTICATION_ERROR: 'AUTHENTICATION_ERROR',
            AUTHORIZATION_ERROR: 'AUTHORIZATION_ERROR',
            NOT_FOUND_ERROR: 'NOT_FOUND_ERROR',
            CONFLICT_ERROR: 'CONFLICT_ERROR',
            RATE_LIMIT_ERROR: 'RATE_LIMIT_ERROR',
            DATABASE_ERROR: 'DATABASE_ERROR',
            FILE_ERROR: 'FILE_ERROR',
            NETWORK_ERROR: 'NETWORK_ERROR',
            INTERNAL_ERROR: 'INTERNAL_ERROR'
        };

        // 错误统计
        this.errorStats = new Map();
        
        // 错误恢复策略
        this.recoveryStrategies = new Map();
        
        this.initializeRecoveryStrategies();
    }

    /**
     * 初始化错误恢复策略
     */
    initializeRecoveryStrategies() {
        // 数据库错误恢复
        this.recoveryStrategies.set(this.errorTypes.DATABASE_ERROR, {
            maxRetries: 3,
            retryDelay: 1000,
            backoffMultiplier: 2
        });

        // 网络错误恢复
        this.recoveryStrategies.set(this.errorTypes.NETWORK_ERROR, {
            maxRetries: 2,
            retryDelay: 500,
            backoffMultiplier: 1.5
        });

        // 文件错误恢复
        this.recoveryStrategies.set(this.errorTypes.FILE_ERROR, {
            maxRetries: 1,
            retryDelay: 100,
            backoffMultiplier: 1
        });
    }

    /**
     * 创建自定义错误类
     */
    createError(type, message, details = {}, statusCode = 500) {
        const error = new Error(message);
        error.type = type;
        error.details = details;
        error.statusCode = statusCode;
        error.timestamp = new Date().toISOString();
        error.id = this.generateErrorId();
        
        return error;
    }

    /**
     * 生成错误ID
     */
    generateErrorId() {
        return `ERR_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
    }

    /**
     * Express错误处理中间件
     */
    expressErrorHandler() {
        return (error, req, res, next) => {
            // 记录错误统计
            this.recordErrorStats(error);

            // 记录错误日志
            this.logError(error, req);

            // 格式化错误响应
            const errorResponse = this.formatErrorResponse(error, req);

            // 发送响应
            const statusCode = errorResponse.statusCode || 500;
            res.status(statusCode).json(errorResponse);
        };
    }

    /**
     * 异步错误包装器
     */
    asyncWrapper(fn) {
        return (req, res, next) => {
            Promise.resolve(fn(req, res, next)).catch(next);
        };
    }

    /**
     * 格式化错误响应
     */
    formatErrorResponse(error, req = null) {
        const isDevelopment = process.env.NODE_ENV === 'development';
        
        // 确定错误类型和状态码
        const errorType = error.type || this.classifyError(error);
        const statusCode = this.getStatusCode(error, errorType);

        // 基础响应结构
        const response = {
            success: false,
            error: {
                id: error.id || this.generateErrorId(),
                type: errorType,
                message: this.getSafeErrorMessage(error, errorType),
                timestamp: error.timestamp || new Date().toISOString(),
                statusCode: statusCode
            }
        };

        // 开发环境添加详细信息
        if (isDevelopment) {
            response.error.details = {
                originalMessage: error.message,
                stack: error.stack,
                details: error.details || {}
            };

            if (req) {
                response.error.request = {
                    method: req.method,
                    url: req.url,
                    headers: this.sanitizeHeaders(req.headers),
                    body: this.sanitizeBody(req.body),
                    params: req.params,
                    query: req.query
                };
            }
        }

        // 添加用户友好的建议
        response.error.suggestion = this.getErrorSuggestion(errorType);

        return response;
    }

    /**
     * 错误分类
     */
    classifyError(error) {
        const message = error.message.toLowerCase();
        
        if (error.name === 'ValidationError' || message.includes('validation')) {
            return this.errorTypes.VALIDATION_ERROR;
        }
        
        if (error.name === 'UnauthorizedError' || message.includes('unauthorized') || message.includes('token')) {
            return this.errorTypes.AUTHENTICATION_ERROR;
        }
        
        if (message.includes('forbidden') || message.includes('permission')) {
            return this.errorTypes.AUTHORIZATION_ERROR;
        }
        
        if (error.name === 'NotFoundError' || message.includes('not found')) {
            return this.errorTypes.NOT_FOUND_ERROR;
        }
        
        if (message.includes('conflict') || message.includes('duplicate')) {
            return this.errorTypes.CONFLICT_ERROR;
        }
        
        if (message.includes('rate limit') || message.includes('too many')) {
            return this.errorTypes.RATE_LIMIT_ERROR;
        }
        
        if (message.includes('database') || message.includes('sql') || error.code === 'SQLITE_ERROR') {
            return this.errorTypes.DATABASE_ERROR;
        }
        
        if (message.includes('file') || message.includes('upload') || error.code === 'ENOENT') {
            return this.errorTypes.FILE_ERROR;
        }
        
        if (message.includes('network') || message.includes('timeout') || error.code === 'ECONNREFUSED') {
            return this.errorTypes.NETWORK_ERROR;
        }
        
        return this.errorTypes.INTERNAL_ERROR;
    }

    /**
     * 获取状态码
     */
    getStatusCode(error, errorType) {
        if (error.statusCode) {
            return error.statusCode;
        }

        const statusCodeMap = {
            [this.errorTypes.VALIDATION_ERROR]: 400,
            [this.errorTypes.AUTHENTICATION_ERROR]: 401,
            [this.errorTypes.AUTHORIZATION_ERROR]: 403,
            [this.errorTypes.NOT_FOUND_ERROR]: 404,
            [this.errorTypes.CONFLICT_ERROR]: 409,
            [this.errorTypes.RATE_LIMIT_ERROR]: 429,
            [this.errorTypes.DATABASE_ERROR]: 500,
            [this.errorTypes.FILE_ERROR]: 500,
            [this.errorTypes.NETWORK_ERROR]: 503,
            [this.errorTypes.INTERNAL_ERROR]: 500
        };

        return statusCodeMap[errorType] || 500;
    }

    /**
     * 获取安全的错误消息
     */
    getSafeErrorMessage(error, errorType) {
        const isProduction = process.env.NODE_ENV === 'production';
        
        if (!isProduction) {
            return error.message;
        }

        // 生产环境使用通用错误消息
        const safeMessages = {
            [this.errorTypes.VALIDATION_ERROR]: '输入数据验证失败',
            [this.errorTypes.AUTHENTICATION_ERROR]: '身份验证失败',
            [this.errorTypes.AUTHORIZATION_ERROR]: '权限不足',
            [this.errorTypes.NOT_FOUND_ERROR]: '请求的资源不存在',
            [this.errorTypes.CONFLICT_ERROR]: '请求冲突',
            [this.errorTypes.RATE_LIMIT_ERROR]: '请求过于频繁，请稍后重试',
            [this.errorTypes.DATABASE_ERROR]: '数据库操作失败',
            [this.errorTypes.FILE_ERROR]: '文件操作失败',
            [this.errorTypes.NETWORK_ERROR]: '网络连接失败',
            [this.errorTypes.INTERNAL_ERROR]: '服务器内部错误'
        };

        return safeMessages[errorType] || '服务器内部错误';
    }

    /**
     * 获取错误建议
     */
    getErrorSuggestion(errorType) {
        const suggestions = {
            [this.errorTypes.VALIDATION_ERROR]: '请检查输入数据的格式和完整性',
            [this.errorTypes.AUTHENTICATION_ERROR]: '请检查用户名和密码，或重新登录',
            [this.errorTypes.AUTHORIZATION_ERROR]: '请联系管理员获取相应权限',
            [this.errorTypes.NOT_FOUND_ERROR]: '请检查请求的URL或资源ID',
            [this.errorTypes.CONFLICT_ERROR]: '请检查是否存在重复数据或冲突操作',
            [this.errorTypes.RATE_LIMIT_ERROR]: '请降低请求频率，稍后重试',
            [this.errorTypes.DATABASE_ERROR]: '请稍后重试，如问题持续请联系技术支持',
            [this.errorTypes.FILE_ERROR]: '请检查文件格式和大小，确保文件完整',
            [this.errorTypes.NETWORK_ERROR]: '请检查网络连接，稍后重试',
            [this.errorTypes.INTERNAL_ERROR]: '请稍后重试，如问题持续请联系技术支持'
        };

        return suggestions[errorType] || '请联系技术支持';
    }

    /**
     * 记录错误日志
     */
    logError(error, req = null) {
        const logData = {
            errorId: error.id || this.generateErrorId(),
            type: error.type || this.classifyError(error),
            message: error.message,
            stack: error.stack,
            timestamp: new Date().toISOString(),
            details: error.details || {}
        };

        if (req) {
            logData.request = {
                method: req.method,
                url: req.url,
                userAgent: req.get('User-Agent'),
                ip: req.ip,
                userId: req.user?.id,
                sessionId: req.sessionID
            };
        }

        if (this.logger) {
            this.logger.error('Application Error', logData);
        } else {
            consoleLogger.error('Application Error:', JSON.stringify(logData, null, 2));
        }
    }

    /**
     * 记录错误统计
     */
    recordErrorStats(error) {
        const errorType = error.type || this.classifyError(error);
        
        if (!this.errorStats.has(errorType)) {
            this.errorStats.set(errorType, {
                count: 0,
                lastOccurred: null,
                firstOccurred: new Date()
            });
        }

        const stats = this.errorStats.get(errorType);
        stats.count++;
        stats.lastOccurred = new Date();
    }

    /**
     * 获取错误统计
     */
    getErrorStats() {
        const stats = {};
        for (const [type, data] of this.errorStats.entries()) {
            stats[type] = { ...data };
        }
        return stats;
    }

    /**
     * 清理敏感的请求头
     */
    sanitizeHeaders(headers) {
        const sanitized = { ...headers };
        const sensitiveHeaders = ['authorization', 'cookie', 'x-api-key'];
        
        sensitiveHeaders.forEach(header => {
            if (sanitized[header]) {
                sanitized[header] = '[REDACTED]';
            }
        });

        return sanitized;
    }

    /**
     * 清理敏感的请求体
     */
    sanitizeBody(body) {
        if (!body || typeof body !== 'object') {
            return body;
        }

        const sanitized = { ...body };
        const sensitiveFields = ['password', 'token', 'secret', 'key'];
        
        sensitiveFields.forEach(field => {
            if (sanitized[field]) {
                sanitized[field] = '[REDACTED]';
            }
        });

        return sanitized;
    }

    /**
     * 404错误处理中间件
     */
    notFoundHandler() {
        return (req, res, next) => {
            const error = this.createError(
                this.errorTypes.NOT_FOUND_ERROR,
                `路径 ${req.originalUrl} 不存在`,
                { path: req.originalUrl, method: req.method },
                404
            );
            next(error);
        };
    }

    /**
     * 未捕获异常处理
     */
    handleUncaughtException() {
        process.on('uncaughtException', (error) => {
            consoleLogger.systemError('未捕获的异常:', error);
            this.logError(error);
            
            // 优雅关闭
            process.exit(1);
        });
    }

    /**
     * 未处理的Promise拒绝处理
     */
    handleUnhandledRejection() {
        process.on('unhandledRejection', (reason, promise) => {
            console.error('未处理的Promise拒绝:', reason);
            
            const error = new Error(`未处理的Promise拒绝: ${reason}`);
            error.type = this.errorTypes.INTERNAL_ERROR;
            
            this.logError(error);
        });
    }

    /**
     * 初始化全局错误处理
     */
    initializeGlobalHandlers() {
        this.handleUncaughtException();
        this.handleUnhandledRejection();
    }
}

module.exports = ErrorHandler;
