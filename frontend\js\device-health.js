// 设备健康度评估前端模块
class DeviceHealthManager {
    constructor() {
        this.currentDeviceId = null;
        this.healthData = null;
        this.collapsedFactories = new Set(); // 记录展开状态的厂区，默认所有厂区都折叠
        this.factoryPages = {}; // 记录每个厂区的当前页码
        this.pageSize = 15; // 每页显示15个设备
        this.isLoading = false; // 防止重复加载
        this.refreshDebounceTimer = null; // 防抖定时器
        // 延迟初始化，等待页面元素加载完成
        setTimeout(() => {
            if (document.getElementById('healthOverviewContainer')) {
                this.init();
            }
        }, 100);
    }

    // 防抖工具函数
    debounce(func, wait) {
        return (...args) => {
            clearTimeout(this.refreshDebounceTimer);
            this.refreshDebounceTimer = setTimeout(() => func.apply(this, args), wait);
        };
    }

    init() {
        this.bindEvents();
        this.loadHealthOverview();
    }

    bindEvents() {
        // 移除之前的事件监听器（如果存在）
        if (this.clickHandler) {
            document.removeEventListener('click', this.clickHandler);
        }

        // 创建事件处理器
        this.clickHandler = (e) => {
            // 健康度详情按钮事件
            if (e.target.classList.contains('health-detail-btn')) {
                const deviceId = e.target.dataset.deviceId;
                this.showHealthDetail(deviceId);
                return;
            }

            // 维护计划按钮事件
            if (e.target.classList.contains('maintenance-plan-btn')) {
                const deviceId = e.target.dataset.deviceId;
                this.showMaintenancePlan(deviceId);
                return;
            }

            // 分页按钮事件
            if (e.target.classList.contains('pagination-btn') && e.target.dataset.action === 'factory-page') {
                e.preventDefault();
                e.stopPropagation();
                const factoryId = e.target.dataset.factoryId;
                const page = parseInt(e.target.dataset.page);
                console.log('点击分页按钮，厂区ID:', factoryId, '页码:', page); // 调试日志
                this.goToFactoryPage(factoryId, page);
                return;
            }

            // 厂区折叠/展开事件 - 改进事件处理
            let toggleBtn = null;
            if (e.target.classList.contains('factory-toggle-btn')) {
                toggleBtn = e.target;
            } else if (e.target.closest('.factory-toggle-btn')) {
                toggleBtn = e.target.closest('.factory-toggle-btn');
            }

            if (toggleBtn) {
                e.preventDefault();
                e.stopPropagation();
                const factoryId = toggleBtn.dataset.factoryId;
                console.log('点击厂区折叠按钮:', factoryId); // 调试日志
                this.toggleFactory(factoryId);
                return;
            }
        };

        // 绑定事件监听器
        document.addEventListener('click', this.clickHandler);

        // 页面级刷新按钮 - 添加防抖机制
        const refreshBtn = document.getElementById('refreshHealthBtn');
        if (refreshBtn) {
            // 移除之前的事件监听器
            refreshBtn.removeEventListener('click', this.debouncedRefresh);

            // 创建防抖刷新函数
            this.debouncedRefresh = this.debounce(() => {
                if (this.isLoading) {
                    console.log('正在加载中，跳过重复请求');
                    return;
                }
                console.log('执行设备健康度数据刷新');
                this.loadHealthOverview();
            }, 1000); // 1秒防抖

            // 绑定新的事件监听器
            refreshBtn.addEventListener('click', this.debouncedRefresh);
        }
    }

    // 加载设备健康度概览
    async loadHealthOverview() {
        if (this.isLoading) {
            console.log('正在加载中，跳过重复请求');
            return;
        }

        try {
            this.isLoading = true;
            console.log('开始加载设备健康度概览数据');

            // 显示加载状态
            this.showLoadingState();

            // 获取当前用户信息用于认证
            const currentUser = sessionStorage.getItem('username') || window.currentUser;
            const currentRole = sessionStorage.getItem('role') || window.currentRole;

            if (!currentUser) {
                throw new Error('用户未登录，请重新登录');
            }

            // 构建带认证参数的URL
            const params = new URLSearchParams({
                username: currentUser,
                role: currentRole || 'user'
            });

            const response = await fetch(`/api/device-health/overview?${params.toString()}`);
            const result = await response.json();

            if (result.success) {
                this.renderHealthOverview(result.data);
                console.log('设备健康度概览数据加载成功');
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('加载设备健康度概览失败:', error);
            this.showError('加载设备健康度概览失败: ' + error.message);
        } finally {
            this.isLoading = false;
        }
    }

    // 显示加载状态
    showLoadingState() {
        const container = document.getElementById('healthOverviewContainer');
        if (container) {
            container.innerHTML = `
                <div class="loading-placeholder">
                    <div class="loading-spinner"></div>
                    <p>正在刷新设备健康度数据...</p>
                </div>
            `;
        }
    }

    // 渲染健康度概览
    renderHealthOverview(data) {
        const container = document.getElementById('healthOverviewContainer');
        if (!container) return;

        // 渲染统计摘要
        const summaryHTML = `
            <div class="health-summary">
                <div class="summary-cards">
                    <div class="summary-card excellent">
                        <div class="card-number">${data.summary.excellent}</div>
                        <div class="card-label">优秀</div>
                    </div>
                    <div class="summary-card good">
                        <div class="card-number">${data.summary.good}</div>
                        <div class="card-label">良好</div>
                    </div>
                    <div class="summary-card fair">
                        <div class="card-number">${data.summary.fair}</div>
                        <div class="card-label">一般</div>
                    </div>
                    <div class="summary-card poor">
                        <div class="card-number">${data.summary.poor}</div>
                        <div class="card-label">较差</div>
                    </div>
                    <div class="summary-card critical">
                        <div class="card-number">${data.summary.critical}</div>
                        <div class="card-label">危险</div>
                    </div>
                </div>
            </div>
        `;

        // 渲染按厂区分组的设备列表
        const factoriesHTML = this.renderFactoryGroups(data.devicesByFactory);

        container.innerHTML = summaryHTML + `
            <div class="health-devices-list">
                <div class="list-header">
                    <h3>设备健康度列表</h3>
                </div>
                <div class="factory-groups">
                    ${factoriesHTML}
                </div>
            </div>
        `;

        // 重新绑定事件
        this.bindEvents();

        // 生成所有厂区的页码按钮
        this.generateAllFactoryPageNumbers(data.devicesByFactory);
    }

    // 渲染厂区分组
    renderFactoryGroups(devicesByFactory) {
        if (!devicesByFactory || Object.keys(devicesByFactory).length === 0) {
            return '<div class="no-factories">暂无设备数据</div>';
        }

        return Object.values(devicesByFactory).map(factory => {
            const isCollapsed = !this.collapsedFactories.has(factory.factoryId); // 默认折叠，Set中记录展开的厂区
            const deviceCount = factory.devices.length;

            // 初始化厂区页码
            if (!this.factoryPages[factory.factoryId]) {
                this.factoryPages[factory.factoryId] = 1;
            }

            // 计算厂区健康度统计
            const factoryStats = this.calculateFactoryStats(factory.devices);

            // 计算分页
            const currentPage = this.factoryPages[factory.factoryId];
            const totalPages = Math.ceil(deviceCount / this.pageSize);
            const startIndex = (currentPage - 1) * this.pageSize;
            const endIndex = startIndex + this.pageSize;
            const pageDevices = factory.devices.slice(startIndex, endIndex);

            return `
                <div class="factory-group" data-factory-id="${factory.factoryId}">
                    <div class="factory-header">
                        <button class="factory-toggle-btn" data-factory-id="${factory.factoryId}">
                            <svg class="toggle-icon ${isCollapsed ? 'collapsed' : 'expanded'}" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7" />
                            </svg>
                            <span class="factory-name">${factory.factoryName}</span>
                            <span class="device-count">(${deviceCount}台设备)</span>
                        </button>
                        <div class="factory-stats">
                            <span class="stat excellent" title="优秀">${factoryStats.excellent}</span>
                            <span class="stat good" title="良好">${factoryStats.good}</span>
                            <span class="stat fair" title="一般">${factoryStats.fair}</span>
                            <span class="stat poor" title="较差">${factoryStats.poor}</span>
                            <span class="stat critical" title="危险">${factoryStats.critical}</span>
                        </div>
                    </div>
                    <div class="factory-devices ${isCollapsed ? 'collapsed' : 'expanded'}">
                        <div class="devices-grid">
                            ${pageDevices.map(device => this.renderDeviceCard(device)).join('')}
                        </div>
                        ${totalPages > 1 ? this.renderFactoryPagination(factory.factoryId, currentPage, totalPages, deviceCount) : ''}
                    </div>
                </div>
            `;
        }).join('');
    }

    // 渲染单个设备卡片
    renderDeviceCard(device) {
        return `
            <div class="health-device-item" data-device-id="${device.deviceId}">
                <div class="device-basic-info">
                    <div class="device-name">${device.deviceName}</div>
                    <div class="device-code">${device.deviceCode}</div>
                    <div class="device-location">${device.location}</div>
                </div>
                <div class="health-score-display">
                    <div class="score-circle" style="border-color: ${device.healthLevel.color}">
                        <span class="score-number">${device.healthScore}</span>
                    </div>
                    <div class="health-level" style="color: ${device.healthLevel.color}">
                        ${device.healthLevel.text}
                    </div>
                </div>
                <div class="device-actions">
                    <button class="action-btn health-detail-btn" data-device-id="${device.deviceId}">
                        详情
                    </button>
                    <button class="action-btn maintenance-plan-btn" data-device-id="${device.deviceId}">
                        维护计划
                    </button>
                </div>
            </div>
        `;
    }

    // 计算厂区统计信息
    calculateFactoryStats(devices) {
        return {
            excellent: devices.filter(d => d.healthScore >= 90).length,
            good: devices.filter(d => d.healthScore >= 80 && d.healthScore < 90).length,
            fair: devices.filter(d => d.healthScore >= 70 && d.healthScore < 80).length,
            poor: devices.filter(d => d.healthScore >= 60 && d.healthScore < 70).length,
            critical: devices.filter(d => d.healthScore < 60).length
        };
    }

    // 渲染厂区分页控件
    renderFactoryPagination(factoryId, currentPage, totalPages, totalDevices) {
        return `
            <div class="factory-pagination">
                <div class="pagination-info">
                    共 <span class="font-medium">${totalDevices}</span> 台设备，每页 ${this.pageSize} 台
                </div>
                <div class="pagination-controls">
                    <button class="pagination-btn"
                            data-action="factory-page"
                            data-factory-id="${factoryId}"
                            data-page="${currentPage - 1}"
                            ${currentPage <= 1 ? 'disabled' : ''}>
                        上一页
                    </button>
                    <div class="factory-page-numbers" data-factory-id="${factoryId}"></div>
                    <button class="pagination-btn"
                            data-action="factory-page"
                            data-factory-id="${factoryId}"
                            data-page="${currentPage + 1}"
                            ${currentPage >= totalPages ? 'disabled' : ''}>
                        下一页
                    </button>
                </div>
            </div>
        `;
    }



    // 切换厂区折叠状态
    toggleFactory(factoryId) {
        console.log('toggleFactory 被调用，厂区ID:', factoryId); // 调试日志

        // 切换折叠状态（现在Set记录展开的厂区）
        const wasExpanded = this.collapsedFactories.has(factoryId);
        if (wasExpanded) {
            this.collapsedFactories.delete(factoryId);
            console.log('折叠厂区:', factoryId);
        } else {
            this.collapsedFactories.add(factoryId);
            console.log('展开厂区:', factoryId);
        }

        // 更新UI
        const factoryGroup = document.querySelector(`[data-factory-id="${factoryId}"]`);
        if (!factoryGroup) {
            console.error('找不到厂区元素:', factoryId);
            return;
        }

        const toggleIcon = factoryGroup.querySelector('.toggle-icon');
        const devicesContainer = factoryGroup.querySelector('.factory-devices');

        if (!toggleIcon || !devicesContainer) {
            console.error('找不到切换图标或设备容器');
            return;
        }

        const isNowExpanded = this.collapsedFactories.has(factoryId);
        console.log('当前展开状态:', isNowExpanded);

        if (isNowExpanded) {
            // 展开状态
            toggleIcon.classList.remove('collapsed');
            toggleIcon.classList.add('expanded');
            devicesContainer.classList.remove('collapsed');
            devicesContainer.classList.add('expanded');
        } else {
            // 折叠状态
            toggleIcon.classList.remove('expanded');
            toggleIcon.classList.add('collapsed');
            devicesContainer.classList.remove('expanded');
            devicesContainer.classList.add('collapsed');
        }
    }

    // 厂区分页跳转
    goToFactoryPage(factoryId, page) {
        console.log('goToFactoryPage 被调用，厂区ID:', factoryId, '页码:', page);

        // 更新厂区页码
        this.factoryPages[factoryId] = page;

        // 重新渲染整个健康度概览（保持当前状态）
        this.loadHealthOverview();
    }

    // 生成所有厂区的页码按钮
    generateAllFactoryPageNumbers(devicesByFactory) {
        if (!devicesByFactory) return;

        Object.values(devicesByFactory).forEach(factory => {
            this.generateFactoryPageNumbers(factory.factoryId, factory.devices.length);
        });
    }

    // 生成单个厂区的页码按钮
    generateFactoryPageNumbers(factoryId, totalDevices) {
        const pageNumbersContainer = document.querySelector(`.factory-page-numbers[data-factory-id="${factoryId}"]`);
        if (!pageNumbersContainer) return;

        const currentPage = this.factoryPages[factoryId] || 1;
        const totalPages = Math.ceil(totalDevices / this.pageSize);

        pageNumbersContainer.innerHTML = '';

        // 确定要显示的页码范围
        let startPage = Math.max(1, currentPage - 2);
        let endPage = Math.min(totalPages, startPage + 4);

        // 调整起始页，确保始终显示5个页码（如果有足够的页数）
        if (endPage - startPage < 4 && totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        // 添加第一页按钮（如果不在显示范围内）
        if (startPage > 1) {
            const firstPageBtn = document.createElement('button');
            firstPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            firstPageBtn.textContent = '1';
            firstPageBtn.onclick = () => this.goToFactoryPage(factoryId, 1);
            pageNumbersContainer.appendChild(firstPageBtn);

            // 添加省略号（如果第一页和起始页之间有间隔）
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }
        }

        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `px-3 py-1 border rounded-md ${i === currentPage ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToFactoryPage(factoryId, i);
            pageNumbersContainer.appendChild(pageBtn);
        }

        // 添加最后一页按钮（如果不在显示范围内）
        if (endPage < totalPages) {
            // 添加省略号（如果结束页和最后一页之间有间隔）
            if (endPage < totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }

            const lastPageBtn = document.createElement('button');
            lastPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            lastPageBtn.textContent = totalPages;
            lastPageBtn.onclick = () => this.goToFactoryPage(factoryId, totalPages);
            pageNumbersContainer.appendChild(lastPageBtn);
        }
    }



    // 显示设备健康度详情
    async showHealthDetail(deviceId) {
        try {
            // 获取当前用户信息用于认证
            const currentUser = sessionStorage.getItem('username') || window.currentUser;
            const currentRole = sessionStorage.getItem('role') || window.currentRole;

            if (!currentUser) {
                throw new Error('用户未登录，请重新登录');
            }

            // 构建带认证参数的URL
            const params = new URLSearchParams({
                username: currentUser,
                role: currentRole || 'user'
            });

            const [healthResponse, predictionResponse] = await Promise.all([
                fetch(`/api/device-health/${deviceId}?${params.toString()}`),
                fetch(`/api/device-health/${deviceId}/prediction?${params.toString()}`)
            ]);

            const healthResult = await healthResponse.json();
            const predictionResult = await predictionResponse.json();

            if (healthResult.success && predictionResult.success) {
                this.renderHealthDetailModal(healthResult.data, predictionResult.data);
            } else {
                throw new Error(healthResult.message || predictionResult.message);
            }
        } catch (error) {
            console.error('获取设备健康度详情失败:', error);
            this.showError('获取设备健康度详情失败: ' + error.message);
        }
    }

    // 移除已存在的模态框
    removeExistingModal(modalType) {
        const existingModal = document.querySelector(`[data-modal-type="${modalType}"]`);
        if (existingModal) {
            existingModal.remove();
        }
    }

    // 绑定模态框关闭事件
    bindModalCloseEvents(modal) {
        // 点击背景关闭
        modal.addEventListener('click', (e) => {
            if (e.target === modal) {
                e.preventDefault();
                e.stopPropagation();
                this.closeModal(modal);
            }
        });

        // 点击关闭按钮
        const closeButtons = modal.querySelectorAll('.modal-close');
        closeButtons.forEach(button => {
            button.addEventListener('click', (e) => {
                e.preventDefault();
                e.stopPropagation();
                this.closeModal(modal);
            });
        });
    }

    // 添加ESC键关闭功能
    addEscapeKeyHandler(modal) {
        const escapeHandler = (e) => {
            if (e.key === 'Escape') {
                e.preventDefault();
                this.closeModal(modal);
                document.removeEventListener('keydown', escapeHandler);
            }
        };
        document.addEventListener('keydown', escapeHandler);

        // 将处理器存储在模态框上，以便后续清理
        modal._escapeHandler = escapeHandler;
    }

    // 关闭模态框
    closeModal(modal) {
        if (modal && modal.parentNode) {
            // 移除ESC键监听器
            if (modal._escapeHandler) {
                document.removeEventListener('keydown', modal._escapeHandler);
            }

            // 移除模态框
            modal.remove();

            // 恢复body滚动
            document.body.classList.remove('modal-open');
        }
    }

    // 渲染健康度详情模态框
    renderHealthDetailModal(healthData, predictionData) {
        // 先移除已存在的模态框
        this.removeExistingModal('health-detail-modal');

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.setAttribute('data-modal-type', 'health-detail-modal');
        modal.innerHTML = `
            <div class="modal-content health-detail-modal">
                <div class="modal-header">
                    <h3>设备健康度详情</h3>
                    <button class="modal-close" type="button">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="health-overview">
                        <div class="health-score-large">
                            <div class="score-circle-large" style="border-color: ${healthData.level.color}">
                                <span class="score-number-large">${healthData.score}</span>
                            </div>
                            <div class="health-level-large" style="color: ${healthData.level.color}">
                                ${healthData.level.text}
                            </div>
                        </div>
                        <div class="health-details">
                            <!-- 各维度评分 -->
                            <div class="detail-item">
                                <span class="detail-label">设备年龄评分:</span>
                                <span class="detail-value">${healthData.details.ageScore}分</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">维修频率评分:</span>
                                <span class="detail-value">${healthData.details.maintenanceScore}分</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">故障严重程度评分:</span>
                                <span class="detail-value">${healthData.details.faultScore}分</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">保养情况评分:</span>
                                <span class="detail-value">${healthData.details.upkeepScore}分</span>
                            </div>

                            <!-- 总得分计算过程 -->
                            <div class="calculation-section">
                                <div class="calculation-header">
                                    <span class="calculation-title">总得分计算</span>
                                </div>
                                <div class="calculation-formula">
                                    <div class="formula-text">
                                        ${healthData.details.ageScore} × 20％ + ${healthData.details.maintenanceScore} × 30％ + ${healthData.details.faultScore} × 30％ + ${healthData.details.upkeepScore} × 20％
                                    </div>
                                    <div class="formula-result">
                                        = ${(healthData.details.ageScore * 0.20).toFixed(1)} + ${(healthData.details.maintenanceScore * 0.30).toFixed(1)} + ${(healthData.details.faultScore * 0.30).toFixed(1)} + ${(healthData.details.upkeepScore * 0.20).toFixed(1)} = <strong>${healthData.score}分</strong>
                                    </div>
                                </div>
                            </div>

                            <!-- 设备基础信息 -->
                            <div class="detail-item">
                                <span class="detail-label">设备年龄:</span>
                                <span class="detail-value">${healthData.details.deviceAge.toFixed(1)}年</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">维修记录:</span>
                                <span class="detail-value">${healthData.details.maintenanceRecords}次</span>
                            </div>
                            <div class="detail-item">
                                <span class="detail-label">保养记录:</span>
                                <span class="detail-value">${healthData.details.upkeepRecords}次</span>
                            </div>
                        </div>
                    </div>

                    <div class="prediction-section">
                        <h4>故障预测</h4>
                        <div class="prediction-content">
                            ${predictionData.prediction ? `
                                <div class="prediction-item">
                                    <span class="prediction-label">预测下次故障时间:</span>
                                    <span class="prediction-value">${new Date(predictionData.prediction).toLocaleDateString('zh-CN')}</span>
                                </div>
                                <div class="prediction-item">
                                    <span class="prediction-label">预测置信度:</span>
                                    <span class="prediction-value">${predictionData.confidence}%</span>
                                </div>
                                <div class="prediction-item">
                                    <span class="prediction-label">平均故障间隔:</span>
                                    <span class="prediction-value">${predictionData.avgInterval}天</span>
                                </div>
                            ` : `
                                <div class="no-prediction">
                                    ${predictionData.message}
                                </div>
                            `}
                        </div>
                    </div>

                    <div class="recommendations-section">
                        <h4>维护建议</h4>
                        <div class="recommendations-list">
                            ${healthData.recommendations.map(rec => `
                                <div class="recommendation-item ${rec.priority}">
                                    <div class="rec-priority">${rec.priority === 'high' ? '高' : rec.priority === 'medium' ? '中' : '低'}</div>
                                    <div class="rec-message">${rec.message}</div>
                                </div>
                            `).join('')}
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close" type="button">关闭</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.classList.add('modal-open');

        // 绑定关闭事件 - 使用更精确的事件处理
        this.bindModalCloseEvents(modal);

        // 添加ESC键关闭功能
        this.addEscapeKeyHandler(modal);
    }

    // 显示维护计划
    async showMaintenancePlan(deviceId) {
        try {
            const response = await fetch(`/api/device-health/${deviceId}/maintenance-plan`);
            const result = await response.json();

            if (result.success) {
                this.renderMaintenancePlanModal(result.data);
            } else {
                throw new Error(result.message);
            }
        } catch (error) {
            console.error('获取维护计划失败:', error);
            this.showError('获取维护计划失败: ' + error.message);
        }
    }

    // 渲染维护计划模态框
    renderMaintenancePlanModal(planData) {
        // 先移除已存在的模态框
        this.removeExistingModal('maintenance-plan-modal');

        const modal = document.createElement('div');
        modal.className = 'modal-overlay';
        modal.setAttribute('data-modal-type', 'maintenance-plan-modal');
        modal.innerHTML = `
            <div class="modal-content maintenance-plan-modal">
                <div class="modal-header">
                    <h3>设备维护计划</h3>
                    <button class="modal-close" type="button">&times;</button>
                </div>
                <div class="modal-body">
                    <div class="plan-overview">
                        <div class="plan-item">
                            <span class="plan-label">当前健康度</span>
                            <span class="plan-value">${planData.healthScore}分</span>
                        </div>
                        <div class="plan-item">
                            <span class="plan-label">下次复查时间</span>
                            <span class="plan-value">${new Date(planData.nextReview).toLocaleDateString('zh-CN')}</span>
                        </div>
                    </div>

                    <div class="plan-sections">
                        <div class="plan-section immediate">
                            <h4>立即执行</h4>
                            <div class="plan-actions">
                                ${planData.plan.immediate.length > 0 ?
                                    planData.plan.immediate.map(action => `
                                        <div class="plan-action ${action.priority}">
                                            <div class="action-title">${action.action}</div>
                                            <div class="action-reason">${action.reason}</div>
                                        </div>
                                    `).join('') :
                                    '<div class="no-actions">暂无需要立即执行的维护任务</div>'
                                }
                            </div>
                        </div>

                        <div class="plan-section short-term">
                            <h4>1个月内</h4>
                            <div class="plan-actions">
                                ${planData.plan.shortTerm.length > 0 ?
                                    planData.plan.shortTerm.map(action => `
                                        <div class="plan-action ${action.priority}">
                                            <div class="action-title">${action.action}</div>
                                            <div class="action-reason">${action.reason}</div>
                                        </div>
                                    `).join('') :
                                    '<div class="no-actions">暂无短期维护计划</div>'
                                }
                            </div>
                        </div>

                        <div class="plan-section long-term">
                            <h4>3个月内</h4>
                            <div class="plan-actions">
                                ${planData.plan.longTerm.length > 0 ?
                                    planData.plan.longTerm.map(action => `
                                        <div class="plan-action ${action.priority}">
                                            <div class="action-title">${action.action}</div>
                                            <div class="action-reason">${action.reason}</div>
                                        </div>
                                    `).join('') :
                                    '<div class="no-actions">暂无长期维护计划</div>'
                                }
                            </div>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button class="btn btn-secondary modal-close" type="button">关闭</button>
                    <button class="btn btn-primary" type="button" onclick="window.print()">打印计划</button>
                </div>
            </div>
        `;

        document.body.appendChild(modal);
        document.body.classList.add('modal-open');

        // 绑定关闭事件 - 使用更精确的事件处理
        this.bindModalCloseEvents(modal);

        // 添加ESC键关闭功能
        this.addEscapeKeyHandler(modal);
    }







    // 显示错误信息
    showError(message) {
        // 可以使用现有的通知系统
        if (window.showNotification) {
            window.showNotification(message, 'error');
        } else {
            alert('错误: ' + message);
        }
    }

    // 显示信息
    showInfo(message) {
        if (window.showNotification) {
            window.showNotification(message, 'info');
        } else {
            alert(message);
        }
    }


}

// 初始化设备健康度管理器
document.addEventListener('DOMContentLoaded', () => {
    // 不在这里自动初始化，而是在showSection函数中按需初始化
});
