/**
 * 模态框高度控制模块
 * 用于优化模态框高度，确保在各种设备上都能正确显示
 * 防止模态框内容过多时超出浏览器视口
 */

// 调整模态框高度，确保不超出视口
function adjustModalHeight(modalId) {
    const modal = document.getElementById(modalId);
    if (!modal) return;

    // 获取模态框内容容器
    const modalContent = modal.querySelector('.relative') || modal.querySelector('.bg-white');
    if (!modalContent) return;

    // 获取视口高度
    const viewportHeight = window.innerHeight;

    // 计算模态框最大高度（视口高度的90%）
    const maxModalHeight = viewportHeight * 0.9;

    // 设置模态框最大高度
    modalContent.style.maxHeight = `${maxModalHeight}px`;

    // 确保内容区域可滚动
    modalContent.style.overflowY = 'auto';

    // 计算内容区域的最大高度
    // 减去标题栏和底部按钮区域的高度
    const headerHeight = modalContent.querySelector('.flex.justify-between') ?
                         modalContent.querySelector('.flex.justify-between').offsetHeight : 0;
    const footerHeight = modalContent.querySelector('.mt-6') ?
                         modalContent.querySelector('.mt-6').offsetHeight : 0;

    // 内容区域最大高度 = 模态框最大高度 - 标题栏高度 - 底部按钮区域高度 - 内边距
    const contentMaxHeight = maxModalHeight - headerHeight - footerHeight - 48; // 48px为上下内边距

    // 查找内容区域
    const contentArea = modalContent.querySelector('.whitespace-pre-wrap') ||
                        modalContent.querySelector('.overflow-y-auto');

    if (contentArea) {
        contentArea.style.maxHeight = `${contentMaxHeight}px`;
        contentArea.style.overflowY = 'auto';
    }

    // 调整模态框位置，确保垂直居中
    modalContent.style.margin = 'auto';

    console.log(`已调整模态框 ${modalId} 的高度，最大高度: ${maxModalHeight}px`);
}

// 为详情模态框添加标签页内容区域高度控制
function adjustDetailTabsHeight() {
    const detailModal = document.getElementById('detailModal');
    if (!detailModal) return;

    const modalContent = detailModal.querySelector('.relative');
    if (!modalContent) return;

    // 获取标签页内容区域
    const tabContents = modalContent.querySelectorAll('[id^="content-"]');
    if (!tabContents.length) return;

    // 获取标题栏和标签栏的高度
    const headerHeight = modalContent.querySelector('.flex.justify-between').offsetHeight;
    const tabsHeight = modalContent.querySelector('.border-b.flex').offsetHeight;

    // 计算标签页内容区域的最大高度
    const viewportHeight = window.innerHeight;
    const maxContentHeight = viewportHeight * 0.9 - headerHeight - tabsHeight - 48; // 48px为上下内边距

    // 设置所有标签页内容区域的最大高度
    tabContents.forEach(content => {
        content.style.maxHeight = `${maxContentHeight}px`;
        content.style.overflowY = 'auto';
    });

    console.log(`已调整详情模态框标签页内容区域高度，最大高度: ${maxContentHeight}px`);
}

// 监听窗口大小变化，动态调整模态框高度
function setupModalResizeHandlers() {
    // 需要处理的模态框ID列表
    const modalIds = ['detailModal', 'editModal', 'previewModal', 'applicationTemplateModal', 'contentModal', 'editUserModal', 'addUserModal'];

    // 窗口大小变化时调整所有模态框高度
    window.addEventListener('resize', () => {
        modalIds.forEach(id => {
            const modal = document.getElementById(id);
            if (modal && !modal.classList.contains('hidden')) {
                adjustModalHeight(id);

                // 如果是详情模态框，还需要调整标签页内容区域高度
                if (id === 'detailModal') {
                    adjustDetailTabsHeight();
                }
            }
        });
    });

    console.log('已设置模态框大小自适应处理程序');
}

// 在模态框打开时调整高度
function setupModalOpenHandlers() {
    // 详情模态框打开时
    const originalShowDetail = window.showDetail;
    if (typeof originalShowDetail === 'function') {
        window.showDetail = function(appId) {
            originalShowDetail(appId);
            setTimeout(() => {
                adjustModalHeight('detailModal');
                adjustDetailTabsHeight();
            }, 10);
        };
    }

    // 内容模态框打开时
    const originalShowContentModal = window.showContentModal;
    if (typeof originalShowContentModal === 'function') {
        window.showContentModal = function(content) {
            originalShowContentModal(content);
            setTimeout(() => adjustModalHeight('contentModal'), 10);
        };
    }

    // 申请书模板预览模态框打开时
    const originalPreviewApplicationTemplate = window.previewApplicationTemplate;
    if (typeof originalPreviewApplicationTemplate === 'function') {
        window.previewApplicationTemplate = function(appId) {
            originalPreviewApplicationTemplate(appId);
            setTimeout(() => adjustModalHeight('applicationTemplateModal'), 10);
        };
    }

    console.log('已设置模态框打开时的高度调整处理程序');
}

// 页面加载完成后初始化
document.addEventListener('DOMContentLoaded', function() {
    // 设置模态框大小自适应处理程序
    setupModalResizeHandlers();

    // 设置模态框打开时的高度调整处理程序
    setupModalOpenHandlers();

    console.log('模态框高度控制模块已初始化');
});
