const crypto = require('crypto');

/**
 * 会话管理器
 * 提供JWT和Session的混合认证机制，增强会话安全性
 */
class SessionManager {
    constructor(logger) {
        this.logger = logger;
        
        // 会话存储（生产环境建议使用Redis）
        this.sessions = new Map();
        
        // 会话配置
        this.config = {
            // 会话超时时间（分钟）
            sessionTimeout: 30,
            // 最大并发会话数
            maxConcurrentSessions: 3,
            // 会话续期阈值（分钟）
            renewalThreshold: 10,
            // 强制单点登录
            forceSingleSession: false,
            // 会话安全选项
            security: {
                // IP地址验证
                validateIP: true,
                // User-Agent验证
                validateUserAgent: true,
                // 会话指纹验证
                validateFingerprint: true
            }
        };
        
        // 启动会话清理任务
        this.startSessionCleanup();
    }

    /**
     * 创建新会话
     */
    createSession(user, clientInfo = {}) {
        const sessionId = this.generateSessionId();
        const now = Date.now();
        
        const session = {
            id: sessionId,
            userId: user.id,
            username: user.username,
            role: user.role,
            department: user.department,
            
            // 时间信息
            createdAt: now,
            lastAccessAt: now,
            expiresAt: now + (this.config.sessionTimeout * 60 * 1000),
            
            // 客户端信息
            clientInfo: {
                ip: clientInfo.ip || '127.0.0.1',
                userAgent: clientInfo.userAgent || 'Unknown',
                fingerprint: clientInfo.fingerprint || this.generateFingerprint(clientInfo)
            },
            
            // 安全信息
            security: {
                loginTime: now,
                accessCount: 1,
                lastActivity: 'login',
                riskScore: 0
            }
        };
        
        // 检查并处理并发会话限制
        this.handleConcurrentSessions(user.id, sessionId);
        
        // 存储会话
        this.sessions.set(sessionId, session);
        
        this.logger.info('Session created', {
            sessionId,
            userId: user.id,
            username: user.username,
            ip: session.clientInfo.ip
        });
        
        return session;
    }

    /**
     * 验证会话
     */
    validateSession(sessionId, clientInfo = {}) {
        const session = this.sessions.get(sessionId);
        
        if (!session) {
            return { valid: false, reason: 'session_not_found' };
        }
        
        const now = Date.now();
        
        // 检查会话是否过期
        if (now > session.expiresAt) {
            this.sessions.delete(sessionId);
            this.logger.info('Session expired', { sessionId, userId: session.userId });
            return { valid: false, reason: 'session_expired' };
        }
        
        // 安全验证
        const securityCheck = this.performSecurityChecks(session, clientInfo);
        if (!securityCheck.passed) {
            this.logger.warn('Session security check failed', {
                sessionId,
                userId: session.userId,
                reason: securityCheck.reason,
                clientInfo
            });
            return { valid: false, reason: securityCheck.reason };
        }
        
        // 更新会话活动信息
        session.lastAccessAt = now;
        session.security.accessCount++;
        session.security.lastActivity = 'access';
        
        // 检查是否需要续期
        const timeUntilExpiry = session.expiresAt - now;
        const renewalThresholdMs = this.config.renewalThreshold * 60 * 1000;
        
        if (timeUntilExpiry < renewalThresholdMs) {
            session.expiresAt = now + (this.config.sessionTimeout * 60 * 1000);
            this.logger.debug('Session renewed', { sessionId, newExpiresAt: session.expiresAt });
        }
        
        return { 
            valid: true, 
            session,
            renewed: timeUntilExpiry < renewalThresholdMs
        };
    }

    /**
     * 执行安全检查
     */
    performSecurityChecks(session, clientInfo) {
        const checks = [];
        
        // IP地址验证
        if (this.config.security.validateIP && clientInfo.ip) {
            if (session.clientInfo.ip !== clientInfo.ip) {
                return { passed: false, reason: 'ip_mismatch' };
            }
            checks.push('ip_validated');
        }
        
        // User-Agent验证
        if (this.config.security.validateUserAgent && clientInfo.userAgent) {
            if (session.clientInfo.userAgent !== clientInfo.userAgent) {
                return { passed: false, reason: 'user_agent_mismatch' };
            }
            checks.push('user_agent_validated');
        }
        
        // 会话指纹验证
        if (this.config.security.validateFingerprint && clientInfo.fingerprint) {
            if (session.clientInfo.fingerprint !== clientInfo.fingerprint) {
                return { passed: false, reason: 'fingerprint_mismatch' };
            }
            checks.push('fingerprint_validated');
        }
        
        return { passed: true, checks };
    }

    /**
     * 处理并发会话限制
     */
    handleConcurrentSessions(userId, newSessionId) {
        const userSessions = this.getUserSessions(userId);
        
        if (this.config.forceSingleSession) {
            // 强制单点登录，终止所有其他会话
            for (const session of userSessions) {
                this.terminateSession(session.id, 'single_session_enforced');
            }
        } else if (userSessions.length >= this.config.maxConcurrentSessions) {
            // 达到最大并发数，终止最旧的会话
            userSessions.sort((a, b) => a.lastAccessAt - b.lastAccessAt);
            const sessionsToTerminate = userSessions.slice(0, userSessions.length - this.config.maxConcurrentSessions + 1);
            
            for (const session of sessionsToTerminate) {
                this.terminateSession(session.id, 'max_concurrent_sessions_exceeded');
            }
        }
    }

    /**
     * 获取用户的所有会话
     */
    getUserSessions(userId) {
        const userSessions = [];
        for (const session of this.sessions.values()) {
            if (session.userId === userId) {
                userSessions.push(session);
            }
        }
        return userSessions;
    }

    /**
     * 终止会话
     */
    terminateSession(sessionId, reason = 'manual') {
        const session = this.sessions.get(sessionId);
        if (session) {
            this.sessions.delete(sessionId);
            this.logger.info('Session terminated', {
                sessionId,
                userId: session.userId,
                reason
            });
            return true;
        }
        return false;
    }

    /**
     * 终止用户的所有会话
     */
    terminateUserSessions(userId, reason = 'manual') {
        const userSessions = this.getUserSessions(userId);
        let terminatedCount = 0;
        
        for (const session of userSessions) {
            if (this.terminateSession(session.id, reason)) {
                terminatedCount++;
            }
        }
        
        return terminatedCount;
    }

    /**
     * 生成会话ID
     */
    generateSessionId() {
        return crypto.randomBytes(32).toString('hex');
    }

    /**
     * 生成客户端指纹
     */
    generateFingerprint(clientInfo) {
        const data = [
            clientInfo.ip || '',
            clientInfo.userAgent || '',
            clientInfo.acceptLanguage || '',
            clientInfo.acceptEncoding || '',
            clientInfo.screenResolution || ''
        ].join('|');
        
        return crypto.createHash('sha256').update(data).digest('hex');
    }

    /**
     * 启动会话清理任务
     */
    startSessionCleanup() {
        // 每5分钟清理一次过期会话
        setInterval(() => {
            this.cleanupExpiredSessions();
        }, 5 * 60 * 1000);
    }

    /**
     * 清理过期会话
     */
    cleanupExpiredSessions() {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const [sessionId, session] of this.sessions.entries()) {
            if (now > session.expiresAt) {
                this.sessions.delete(sessionId);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            this.logger.info('Expired sessions cleaned', { count: cleanedCount });
        }
    }

    /**
     * 获取会话统计信息
     */
    getSessionStats() {
        const now = Date.now();
        const stats = {
            totalSessions: this.sessions.size,
            activeSessions: 0,
            expiringSoon: 0,
            userDistribution: {},
            averageSessionDuration: 0
        };
        
        let totalDuration = 0;
        
        for (const session of this.sessions.values()) {
            // 活跃会话
            if (now <= session.expiresAt) {
                stats.activeSessions++;
                
                // 即将过期的会话（10分钟内）
                if (session.expiresAt - now < 10 * 60 * 1000) {
                    stats.expiringSoon++;
                }
                
                // 用户分布
                const userId = session.userId;
                stats.userDistribution[userId] = (stats.userDistribution[userId] || 0) + 1;
                
                // 会话持续时间
                totalDuration += now - session.createdAt;
            }
        }
        
        if (stats.activeSessions > 0) {
            stats.averageSessionDuration = Math.round(totalDuration / stats.activeSessions / 1000 / 60); // 分钟
        }
        
        return stats;
    }

    /**
     * 获取会话详情
     */
    getSessionDetails(sessionId) {
        const session = this.sessions.get(sessionId);
        if (!session) {
            return null;
        }
        
        const now = Date.now();
        return {
            ...session,
            isExpired: now > session.expiresAt,
            timeUntilExpiry: Math.max(0, session.expiresAt - now),
            duration: now - session.createdAt
        };
    }

    /**
     * 更新会话配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.logger.info('Session manager configuration updated', { config: this.config });
    }

    /**
     * 获取配置
     */
    getConfig() {
        return { ...this.config };
    }
}

module.exports = SessionManager;
