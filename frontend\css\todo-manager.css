/**
 * 待办事项管理器样式
 * 为待办事项功能提供美观和响应式的界面样式
 */

/* 待办事项容器样式 */
.todo-item {
    display: flex;
    align-items: flex-start;
    padding: 12px;
    margin-bottom: 8px;
    border-radius: 8px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    transition: all 0.2s ease;
    cursor: pointer;
    position: relative;
}

.todo-item:hover {
    background: #f9fafb;
    border-color: #d1d5db;
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.todo-item:active {
    transform: translateY(0);
}

/* 待办事项图标 */
.todo-icon {
    width: 32px;
    height: 32px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 14px;
    margin-right: 12px;
    flex-shrink: 0;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

/* 待办事项内容 */
.todo-content {
    flex: 1;
    min-width: 0;
}

.todo-text {
    font-size: 14px;
    font-weight: 500;
    color: #374151;
    line-height: 1.4;
    margin-bottom: 4px;
    word-wrap: break-word;
}

.todo-meta {
    display: flex;
    align-items: center;
    gap: 8px;
    flex-wrap: wrap;
}

.todo-time {
    font-size: 12px;
    color: #6b7280;
}

.todo-urgent {
    background: #fef2f2;
    color: #dc2626;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 600;
    border: 1px solid #fecaca;
}

.todo-action {
    background: #eff6ff;
    color: #2563eb;
    padding: 2px 6px;
    border-radius: 4px;
    font-size: 11px;
    font-weight: 500;
    border: 1px solid #dbeafe;
}

/* 优先级样式 */
.todo-high-priority {
    border-left: 4px solid #dc2626;
    background: linear-gradient(to right, #fef2f2, #ffffff);
}

.todo-high-priority:hover {
    background: linear-gradient(to right, #fef2f2, #f9fafb);
}

.todo-high-priority .todo-icon {
    background: #dc2626 !important;
}

.todo-medium-priority {
    border-left: 4px solid #f59e0b;
    background: linear-gradient(to right, #fffbeb, #ffffff);
}

.todo-medium-priority:hover {
    background: linear-gradient(to right, #fffbeb, #f9fafb);
}

.todo-medium-priority .todo-icon {
    background: #f59e0b !important;
}

.todo-normal-priority {
    border-left: 4px solid #3b82f6;
}

.todo-normal-priority .todo-icon {
    background: #3b82f6 !important;
}

.todo-low-priority {
    border-left: 4px solid #6b7280;
    background: linear-gradient(to right, #f9fafb, #ffffff);
}

.todo-low-priority:hover {
    background: linear-gradient(to right, #f9fafb, #f3f4f6);
}

.todo-low-priority .todo-icon {
    background: #6b7280 !important;
}

/* 待办事项标题增强 */
.activity-title {
    position: relative;
}

.activity-title::after {
    content: '';
    position: absolute;
    bottom: -2px;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(to right, #3b82f6, #1d4ed8);
    border-radius: 1px;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.activity-card:hover .activity-title::after {
    opacity: 1;
}

/* 空状态样式增强 */
.empty-state {
    text-align: center;
    padding: 24px 16px;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 32px;
    margin-bottom: 8px;
    opacity: 0.7;
}

.empty-state-text {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    color: #374151;
}

.empty-state-desc {
    font-size: 12px;
    color: #9ca3af;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .todo-item {
        padding: 10px;
        margin-bottom: 6px;
    }
    
    .todo-icon {
        width: 28px;
        height: 28px;
        font-size: 12px;
        margin-right: 10px;
    }
    
    .todo-text {
        font-size: 13px;
    }
    
    .todo-meta {
        gap: 6px;
    }
    
    .todo-time,
    .todo-urgent,
    .todo-action {
        font-size: 10px;
    }
}

@media (max-width: 480px) {
    .todo-item {
        padding: 8px;
        margin-bottom: 4px;
    }
    
    .todo-icon {
        width: 24px;
        height: 24px;
        font-size: 11px;
        margin-right: 8px;
    }
    
    .todo-text {
        font-size: 12px;
        line-height: 1.3;
    }
    
    .todo-meta {
        flex-direction: column;
        align-items: flex-start;
        gap: 4px;
    }
}

/* 动画效果 */
@keyframes todoSlideIn {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.todo-item {
    animation: todoSlideIn 0.3s ease-out;
}

/* 加载状态 */
.todo-loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #6b7280;
    font-size: 14px;
}

.todo-loading::before {
    content: '';
    width: 16px;
    height: 16px;
    border: 2px solid #e5e7eb;
    border-top-color: #3b82f6;
    border-radius: 50%;
    margin-right: 8px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    to {
        transform: rotate(360deg);
    }
}

/* 错误状态 */
.todo-error {
    text-align: center;
    padding: 16px;
    color: #dc2626;
    font-size: 13px;
    background: #fef2f2;
    border: 1px solid #fecaca;
    border-radius: 6px;
    margin: 8px 0;
}

/* 待办事项计数徽章 */
.todo-count-badge {
    background: #dc2626;
    color: white;
    font-size: 10px;
    font-weight: 600;
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 6px;
    min-width: 16px;
    text-align: center;
    display: inline-block;
}

.todo-count-badge.medium {
    background: #f59e0b;
}

.todo-count-badge.normal {
    background: #3b82f6;
}

.todo-count-badge.low {
    background: #6b7280;
}

/* 待办事项操作按钮 */
.todo-actions {
    display: flex;
    gap: 4px;
    margin-top: 8px;
}

.todo-action-btn {
    background: #f3f4f6;
    border: 1px solid #d1d5db;
    color: #374151;
    padding: 4px 8px;
    border-radius: 4px;
    font-size: 11px;
    cursor: pointer;
    transition: all 0.2s ease;
}

.todo-action-btn:hover {
    background: #e5e7eb;
    border-color: #9ca3af;
}

.todo-action-btn.primary {
    background: #3b82f6;
    border-color: #2563eb;
    color: white;
}

.todo-action-btn.primary:hover {
    background: #2563eb;
}

/* 深色模式支持 */
@media (prefers-color-scheme: dark) {
    .todo-item {
        background: #1f2937;
        border-color: #374151;
        color: #f9fafb;
    }
    
    .todo-item:hover {
        background: #111827;
        border-color: #4b5563;
    }
    
    .todo-text {
        color: #f9fafb;
    }
    
    .todo-time {
        color: #9ca3af;
    }
    
    .empty-state-text {
        color: #f9fafb;
    }
    
    .empty-state-desc {
        color: #6b7280;
    }
}
