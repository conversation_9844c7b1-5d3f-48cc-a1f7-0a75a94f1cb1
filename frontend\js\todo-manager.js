/**
 * 待办事项管理器
 * 负责处理用户的待办事项功能，包括数据获取、显示和交互
 * 
 * 功能特性：
 * - 基于用户角色的待办事项展示
 * - 支持优先级分类和紧急提醒
 * - 提供快速操作入口
 * - 实时数据更新和刷新
 */

class TodoManager {
    constructor() {
        this.currentUser = null;
        this.currentRole = null;
        this.todos = [];
        this.refreshInterval = null;
        this.autoRefreshEnabled = true;
        this.autoRefreshIntervalMs = 5 * 60 * 1000; // 5分钟自动刷新
        
        this.init();
    }

    // 初始化待办事项管理器
    init() {
        console.log('待办事项管理器初始化');
        
        // 获取当前用户信息
        this.getCurrentUserInfo();
        
        // 绑定事件监听器
        this.bindEventListeners();
        
        // 启动自动刷新（如果启用）
        if (this.autoRefreshEnabled) {
            this.startAutoRefresh();
        }
    }

    // 获取当前用户信息
    getCurrentUserInfo() {
        // 从全局变量或sessionStorage获取用户信息
        this.currentUser = window.currentUser || sessionStorage.getItem('username');
        this.currentRole = window.currentRole || sessionStorage.getItem('role');

        if (!this.currentUser || !this.currentRole) {
            console.warn('待办事项管理器：无法获取用户信息');
        } else {
            console.log('待办事项管理器：用户信息获取成功', this.currentUser, this.currentRole);
        }
    }

    // 绑定事件监听器
    bindEventListeners() {
        // 监听用户登录状态变化
        document.addEventListener('userLogin', (event) => {
            this.currentUser = event.detail.username;
            this.currentRole = event.detail.role;
            this.refreshTodos();
        });

        // 监听用户登出
        document.addEventListener('userLogout', () => {
            this.currentUser = null;
            this.currentRole = null;
            this.todos = [];
            this.stopAutoRefresh();
        });

        // 监听申请状态变化（需要刷新待办事项）
        document.addEventListener('applicationStatusChanged', () => {
            this.refreshTodos();
        });
    }

    // 根据用户角色获取待办事项
    async getTodosByRole() {
        if (!this.currentUser || !this.currentRole) {
            console.warn('待办事项管理器：用户信息不完整，跳过加载');
            return [];
        }

        try {
            const response = await apiRequest(`/api/dashboard/todos?username=${this.currentUser}&role=${this.currentRole}`);

            if (response && response.success && response.data && response.data.todos) {
                return response.data.todos;
            } else {
                console.warn('获取待办事项失败:', response ? response.message : '无响应');
                return [];
            }
        } catch (error) {
            console.error('获取待办事项出错:', error);
            return [];
        }
    }

    // 刷新待办事项
    async refreshTodos() {
        try {
            this.todos = await this.getTodosByRole();
            this.updateTodoDisplay();
            
            // 触发待办事项更新事件
            document.dispatchEvent(new CustomEvent('todosUpdated', {
                detail: { todos: this.todos }
            }));
            
        } catch (error) {
            console.error('刷新待办事项失败:', error);
        }
    }

    // 更新待办事项显示
    updateTodoDisplay() {
        const activityCards = document.querySelectorAll('.activity-card');
        if (activityCards.length < 2) return;
        
        const todoCard = activityCards[1];
        const todoList = todoCard.querySelector('.activity-list');
        if (!todoList) return;

        // 更新待办事项标题显示数量
        const todoHeader = todoCard.querySelector('.activity-title');
        if (todoHeader) {
            const count = this.todos.length;
            todoHeader.textContent = `待办事项${count > 0 ? ` (${count})` : ''}`;
        }

        if (this.todos.length === 0) {
            todoList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">📝</div>
                    <div class="empty-state-text">暂无待办事项</div>
                    <div class="empty-state-desc">所有任务都已完成</div>
                </div>
            `;
            return;
        }

        // 按优先级排序待办事项
        const sortedTodos = this.sortTodosByPriority(this.todos);

        todoList.innerHTML = sortedTodos.map(todo => {
            const priorityClass = this.getTodoPriorityClass(todo.priority);
            const actionText = todo.actionUrl ? '点击处理' : '';
            
            return `
                <div class="todo-item ${priorityClass}" data-id="${todo.id}" ${todo.actionUrl ? `onclick="window.todoManager.handleTodoAction('${todo.actionUrl}')"` : ''}>
                    <div class="todo-icon" style="background: ${todo.iconColor || '#3b82f6'}; color: white;">
                        ${todo.icon || '📋'}
                    </div>
                    <div class="todo-content">
                        <div class="todo-text">${todo.text}</div>
                        <div class="todo-meta">
                            <span class="todo-time">${this.formatTime(todo.time)}</span>
                            ${todo.priority === 'high' ? '<span class="todo-urgent">紧急</span>' : ''}
                            ${actionText ? `<span class="todo-action">${actionText}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 按优先级排序待办事项
    sortTodosByPriority(todos) {
        const priorityOrder = { 'high': 0, 'medium': 1, 'normal': 2, 'low': 3 };
        
        return todos.sort((a, b) => {
            // 首先按优先级排序
            const priorityDiff = (priorityOrder[a.priority] || 2) - (priorityOrder[b.priority] || 2);
            if (priorityDiff !== 0) return priorityDiff;
            
            // 优先级相同时按时间排序（最新的在前）
            return new Date(b.time) - new Date(a.time);
        });
    }

    // 获取待办事项优先级样式类
    getTodoPriorityClass(priority) {
        const priorityClasses = {
            'high': 'todo-high-priority',
            'medium': 'todo-medium-priority', 
            'normal': 'todo-normal-priority',
            'low': 'todo-low-priority'
        };
        return priorityClasses[priority] || priorityClasses.normal;
    }

    // 格式化时间显示
    formatTime(timestamp) {
        if (!timestamp) return '';

        const now = new Date();
        const time = new Date(timestamp);
        const diffMs = now - time;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMinutes < 1) {
            return '刚刚';
        } else if (diffMinutes < 60) {
            return `${diffMinutes}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            return time.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    // 处理待办事项操作
    handleTodoAction(actionUrl) {
        if (!actionUrl) return;

        try {
            // 根据actionUrl类型执行不同操作
            if (actionUrl.startsWith('section:')) {
                // 跳转到指定页面
                const sectionName = actionUrl.replace('section:', '');
                if (typeof showSection === 'function') {
                    showSection(sectionName);
                }
            } else if (actionUrl.startsWith('page:')) {
                // 跳转到指定页面（新增支持）
                const pageName = actionUrl.replace('page:', '');
                this.navigateToPage(pageName);
            } else if (actionUrl.startsWith('modal:')) {
                // 打开模态框
                const modalId = actionUrl.replace('modal:', '');
                this.openModal(modalId);
            } else if (actionUrl.startsWith('action:')) {
                // 执行特定操作
                const actionName = actionUrl.replace('action:', '');
                this.executeAction(actionName);
            } else {
                console.warn('未知的待办事项操作类型:', actionUrl);
            }
        } catch (error) {
            console.error('执行待办事项操作失败:', error);
        }
    }

    // 导航到指定页面
    navigateToPage(pageName) {
        // 页面映射表
        const pageMap = {
            'device-management': 'deviceInfo',
            'device-info': 'deviceInfo',
            'maintenance-records': 'maintenanceRecords',
            'device-health': 'deviceHealth',
            'device-export': 'deviceDataExport',
            'user-management': 'manageUsers',
            'system-settings': 'systemSettings',
            'pending-approval': 'pendingApproval',
            'approved-list': 'approved',
            'application-history': 'history',
            'new-application': 'new'
        };

        const sectionName = pageMap[pageName] || pageName;

        if (typeof showSection === 'function') {
            showSection(sectionName);
            console.log(`导航到页面: ${pageName} -> ${sectionName}`);
        } else {
            console.error('showSection函数不可用');
        }
    }

    // 打开模态框
    openModal(modalId) {
        const modal = document.getElementById(modalId);
        if (modal) {
            modal.classList.remove('hidden');
        }
    }

    // 执行特定操作
    executeAction(actionName) {
        const actions = {
            'refresh': () => this.refreshTodos(),
            'markAllRead': () => this.markAllTodosAsRead(),
            'clearCompleted': () => this.clearCompletedTodos()
        };

        if (actions[actionName]) {
            actions[actionName]();
        } else {
            console.warn('未知的待办事项操作:', actionName);
        }
    }

    // 标记所有待办事项为已读
    async markAllTodosAsRead() {
        try {
            const response = await apiRequest('/api/todos/mark-all-read', {
                method: 'POST',
                data: { username: this.currentUser }
            });

            if (response.success) {
                this.refreshTodos();
            }
        } catch (error) {
            console.error('标记待办事项为已读失败:', error);
        }
    }

    // 清除已完成的待办事项
    async clearCompletedTodos() {
        try {
            const response = await apiRequest('/api/todos/clear-completed', {
                method: 'POST',
                data: { username: this.currentUser }
            });

            if (response.success) {
                this.refreshTodos();
            }
        } catch (error) {
            console.error('清除已完成待办事项失败:', error);
        }
    }

    // 启动自动刷新
    startAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
        }

        this.refreshInterval = setInterval(() => {
            if (this.currentUser && this.currentRole) {
                this.refreshTodos();
            }
        }, this.autoRefreshIntervalMs);
    }

    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // 获取待办事项统计信息
    getTodoStats() {
        const stats = {
            total: this.todos.length,
            high: this.todos.filter(t => t.priority === 'high').length,
            medium: this.todos.filter(t => t.priority === 'medium').length,
            normal: this.todos.filter(t => t.priority === 'normal').length,
            low: this.todos.filter(t => t.priority === 'low').length
        };

        return stats;
    }

    // 销毁管理器
    destroy() {
        this.stopAutoRefresh();
        this.todos = [];
        this.currentUser = null;
        this.currentRole = null;
    }
}

// 延迟创建全局待办事项管理器实例，等待用户登录
window.initTodoManager = function() {
    if (!window.todoManager) {
        window.todoManager = new TodoManager();
    }
    return window.todoManager;
};

// 导出待办事项管理器类
if (typeof module !== 'undefined' && module.exports) {
    module.exports = TodoManager;
}
