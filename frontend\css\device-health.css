/* 设备健康度评估样式 */

.device-health-container {
    max-width: 100%;
    margin: 0 auto;
}

.device-health-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #e5e7eb;
}

.device-health-title {
    font-size: 1.875rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.device-health-actions {
    display: flex;
    gap: 0.75rem;
}

/* 健康度概览 */
.health-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 2rem;
}

.summary-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
}

.summary-card.excellent {
    border-color: #10b981;
    background: linear-gradient(135deg, #ecfdf5 0%, #d1fae5 100%);
}

.summary-card.good {
    border-color: #3b82f6;
    background: linear-gradient(135deg, #eff6ff 0%, #dbeafe 100%);
}

.summary-card.fair {
    border-color: #f59e0b;
    background: linear-gradient(135deg, #fffbeb 0%, #fef3c7 100%);
}

.summary-card.poor {
    border-color: #f97316;
    background: linear-gradient(135deg, #fff7ed 0%, #fed7aa 100%);
}

.summary-card.critical {
    border-color: #ef4444;
    background: linear-gradient(135deg, #fef2f2 0%, #fecaca 100%);
}

.card-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.card-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

/* 设备列表 */
.health-devices-list {
    background: #f9fafb;
    border-radius: 0.75rem;
    padding: 1.5rem;
}

.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
}

.list-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}



.devices-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
    gap: 1rem;
}

.health-device-item {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    transition: all 0.3s ease;
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.health-device-item:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
    border-color: #3b82f6;
}

.device-basic-info {
    flex: 1;
}

.device-name {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.device-code {
    font-size: 0.875rem;
    color: #6b7280;
    margin-bottom: 0.25rem;
}

.device-location {
    font-size: 0.875rem;
    color: #9ca3af;
}

.health-score-display {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
}

.score-circle {
    width: 60px;
    height: 60px;
    border-radius: 50%;
    border: 4px solid;
    display: flex;
    align-items: center;
    justify-content: center;
    background: white;
    flex-shrink: 0;
}

.score-number {
    font-size: 1.25rem;
    font-weight: 700;
    color: #1f2937;
}

.health-level {
    font-size: 1rem;
    font-weight: 600;
}

.device-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

.action-btn {
    padding: 0.5rem 1rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    background: white;
    color: #374151;
    font-size: 0.875rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.2s ease;
    flex: 1;
    min-width: 80px;
}

.action-btn:hover {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.action-btn:active {
    transform: translateY(1px);
}

/* 模态框样式 */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
    padding: 1rem;
    cursor: pointer; /* 背景区域显示可点击 */
}

.modal-content {
    background: white;
    border-radius: 0.75rem;
    max-width: 800px;
    width: 100%;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1);
    cursor: default; /* 内容区域恢复默认光标 */
    position: relative;
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.modal-header h3 {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

/* 设备健康度模态框关闭按钮样式 - 使用更高特异性 */
.modal-overlay .modal-content .modal-close {
    background: none !important;
    border: none;
    font-size: 1.5rem;
    color: #6b7280;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.25rem;
    transition: all 0.2s ease;
    min-width: 2rem;
    min-height: 2rem;
    display: flex;
    align-items: center;
    justify-content: center;
    line-height: 1;
}

.modal-overlay .modal-content .modal-close:hover {
    background: none !important;
    color: #374151;
    transform: scale(1.1);
}

.modal-overlay .modal-content .modal-close:active {
    background: none !important;
    transform: scale(0.95);
}

/* 防止body滚动 */
body.modal-open {
    overflow: hidden;
}



.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
    padding: 1.5rem;
    border-top: 1px solid #e5e7eb;
}

/* 健康度详情模态框 */
.health-detail-modal {
    max-width: 900px;
}

.health-overview {
    display: grid;
    grid-template-columns: auto 1fr;
    gap: 2rem;
    margin-bottom: 2rem;
    padding: 1.5rem;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 0.75rem;
    color: white;
}

.health-score-large {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
}

.score-circle-large {
    width: 100px;
    height: 100px;
    border-radius: 50%;
    border: 6px solid white;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    position: relative;
}







.score-number-large {
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.health-level-large {
    font-size: 1.25rem;
    font-weight: 600;
    text-align: center;
}

.health-details {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.75rem;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 0.5rem;
    backdrop-filter: blur(10px);
}

.detail-label {
    font-size: 0.875rem;
    opacity: 0.9;
}

.detail-value {
    font-weight: 600;
}

/* 总得分计算部分样式 */
.calculation-section {
    grid-column: 1 / -1; /* 占据整行 */
    margin: 1rem 0;
    padding: 1rem;
    background: rgba(255, 255, 255, 0.15);
    border-radius: 0.5rem;
    border: 1px solid rgba(255, 255, 255, 0.2);
}

.calculation-header {
    margin-bottom: 0.75rem;
}

.calculation-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: rgba(255, 255, 255, 0.9);
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.calculation-title::before {
    content: "🧮";
    font-size: 1rem;
}

.calculation-formula {
    font-size: 0.8rem;
    line-height: 1.4;
}

.formula-text {
    color: rgba(255, 255, 255, 0.8);
    margin-bottom: 0.5rem;
    font-family: 'Courier New', monospace;
    background: rgba(0, 0, 0, 0.1);
    padding: 0.5rem;
    border-radius: 0.25rem;
    border-left: 3px solid rgba(255, 255, 255, 0.3);
}

.formula-result {
    color: rgba(255, 255, 255, 0.9);
    font-family: 'Courier New', monospace;
    background: rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    border-radius: 0.25rem;
    border-left: 3px solid #10b981;
}

.formula-result strong {
    color: #10b981;
    font-weight: 700;
    font-size: 0.9rem;
}

/* 预测和建议部分 */
.prediction-section,
.recommendations-section {
    margin-bottom: 2rem;
}

.prediction-section h4,
.recommendations-section h4 {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}

.prediction-content {
    background: #f8fafc;
    border-radius: 0.5rem;
    padding: 1rem;
}

.prediction-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0.5rem 0;
    border-bottom: 1px solid #e5e7eb;
}

.prediction-item:last-child {
    border-bottom: none;
}

.prediction-label {
    font-size: 0.875rem;
    color: #6b7280;
}

.prediction-value {
    font-weight: 600;
    color: #1f2937;
}

.no-prediction {
    text-align: center;
    color: #6b7280;
    font-style: italic;
    padding: 2rem;
}

.recommendations-list {
    display: flex;
    flex-direction: column;
    gap: 0.75rem;
}

.recommendation-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 0.5rem;
    border-left: 4px solid;
}

.recommendation-item.high {
    background: #fef2f2;
    border-left-color: #ef4444;
}

.recommendation-item.medium {
    background: #fffbeb;
    border-left-color: #f59e0b;
}

.recommendation-item.low {
    background: #f0f9ff;
    border-left-color: #3b82f6;
}

.rec-priority {
    padding: 0.25rem 0.5rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    flex-shrink: 0;
}

.recommendation-item.high .rec-priority {
    background: #ef4444;
    color: white;
}

.recommendation-item.medium .rec-priority {
    background: #f59e0b;
    color: white;
}

.recommendation-item.low .rec-priority {
    background: #3b82f6;
    color: white;
}

.rec-message {
    flex: 1;
    color: #374151;
}



/* 加载状态 */
.loading-placeholder {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    padding: 4rem 2rem;
    color: #6b7280;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #e5e7eb;
    border-top: 4px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 1rem;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 厂区分组样式 */
.factory-groups {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.factory-group {
    background: white;
    border-radius: 0.75rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
    overflow: hidden;
}

.factory-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 1.5rem;
    background: #f9fafb;
    border-bottom: 1px solid #e5e7eb;
}

.factory-toggle-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    transition: color 0.2s ease;
}

.factory-toggle-btn:hover {
    color: #3b82f6;
}

.toggle-icon {
    width: 1.25rem;
    height: 1.25rem;
    transition: transform 0.2s ease;
}

.toggle-icon.expanded {
    transform: rotate(0deg);
}

.toggle-icon.collapsed {
    transform: rotate(-90deg);
}

.factory-name {
    font-weight: 600;
    color: #1f2937;
}

.device-count {
    font-size: 0.875rem;
    color: #6b7280;
    font-weight: normal;
}

.factory-stats {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.factory-stats .stat {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 1.5rem;
    height: 1.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 600;
    color: white;
}

.factory-stats .stat.excellent {
    background-color: #10b981;
}

.factory-stats .stat.good {
    background-color: #3b82f6;
}

.factory-stats .stat.fair {
    background-color: #f59e0b;
}

.factory-stats .stat.poor {
    background-color: #ef4444;
}

.factory-stats .stat.critical {
    background-color: #dc2626;
}

.factory-devices {
    transition: all 0.3s ease;
    overflow: hidden;
}

.factory-devices.expanded {
    max-height: 2000px; /* 足够大的高度来容纳设备列表 */
    opacity: 1;
    padding: 1.5rem;
}

.factory-devices.collapsed {
    max-height: 0;
    opacity: 0;
    padding: 0 1.5rem;
}

.no-factories {
    text-align: center;
    padding: 3rem;
    color: #6b7280;
    font-size: 1.125rem;
}

/* 按钮样式优化 */
.list-actions {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.action-btn {
    display: flex;
    align-items: center;
    gap: 0.375rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: 1px solid #d1d5db;
    background: white;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
}

.action-btn:hover {
    background: #f9fafb;
    border-color: #9ca3af;
    color: #1f2937;
}

.action-btn svg {
    width: 1rem;
    height: 1rem;
}

/* ==================== 维护计划模态框样式 ==================== */

/* 维护计划模态框 */
.maintenance-plan-modal {
    max-width: 800px;
}

/* 计划概览区域 */
.plan-overview {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
    gap: 1rem;
    margin-bottom: 1.5rem;
    padding: 1rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
}

.plan-item {
    text-align: center;
}

.plan-label {
    display: block;
    font-size: 0.875rem;
    color: #64748b;
    margin-bottom: 0.25rem;
}

.plan-value {
    font-size: 1.25rem;
    font-weight: 600;
    color: #1e293b;
}

/* 计划分组区域 */
.plan-sections {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

/* 计划分组样式 */
.plan-section {
    border-radius: 0.5rem;
    border: 1px solid #e2e8f0;
    overflow: hidden;
}

.plan-section h4 {
    margin: 0;
    padding: 0.75rem 1rem;
    font-size: 1rem;
    font-weight: 600;
    background: #f1f5f9;
    color: #334155;
    border-bottom: 1px solid #e2e8f0;
}

/* 立即执行 */
.plan-section.immediate h4 {
    background: #fef2f2;
    color: #dc2626;
    border-bottom-color: #fecaca;
}

/* 短期计划 */
.plan-section.short-term h4 {
    background: #fffbeb;
    color: #d97706;
    border-bottom-color: #fed7aa;
}

/* 长期计划 */
.plan-section.long-term h4 {
    background: #eff6ff;
    color: #2563eb;
    border-bottom-color: #bfdbfe;
}

/* 计划动作列表 */
.plan-actions {
    padding: 1rem;
    background: white;
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

/* 单个计划动作 */
.plan-action {
    padding: 0.75rem;
    border-radius: 0.375rem;
    border-left: 3px solid;
    background: #fafafa;
}

/* 优先级样式 */
.plan-action.high {
    border-left-color: #dc2626;
    background: #fef2f2;
}

.plan-action.medium {
    border-left-color: #d97706;
    background: #fffbeb;
}

.plan-action.low {
    border-left-color: #2563eb;
    background: #eff6ff;
}

.action-title {
    font-size: 0.9rem;
    font-weight: 600;
    color: #1e293b;
    margin-bottom: 0.25rem;
}

.action-reason {
    font-size: 0.8rem;
    color: #64748b;
    line-height: 1.4;
}

/* 无计划状态 */
.no-actions {
    text-align: center;
    padding: 1.5rem;
    color: #64748b;
    font-style: italic;
    background: #f8fafc;
    border-radius: 0.375rem;
}

/* ==================== 维护计划响应式设计 ==================== */

/* 移动设备 */
@media (max-width: 768px) {
    .maintenance-plan-modal {
        max-width: 95vw;
        margin: 0.5rem;
    }

    .plan-overview {
        grid-template-columns: 1fr;
        gap: 0.75rem;
    }

    .plan-item {
        display: flex;
        justify-content: space-between;
        align-items: center;
        text-align: left;
    }

    .plan-section h4 {
        padding: 0.5rem 0.75rem;
        font-size: 0.9rem;
    }

    .plan-actions {
        padding: 0.75rem;
    }

    .plan-action {
        padding: 0.5rem;
    }

    .action-title {
        font-size: 0.85rem;
    }

    .action-reason {
        font-size: 0.75rem;
    }
}

/* 响应式设计 */
@media (max-width: 768px) {
    .device-health-header {
        flex-direction: column;
        gap: 1rem;
        align-items: stretch;
    }

    .device-health-actions {
        justify-content: center;
    }

    .summary-cards {
        grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
    }

    .devices-grid {
        grid-template-columns: 1fr;
    }

    .health-overview {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .health-details {
        grid-template-columns: 1fr;
    }

    .modal-content {
        margin: 1rem;
        max-height: calc(100vh - 2rem);
    }

    .device-actions {
        flex-direction: column;
    }

    .action-btn {
        flex: none;
    }

    .factory-header {
        flex-direction: column;
        gap: 0.75rem;
        align-items: stretch;
    }

    .factory-toggle-btn {
        justify-content: flex-start;
    }

    .factory-stats {
        justify-content: center;
    }



    .calculation-title {
        font-size: 0.9rem;
    }

    .formula-content {
        font-size: 0.75rem;
    }

    .calc-component,
    .calc-value {
        font-size: 0.75rem;
    }

    /* 移动端分页优化 */
    .factory-pagination {
        flex-direction: column;
        gap: 0.5rem;
        text-align: center;
    }

    .pagination-controls {
        justify-content: center;
    }
}

/* 厂区分页样式 */
.factory-pagination {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-top: 1rem;
    padding: 0.75rem;
    background: #f8fafc;
    border-radius: 0.5rem;
    border: 1px solid #e5e7eb;
}

.pagination-info {
    font-size: 0.875rem;
    color: #6b7280;
}

.pagination-info .font-medium {
    font-weight: 600;
    color: #374151;
}

.pagination-controls {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.factory-page-numbers {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    background: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    color: #374151;
    cursor: pointer;
    transition: all 0.2s ease;
    min-width: 60px;
}

.pagination-btn:hover:not(:disabled) {
    background: #f3f4f6;
    border-color: #9ca3af;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    background: #f9fafb;
}

.pagination-current {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    background: #eff6ff;
    border: 1px solid #bfdbfe;
    border-radius: 0.375rem;
    color: #1d4ed8;
    font-weight: 500;
    min-width: 60px;
    text-align: center;
}
