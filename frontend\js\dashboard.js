// Dashboard首页管理模块
class DashboardManager {
    constructor() {
        this.refreshInterval = null;
        this.charts = {};
        this.isInitialized = false;
        this.currentUser = null;
        this.currentRole = null;
    }

    // 初始化Dashboard
    async init(username, role) {
        if (this.isInitialized) {
            return;
        }

        this.currentUser = username;
        this.currentRole = role;

        // 初始化待办事项管理器
        if (typeof window.initTodoManager === 'function') {
            window.initTodoManager();
        }

        try {
            await this.loadDashboardData();
            this.setupEventListeners();
            this.startAutoRefresh();
            this.isInitialized = true;
            console.log('Dashboard初始化完成');
        } catch (error) {
            console.error('Dashboard初始化失败:', error);
            this.showError('Dashboard加载失败，请刷新页面重试');
        }
    }

    // 设置事件监听器
    setupEventListeners() {
        // 快速操作按钮点击事件
        document.addEventListener('click', (e) => {
            if (e.target.closest('.quick-action-btn')) {
                const action = e.target.closest('.quick-action-btn').dataset.action;
                this.handleQuickAction(action);
            }

            // 活动刷新按钮点击事件
            if (e.target.closest('.activity-refresh-btn')) {
                this.refreshActivityFeed();
            }
        });

        // 窗口大小变化时重新调整图表
        window.addEventListener('resize', () => {
            this.debounce(() => {
                this.resizeCharts();
            }, 300)();
        });
    }

    // 加载Dashboard数据
    async loadDashboardData() {
        try {
            this.showLoadingState();

            // 并行加载核心数据
            const [overviewData, chartData, activityData] = await Promise.all([
                this.loadOverviewData(),
                this.loadChartData(),
                this.loadActivityData()
            ]);

            // 更新核心区域
            this.updateWelcomeSection();
            this.updateOverviewCards(overviewData);
            this.updateQuickActions();
            this.updateCharts(chartData);
            this.updateActivityFeed(activityData);

            // 单独加载待办事项数据（不影响主要功能）
            this.loadTodoData().then(todoData => {
                this.updateTodoList(todoData);
            }).catch(error => {
                console.warn('待办事项数据加载失败:', error);
                this.updateTodoList({ todos: [] });
            });

            this.hideLoadingState();
        } catch (error) {
            console.error('加载Dashboard数据失败:', error);
            this.hideLoadingState();
            this.showError('数据加载失败');
        }
    }

    // 加载概览数据
    async loadOverviewData() {
        try {
            const response = await apiRequest(`/api/dashboard/overview?username=${this.currentUser}&role=${this.currentRole}`);
            return response.success ? response.data : {};
        } catch (error) {
            console.error('加载概览数据失败:', error);
            return {};
        }
    }

    // 加载图表数据
    async loadChartData() {
        try {
            const response = await apiRequest(`/api/dashboard/charts?username=${this.currentUser}&role=${this.currentRole}`);
            return response.success ? response.data : {};
        } catch (error) {
            console.error('加载图表数据失败:', error);
            return {};
        }
    }

    // 加载活动数据
    async loadActivityData() {
        try {
            const response = await apiRequest(`/api/dashboard/activity?username=${this.currentUser}&role=${this.currentRole}`);
            return response.success ? response.data : {};
        } catch (error) {
            console.error('加载活动数据失败:', error);
            return {};
        }
    }

    // 加载待办事项数据
    async loadTodoData() {
        try {
            const response = await apiRequest(`/api/dashboard/todos?username=${this.currentUser}&role=${this.currentRole}`);
            return response.success ? response.data : { todos: [] };
        } catch (error) {
            console.error('加载待办事项数据失败:', error);
            return { todos: [] };
        }
    }

    // 更新欢迎区域
    updateWelcomeSection() {
        const now = new Date();
        const hour = now.getHours();
        let greeting = '早上好';

        if (hour >= 12 && hour < 18) {
            greeting = '下午好';
        } else if (hour >= 18) {
            greeting = '晚上好';
        }

        const welcomeText = document.querySelector('.welcome-text h1');
        const welcomeDesc = document.querySelector('.welcome-text p');
        const currentTime = document.querySelector('.current-time');
        const userRole = document.querySelector('.user-role');

        if (welcomeText) {
            welcomeText.textContent = `${greeting}，${this.currentUser}`;
        }

        // 根据用户角色显示不同的欢迎描述
        if (welcomeDesc) {
            const roleDescriptions = {
                'admin': '您拥有系统管理员权限，可以管理用户、设备、申请数据和系统配置',
                'readonly': '您拥有只读权限，可以查看所有申请和设备数据',
                'director': '您拥有厂长权限，可以审批申请并查看相关统计数据',
                'chief': '您拥有总监权限，可以审批申请并查看相关统计数据',
                'manager': '您拥有经理权限，可以审批申请并查看相关统计数据',
                'ceo': '您拥有CEO权限，可以进行审批并查看全局数据',
                'user': '您可以提交申请并查看自己的申请记录'
            };
            welcomeDesc.textContent = roleDescriptions[this.currentRole] || '今天是美好的一天，让我们开始工作吧！';
        }

        if (currentTime) {
            currentTime.textContent = now.toLocaleString('zh-CN', {
                year: 'numeric',
                month: 'long',
                day: 'numeric',
                weekday: 'long',
                hour: '2-digit',
                minute: '2-digit'
            });
        }

        if (userRole) {
            const roleNames = {
                'admin': '系统管理员',
                'user': '普通用户',
                'director': '厂长',
                'chief': '总监',
                'manager': '经理',
                'ceo': 'CEO',
                'readonly': '只读用户'
            };
            userRole.textContent = roleNames[this.currentRole] || this.currentRole;
        }
    }

    // 更新概览卡片
    updateOverviewCards(data) {
        // 根据用户角色定制卡片标题
        const getCardTitle = (cardType) => {
            const titles = {
                applications: {
                    'admin': '申请总数',
                    'readonly': '申请总数',
                    'director': '申请总数',
                    'chief': '申请总数',
                    'manager': '申请总数',
                    'ceo': '申请总数',
                    'user': '我的申请'
                },
                devices: {
                    'admin': '设备总数',
                    'readonly': '设备总数',
                    'director': '设备总数',
                    'chief': '设备总数',
                    'manager': '设备总数',
                    'ceo': '设备总数',
                    'user': '设备总数'
                },
                efficiency: {
                    'director': '我的审批效率',
                    'chief': '我的审批效率',
                    'manager': '我的审批效率',
                    'ceo': '我的审批效率',
                    'admin': '系统审批效率'
                }
            };
            return titles[cardType][this.currentRole] || '';
        };

        const cards = {
            applications: {
                selector: '.application-card',
                value: data.totalApplications || 0,
                trend: data.applicationTrend || 0,
                visible: true, // 所有用户都能看到申请数据
                title: getCardTitle('applications')
            },
            devices: {
                selector: '.device-card',
                value: data.totalDevices || 0,
                trend: data.deviceTrend || 0,
                visible: true, // 所有用户都能看到设备数据
                title: getCardTitle('devices')
            },
            efficiency: {
                selector: '.efficiency-card',
                value: data.approvalEfficiency || 0,
                trend: data.efficiencyTrend || 0,
                suffix: '%',
                visible: ['director', 'chief', 'manager', 'ceo', 'admin'].includes(this.currentRole), // 审批人员和管理员能看到效率数据
                title: getCardTitle('efficiency')
            }
        };

        Object.entries(cards).forEach(([, config]) => {
            const card = document.querySelector(config.selector);
            if (card) {
                // 根据权限显示或隐藏卡片
                if (config.visible) {
                    card.style.display = '';

                    // 更新卡片标题
                    const titleElement = card.querySelector('.card-title');
                    if (titleElement && config.title) {
                        titleElement.textContent = config.title;
                    }

                    const valueElement = card.querySelector('.card-value');
                    const trendElement = card.querySelector('.card-trend');

                    if (valueElement) {
                        valueElement.textContent = config.value + (config.suffix || '');
                    }

                    if (trendElement && config.trend !== undefined) {
                        const trendIcon = config.trend > 0 ? '↗' : config.trend < 0 ? '↘' : '→';
                        const trendClass = config.trend > 0 ? 'trend-up' : config.trend < 0 ? 'trend-down' : 'trend-neutral';
                        const trendText = config.trend > 0 ? `+${config.trend}%` : `${config.trend}%`;

                        trendElement.innerHTML = `<span>${trendIcon}</span> <span>${trendText}</span>`;
                        trendElement.className = `card-trend ${trendClass}`;
                    }
                } else {
                    // 隐藏无权限访问的卡片
                    card.style.display = 'none';
                }
            }
        });
    }

    // 更新快速操作
    updateQuickActions() {
        const actionsContainer = document.querySelector('.quick-actions-grid');
        if (!actionsContainer) return;

        const actions = this.getQuickActionsForRole();

        actionsContainer.innerHTML = actions.map(action => `
            <div class="quick-action-btn" data-action="${action.action}">
                <span class="quick-action-icon">${action.icon}</span>
                <div class="quick-action-title">${action.title}</div>
                <div class="quick-action-desc">${action.description}</div>
            </div>
        `).join('');
    }

    // 根据角色获取快速操作
    getQuickActionsForRole() {
        // 根据角色添加不同的操作
        if (this.currentRole === 'admin') {
            // 管理员：拥有所有权限
            return [
                {
                    action: 'new-application',
                    icon: '📝',
                    title: '新建申请',
                    description: '提交新的申请'
                },
                {
                    action: 'view-history',
                    icon: '📋',
                    title: '申请记录',
                    description: '查看申请历史'
                },
                {
                    action: 'device-management',
                    icon: '⚙️',
                    title: '设备管理',
                    description: '管理设备信息'
                },
                {
                    action: 'manage-users',
                    icon: '👥',
                    title: '用户管理',
                    description: '管理系统用户'
                },
                {
                    action: 'system-settings',
                    icon: '🔧',
                    title: 'E-mail系统设置',
                    description: '配置E-mail系统参数'
                },
                {
                    action: 'data-export',
                    icon: '📊',
                    title: '数据导出',
                    description: '导出系统数据'
                }
            ];
        } else if (this.currentRole === 'director') {
            // 厂长：审批功能 + 申请功能 + 设备查看
            return [
                {
                    action: 'new-application',
                    icon: '📝',
                    title: '新建申请',
                    description: '提交新的申请'
                },
                {
                    action: 'view-history',
                    icon: '📋',
                    title: '申请记录',
                    description: '查看申请历史'
                },
                {
                    action: 'pending-approval',
                    icon: '⏳',
                    title: '待审核',
                    description: '处理待审核申请'
                },
                {
                    action: 'approved-list',
                    icon: '✅',
                    title: '已审核',
                    description: '查看已审核申请'
                },
                {
                    action: 'device-management',
                    icon: '⚙️',
                    title: '设备查看',
                    description: '查看设备信息'
                },
                {
                    action: 'data-export',
                    icon: '📊',
                    title: '数据导出',
                    description: '导出数据报表'
                }
            ];
        } else if (['chief', 'manager', 'ceo'].includes(this.currentRole)) {
            // 其他审批人员：只有审批功能 + 设备查看
            return [
                {
                    action: 'pending-approval',
                    icon: '⏳',
                    title: '待审核',
                    description: '处理待审核申请'
                },
                {
                    action: 'approved-list',
                    icon: '✅',
                    title: '已审核',
                    description: '查看已审核申请'
                },
                {
                    action: 'device-management',
                    icon: '⚙️',
                    title: '设备查看',
                    description: '查看设备信息'
                },
                {
                    action: 'data-export',
                    icon: '📊',
                    title: '数据导出',
                    description: '导出数据报表'
                }
            ];
        } else if (this.currentRole === 'readonly') {
            // 只读用户：查看所有数据
            return [
                {
                    action: 'view-history',
                    icon: '📋',
                    title: '申请记录',
                    description: '查看所有申请'
                },
                {
                    action: 'device-management',
                    icon: '⚙️',
                    title: '设备查看',
                    description: '查看设备信息'
                },
                {
                    action: 'data-export',
                    icon: '📊',
                    title: '数据导出',
                    description: '导出数据报表'
                }
            ];
        } else {
            // 普通用户：基本申请功能 + 设备查看
            return [
                {
                    action: 'new-application',
                    icon: '📝',
                    title: '新建申请',
                    description: '提交新的申请'
                },
                {
                    action: 'view-history',
                    icon: '📋',
                    title: '我的申请',
                    description: '查看申请记录'
                },
                {
                    action: 'device-management',
                    icon: '⚙️',
                    title: '设备查看',
                    description: '查看设备信息'
                },
                {
                    action: 'data-export',
                    icon: '📊',
                    title: '数据导出',
                    description: '导出数据报表'
                }
            ];
        }
    }

    // 处理快速操作
    handleQuickAction(action) {
        const actionMap = {
            'new-application': () => showSection('new'),
            'view-history': () => showSection('history'),
            'pending-approval': () => showSection('pendingApproval'),
            'approved-list': () => showSection('approved'),
            'manage-users': () => showSection('manageUsers'),
            'device-management': () => showSection('deviceInfo'),
            'system-settings': () => showSection('systemSettings'),
            'data-export': () => showSection('deviceDataExport')
        };

        if (actionMap[action]) {
            actionMap[action]();
        }
    }

    // 检查Chart.js是否可用
    isChartJsAvailable() {
        return typeof Chart !== 'undefined' && Chart.Chart !== undefined;
    }

    // 显示图表错误信息
    showChartError(message, canvasId) {
        const canvas = document.getElementById(canvasId);
        if (!canvas) return;

        const container = canvas.parentElement;
        if (!container) return;

        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'chart-error-message';
        errorDiv.style.cssText = `
            display: flex;
            align-items: center;
            justify-content: center;
            height: 200px;
            background-color: #f8f9fa;
            border: 2px dashed #dee2e6;
            border-radius: 8px;
            color: #6c757d;
            font-size: 14px;
            text-align: center;
            flex-direction: column;
        `;

        errorDiv.innerHTML = `
            <i class="fas fa-chart-line" style="font-size: 24px; margin-bottom: 8px; opacity: 0.5;"></i>
            <div>${message}</div>
            <small style="margin-top: 4px; opacity: 0.7;">请检查网络连接或联系管理员</small>
        `;

        // 隐藏canvas，显示错误信息
        canvas.style.display = 'none';
        container.appendChild(errorDiv);
    }

    // 更新图表
    updateCharts(data) {
        try {
            console.log('开始更新图表，数据:', data);

            // 检查Chart.js是否可用
            if (!this.isChartJsAvailable()) {
                console.error('Chart.js库未加载，无法显示图表');
                this.showChartError('图表库加载失败', 'applicationTrendChart');
                this.showChartError('图表库加载失败', 'deviceHealthChart');
                return;
            }

            // 延迟一下确保DOM元素已经可见
            setTimeout(() => {
                // 所有用户都能看到申请趋势图表
                this.updateApplicationTrendChart(data.applicationTrend || []);

                // 所有用户都能看到设备健康度图表
                this.updateDeviceHealthChart(data.deviceHealth || {});

                console.log('图表更新完成');
            }, 100);
        } catch (error) {
            console.error('更新图表失败:', error);
            this.showError('图表更新失败: ' + error.message);
        }
    }

    // 更新申请趋势图表
    updateApplicationTrendChart(trendData) {
        const canvas = document.getElementById('applicationTrendChart');
        if (!canvas) {
            console.warn('申请趋势图表canvas元素未找到');
            return;
        }

        // 检查Chart.js是否可用
        if (!this.isChartJsAvailable()) {
            console.error('Chart.js库未加载，无法创建申请趋势图表');
            this.showChartError('图表库未加载', 'applicationTrendChart');
            return;
        }

        // 确保canvas有正确的尺寸
        this.setupCanvasSize(canvas);

        // 销毁现有图表
        if (this.charts.applicationTrend) {
            this.charts.applicationTrend.destroy();
        }

        // 如果没有数据，显示空状态
        if (!trendData || trendData.length === 0) {
            this.showChartEmptyState(canvas, '暂无申请趋势数据');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 准备数据
        const labels = trendData.map(item => {
            // 使用后端提供的月份标签，如果没有则从日期解析
            return item.month || item.date;
        });

        const counts = trendData.map(item => item.count);

        this.charts.applicationTrend = new Chart(ctx, {
            type: 'line',
            data: {
                labels: labels,
                datasets: [{
                    label: '申请数量',
                    data: counts,
                    borderColor: '#3b82f6',
                    backgroundColor: 'rgba(59, 130, 246, 0.1)',
                    borderWidth: 2,
                    fill: true,
                    tension: 0.4,
                    pointBackgroundColor: '#3b82f6',
                    pointBorderColor: '#ffffff',
                    pointBorderWidth: 2,
                    pointRadius: 4,
                    pointHoverRadius: 6
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#3b82f6',
                        borderWidth: 1,
                        cornerRadius: 8,
                        displayColors: false,
                        callbacks: {
                            title: function(context) {
                                const dataIndex = context[0].dataIndex;
                                // 使用后端提供的月份标签，如果没有则解析日期
                                return trendData[dataIndex].month || trendData[dataIndex].date;
                            },
                            label: function(context) {
                                return `申请数量: ${context.parsed.y}`;
                            }
                        }
                    }
                },
                scales: {
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12
                            }
                        }
                    },
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(107, 114, 128, 0.1)'
                        },
                        ticks: {
                            color: '#6b7280',
                            font: {
                                size: 12
                            },
                            stepSize: 1
                        }
                    }
                },
                interaction: {
                    intersect: false,
                    mode: 'index'
                }
            }
        });
    }

    // 更新设备健康度分布图表
    updateDeviceHealthChart(healthData) {
        const canvas = document.getElementById('deviceHealthChart');
        if (!canvas) {
            console.warn('设备健康度图表canvas元素未找到');
            return;
        }

        // 检查Chart.js是否可用
        if (!this.isChartJsAvailable()) {
            console.error('Chart.js库未加载，无法创建设备健康度图表');
            this.showChartError('图表库未加载', 'deviceHealthChart');
            return;
        }

        // 确保canvas有正确的尺寸
        this.setupCanvasSize(canvas);

        // 销毁现有图表
        if (this.charts.deviceHealth) {
            this.charts.deviceHealth.destroy();
        }

        // 检查是否有数据
        const totalDevices = (healthData.excellent || 0) + (healthData.good || 0) +
                           (healthData.fair || 0) + (healthData.poor || 0) + (healthData.critical || 0);

        if (totalDevices === 0) {
            this.showChartEmptyState(canvas, '暂无设备健康度数据');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 准备数据
        const data = [
            healthData.excellent || 0,
            healthData.good || 0,
            healthData.fair || 0,
            healthData.poor || 0,
            healthData.critical || 0
        ];

        const labels = ['优秀', '良好', '一般', '较差', '危险'];
        const colors = ['#10b981', '#3b82f6', '#f59e0b', '#f97316', '#ef4444'];

        this.charts.deviceHealth = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: labels,
                datasets: [{
                    data: data,
                    backgroundColor: colors,
                    borderColor: '#ffffff',
                    borderWidth: 2,
                    hoverBorderWidth: 3
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true,
                            font: {
                                size: 12
                            },
                            color: '#374151'
                        }
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#ffffff',
                        bodyColor: '#ffffff',
                        borderColor: '#3b82f6',
                        borderWidth: 1,
                        cornerRadius: 8,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? ((context.parsed / total) * 100).toFixed(1) : 0;
                                return `${context.label}: ${context.parsed} (${percentage}%)`;
                            }
                        }
                    }
                },
                cutout: '60%',
                animation: {
                    animateRotate: true,
                    duration: 1000
                }
            }
        });
    }

    // 隐藏设备健康度图表
    hideDeviceHealthChart() {
        const chartCard = document.querySelector('.chart-card:has(#deviceHealthChart)');
        if (chartCard) {
            chartCard.style.display = 'none';
        } else {
            // 如果CSS :has() 不支持，使用备用方法
            const canvas = document.getElementById('deviceHealthChart');
            if (canvas) {
                const chartCard = canvas.closest('.chart-card');
                if (chartCard) {
                    chartCard.style.display = 'none';
                }
            }
        }
    }

    // 设置Canvas尺寸
    setupCanvasSize(canvas) {
        const container = canvas.parentElement;
        if (container) {
            const rect = container.getBoundingClientRect();
            const dpr = window.devicePixelRatio || 1;

            // 设置canvas的实际尺寸
            canvas.width = rect.width * dpr;
            canvas.height = rect.height * dpr;

            // 设置canvas的显示尺寸
            canvas.style.width = rect.width + 'px';
            canvas.style.height = rect.height + 'px';

            // 缩放绘图上下文以匹配设备像素比
            const ctx = canvas.getContext('2d');
            ctx.scale(dpr, dpr);
        }
    }

    // 显示图表空状态
    showChartEmptyState(canvas, message) {
        const ctx = canvas.getContext('2d');
        const rect = canvas.getBoundingClientRect();
        const width = rect.width;
        const height = rect.height;

        // 清空画布
        ctx.clearRect(0, 0, width, height);

        // 设置样式
        ctx.fillStyle = '#9ca3af';
        ctx.textAlign = 'center';
        ctx.textBaseline = 'middle';
        ctx.font = '14px Arial, sans-serif';

        // 绘制空状态图标
        ctx.font = '32px Arial, sans-serif';
        ctx.fillText('📊', width / 2, height / 2 - 20);

        // 绘制提示文字
        ctx.font = '14px Arial, sans-serif';
        ctx.fillText(message, width / 2, height / 2 + 20);
    }

    // 更新活动动态
    updateActivityFeed(data) {
        const activityList = document.querySelector('.activity-list');
        if (!activityList || !data.activities) return;

        if (data.activities.length === 0) {
            activityList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">📭</div>
                    <div class="empty-state-text">暂无最新动态</div>
                    <div class="empty-state-desc">当有新的活动时会在这里显示</div>
                </div>
            `;
            return;
        }

        activityList.innerHTML = data.activities.map(activity => {
            const activityTypeClass = this.getActivityTypeClass(activity.type);
            return `
                <div class="activity-item ${activityTypeClass}" data-type="${activity.type || 'default'}">
                    <div class="activity-icon" style="background: ${activity.iconColor || '#e5e7eb'}; color: white;">
                        ${activity.icon || '📄'}
                    </div>
                    <div class="activity-content">
                        <div class="activity-text">${activity.text}</div>
                        <div class="activity-time">${this.formatActivityTime(activity.time)}</div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 更新待办事项列表
    updateTodoList(data) {
        // 查找待办事项区域（第二个activity-card）
        const activityCards = document.querySelectorAll('.activity-card');
        if (activityCards.length < 2) return;

        const todoCard = activityCards[1];
        const todoList = todoCard.querySelector('.activity-list');
        if (!todoList || !data.todos) return;

        if (data.todos.length === 0) {
            todoList.innerHTML = `
                <div class="empty-state">
                    <div class="empty-state-icon">📝</div>
                    <div class="empty-state-text">暂无待办事项</div>
                    <div class="empty-state-desc">所有任务都已完成</div>
                </div>
            `;
            return;
        }

        todoList.innerHTML = data.todos.map(todo => {
            const priorityClass = this.getTodoPriorityClass(todo.priority);
            const actionText = todo.actionUrl ? '点击处理' : '';

            return `
                <div class="todo-item ${priorityClass}" data-id="${todo.id}" ${todo.actionUrl ? `onclick="window.todoManager.handleTodoAction('${todo.actionUrl}')"` : ''}>
                    <div class="todo-icon" style="background: ${todo.iconColor || '#3b82f6'}; color: white;">
                        ${todo.icon || '📋'}
                    </div>
                    <div class="todo-content">
                        <div class="todo-text">${todo.text}</div>
                        <div class="todo-meta">
                            <span class="todo-time">${this.formatActivityTime(todo.time)}</span>
                            ${todo.priority === 'high' ? '<span class="todo-urgent">紧急</span>' : ''}
                            ${actionText ? `<span class="todo-action">${actionText}</span>` : ''}
                        </div>
                    </div>
                </div>
            `;
        }).join('');
    }

    // 获取活动类型样式类
    getActivityTypeClass(type) {
        const typeClasses = {
            'application': 'activity-application',
            'maintenance': 'activity-maintenance',
            'device': 'activity-device',
            'default': 'activity-default'
        };
        return typeClasses[type] || typeClasses.default;
    }

    // 获取待办事项优先级样式类
    getTodoPriorityClass(priority) {
        const priorityClasses = {
            'high': 'todo-high-priority',
            'medium': 'todo-medium-priority',
            'normal': 'todo-normal-priority',
            'low': 'todo-low-priority'
        };
        return priorityClasses[priority] || priorityClasses.normal;
    }

    // 格式化活动时间（更详细的时间显示）
    formatActivityTime(timestamp) {
        if (!timestamp) return '';

        const now = new Date();
        const activityTime = new Date(timestamp);
        const diffMs = now - activityTime;
        const diffMinutes = Math.floor(diffMs / (1000 * 60));
        const diffHours = Math.floor(diffMs / (1000 * 60 * 60));
        const diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (diffMinutes < 1) {
            return '刚刚';
        } else if (diffMinutes < 60) {
            return `${diffMinutes}分钟前`;
        } else if (diffHours < 24) {
            return `${diffHours}小时前`;
        } else if (diffDays < 7) {
            return `${diffDays}天前`;
        } else {
            // 超过7天显示具体日期
            return activityTime.toLocaleDateString('zh-CN', {
                month: 'short',
                day: 'numeric',
                hour: '2-digit',
                minute: '2-digit'
            });
        }
    }

    // 刷新活动动态
    async refreshActivityFeed() {
        const refreshBtn = document.querySelector('.activity-refresh-btn');
        if (!refreshBtn) return;

        try {
            // 添加刷新动画
            refreshBtn.classList.add('refreshing');
            refreshBtn.disabled = true;

            // 并行重新加载活动数据和待办事项数据
            const [activityData, todoData] = await Promise.all([
                this.loadActivityData(),
                this.loadTodoData()
            ]);

            this.updateActivityFeed(activityData);
            this.updateTodoList(todoData);

            // 同时刷新待办事项管理器
            if (window.todoManager) {
                await window.todoManager.refreshTodos();
            }

            // 显示刷新成功提示
            this.showToast('数据已刷新', 'success');
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.showToast('刷新失败，请稍后重试', 'error');
        } finally {
            // 移除刷新动画
            setTimeout(() => {
                refreshBtn.classList.remove('refreshing');
                refreshBtn.disabled = false;
            }, 500);
        }
    }

    // 显示提示消息
    showToast(message, type = 'info') {
        // 创建提示元素
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;

        // 添加样式
        Object.assign(toast.style, {
            position: 'fixed',
            top: '20px',
            right: '20px',
            padding: '12px 20px',
            borderRadius: '6px',
            color: 'white',
            fontSize: '14px',
            zIndex: '10000',
            opacity: '0',
            transform: 'translateY(-20px)',
            transition: 'all 0.3s ease'
        });

        // 根据类型设置背景色
        const colors = {
            success: '#10b981',
            error: '#ef4444',
            info: '#3b82f6',
            warning: '#f59e0b'
        };
        toast.style.backgroundColor = colors[type] || colors.info;

        // 添加到页面
        document.body.appendChild(toast);

        // 显示动画
        setTimeout(() => {
            toast.style.opacity = '1';
            toast.style.transform = 'translateY(0)';
        }, 100);

        // 自动移除
        setTimeout(() => {
            toast.style.opacity = '0';
            toast.style.transform = 'translateY(-20px)';
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.parentNode.removeChild(toast);
                }
            }, 300);
        }, 3000);
    }

    // 格式化时间
    formatTime(timestamp) {
        const now = new Date();
        const time = new Date(timestamp);
        const diff = now - time;
        const minutes = Math.floor(diff / 60000);
        const hours = Math.floor(diff / 3600000);
        const days = Math.floor(diff / 86400000);

        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 7) return `${days}天前`;

        return time.toLocaleDateString('zh-CN');
    }

    // 显示加载状态
    showLoadingState() {
        const cards = document.querySelectorAll('.overview-card .card-value');
        cards.forEach(card => {
            card.classList.add('loading-skeleton');
            card.textContent = '';
        });
    }

    // 隐藏加载状态
    hideLoadingState() {
        const cards = document.querySelectorAll('.overview-card .card-value');
        cards.forEach(card => {
            card.classList.remove('loading-skeleton');
        });
    }

    // 显示错误信息
    showError(message) {
        console.error('Dashboard错误:', message);

        // 创建错误提示元素
        const errorDiv = document.createElement('div');
        errorDiv.className = 'alert alert-danger dashboard-error';
        errorDiv.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            z-index: 9999;
            max-width: 400px;
            padding: 15px;
            border-radius: 5px;
            background-color: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        `;

        // 针对网络错误提供更友好的提示
        let displayMessage = message;
        if (message.includes('timeout') || message.includes('超时') || message.includes('Failed to fetch') || message.includes('网络')) {
            displayMessage = `
                <strong><i class="fas fa-wifi"></i> 网络连接问题</strong><br>
                <small>网络延迟较高或连接不稳定，正在重试中...<br>
                如问题持续，请检查网络连接或稍后重试</small>
            `;
        }

        errorDiv.innerHTML = `
            <button type="button" class="close" style="float: right; background: none; border: none; font-size: 20px; cursor: pointer;">&times;</button>
            ${displayMessage}
        `;

        // 添加关闭按钮事件
        const closeBtn = errorDiv.querySelector('.close');
        closeBtn.onclick = () => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        };

        document.body.appendChild(errorDiv);

        // 5秒后自动移除
        setTimeout(() => {
            if (errorDiv.parentNode) {
                errorDiv.parentNode.removeChild(errorDiv);
            }
        }, 5000);
    }

    // 开始自动刷新
    startAutoRefresh() {
        // 每30秒刷新一次数据
        this.refreshInterval = setInterval(() => {
            this.loadDashboardData();
        }, 30000);
    }

    // 停止自动刷新
    stopAutoRefresh() {
        if (this.refreshInterval) {
            clearInterval(this.refreshInterval);
            this.refreshInterval = null;
        }
    }

    // 清除缓存并重新加载数据
    async refreshWithCacheClear() {
        try {
            // 清除API缓存
            if (typeof ApiCache !== 'undefined') {
                ApiCache.clear();
                console.log('API缓存已清除');
            }

            // 重新加载数据
            await this.loadDashboardData();
        } catch (error) {
            console.error('刷新数据失败:', error);
            this.showError('刷新数据失败，请稍后重试');
        }
    }

    // 调整图表大小
    resizeCharts() {
        try {
            Object.values(this.charts).forEach(chart => {
                if (chart && typeof chart.resize === 'function') {
                    chart.resize();
                }
            });
        } catch (error) {
            console.error('调整图表大小失败:', error);
        }
    }

    // 防抖函数
    debounce(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    }

    // 销毁Dashboard
    destroy() {
        this.stopAutoRefresh();
        Object.values(this.charts).forEach(chart => {
            if (chart && typeof chart.destroy === 'function') {
                chart.destroy();
            }
        });
        this.charts = {};
        this.isInitialized = false;
    }
}

// 全局Dashboard管理器实例
window.dashboardManager = null;

// 初始化Dashboard的全局函数
function initDashboard(username, role) {
    if (!window.dashboardManager) {
        window.dashboardManager = new DashboardManager();
    }
    return window.dashboardManager.init(username, role);
}
