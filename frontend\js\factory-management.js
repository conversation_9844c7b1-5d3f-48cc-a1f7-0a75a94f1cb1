// 厂区管理前端模块
class FactoryManager {
    constructor() {
        this.factories = [];
        this.devices = [];
        this.currentEditingFactory = null;
        this.currentUser = sessionStorage.getItem('username') || '';
        this.currentRole = sessionStorage.getItem('role') || '';
        this.currentDepartment = sessionStorage.getItem('department') || '';
        this.init();
    }

    // 初始化
    init() {
        this.bindEvents();
        this.loadFactories();
        this.loadDevices();
        this.updateButtonPermissions();
    }

    // 刷新用户权限（当用户信息变化时调用）
    refreshPermissions() {
        this.currentUser = sessionStorage.getItem('username') || '';
        this.currentRole = sessionStorage.getItem('role') || '';
        this.currentDepartment = sessionStorage.getItem('department') || '';
        this.updateButtonPermissions();
    }

    // 更新按钮权限
    updateButtonPermissions() {
        const isAdmin = this.currentRole === 'admin';

        // 厂区管理按钮权限控制
        const addFactoryBtn = document.getElementById('addFactoryBtn');

        if (addFactoryBtn) {
            if (isAdmin) {
                addFactoryBtn.style.display = '';
                addFactoryBtn.style.visibility = 'visible';
            } else {
                addFactoryBtn.style.display = 'none';
            }
        }
    }

    // 绑定事件
    bindEvents() {
        // 厂区管理按钮
        const manageFactoriesBtn = document.getElementById('manageFactoriesBtn');
        if (manageFactoriesBtn) {
            manageFactoriesBtn.addEventListener('click', () => this.showFactoryModal());
        }

        // 关闭厂区模态框
        const closeFactoryModalBtn = document.getElementById('closeFactoryModalBtn');
        if (closeFactoryModalBtn) {
            closeFactoryModalBtn.addEventListener('click', () => this.hideFactoryModal());
        }

        // 添加厂区按钮
        const addFactoryBtn = document.getElementById('addFactoryBtn');
        if (addFactoryBtn) {
            addFactoryBtn.addEventListener('click', () => this.showFactoryForm());
        }

        // 取消厂区表单
        const cancelFactoryFormBtn = document.getElementById('cancelFactoryFormBtn');
        if (cancelFactoryFormBtn) {
            cancelFactoryFormBtn.addEventListener('click', () => this.hideFactoryForm());
        }

        // 厂区表单提交
        const factoryForm = document.getElementById('factoryForm');
        if (factoryForm) {
            factoryForm.addEventListener('submit', (e) => this.handleFactoryFormSubmit(e));
        }
    }

    // 加载厂区列表
    async loadFactories() {
        try {
            const response = await apiRequest('/api/factories');
            if (response.success) {
                this.factories = response.factories;
                this.updateFactoryTable();
            } else {
                console.error('加载厂区列表失败:', response.message);
            }
        } catch (error) {
            console.error('加载厂区列表失败:', error);
        }
    }

    // 加载设备列表（用于统计设备数量）
    async loadDevices() {
        try {
            const response = await apiRequest('/api/devices');
            if (response.success) {
                this.devices = response.devices;
                this.updateFactoryTable();
            }
        } catch (error) {
            console.error('加载设备列表失败:', error);
        }
    }

    // 更新厂区表格
    updateFactoryTable() {
        const tbody = document.getElementById('factoryTableBody');
        if (!tbody) return;

        const isAdmin = this.currentRole === 'admin';

        if (this.factories.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="px-4 py-4 text-center text-gray-500">
                        暂无厂区数据
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.factories.map(factory => {
            const deviceCount = this.devices.filter(device => device.factory === factory.id).length;

            // 根据权限生成操作按钮
            let actionButtons = '';
            if (isAdmin) {
                actionButtons = `
                    <button onclick="factoryManager.editFactory('${factory.id}')"
                            class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                    <button onclick="factoryManager.deleteFactory('${factory.id}')"
                            class="text-red-600 hover:text-red-900 ${deviceCount > 0 ? 'opacity-50 cursor-not-allowed' : ''}"
                            ${deviceCount > 0 ? 'disabled' : ''}>删除</button>
                `;
            } else {
                actionButtons = `
                    <span class="text-gray-400 text-sm">仅查看</span>
                `;
            }

            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${factory.id}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${factory.name}
                    </td>
                    <td class="px-4 py-4 text-sm text-gray-900 max-w-xs truncate">
                        ${factory.description || '-'}
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${
                            deviceCount > 0 ? 'bg-blue-100 text-blue-800' : 'bg-gray-100 text-gray-800'
                        }">
                            ${deviceCount} 个设备
                        </span>
                    </td>
                    <td class="px-4 py-4 whitespace-nowrap text-sm font-medium">
                        ${actionButtons}
                    </td>
                </tr>
            `;
        }).join('');
    }

    // 显示厂区模态框
    showFactoryModal() {
        const modal = document.getElementById('factoryModal');
        if (modal) {
            modal.classList.remove('hidden');
            document.body.classList.add('modal-open');
            this.loadFactories();
            this.loadDevices();
        }
    }

    // 隐藏厂区模态框
    hideFactoryModal() {
        const modal = document.getElementById('factoryModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.classList.remove('modal-open');
            this.hideFactoryForm();
        }
    }

    // 显示厂区表单
    showFactoryForm(factory = null) {
        // 权限检查：只有管理员可以添加/编辑厂区
        const isAdmin = this.currentRole === 'admin';
        if (!isAdmin) {
            showError('权限不足，只有管理员可以管理厂区信息');
            return;
        }

        const container = document.getElementById('factoryFormContainer');
        const title = document.getElementById('factoryFormTitle');
        const form = document.getElementById('factoryForm');
        const factoryIdInput = document.getElementById('factoryId');

        if (!container || !title || !form) return;

        this.currentEditingFactory = factory;

        if (factory) {
            title.textContent = '编辑厂区';
            document.getElementById('factoryId').value = factory.id;
            document.getElementById('factoryName').value = factory.name;
            document.getElementById('factoryDescription').value = factory.description || '';
            // 编辑时禁用ID字段
            factoryIdInput.disabled = true;
        } else {
            title.textContent = '添加厂区';
            form.reset();
            // 添加时启用ID字段
            factoryIdInput.disabled = false;
        }

        container.classList.remove('hidden');
    }

    // 隐藏厂区表单
    hideFactoryForm() {
        const container = document.getElementById('factoryFormContainer');
        if (container) {
            container.classList.add('hidden');
            this.currentEditingFactory = null;
        }
    }

    // 处理厂区表单提交
    async handleFactoryFormSubmit(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const factoryData = {
            id: formData.get('id'),
            name: formData.get('name'),
            description: formData.get('description')
        };

        // 验证必填字段
        if (this.currentEditingFactory) {
            // 编辑模式：只验证厂区名称
            if (!factoryData.name) {
                showError('厂区名称为必填项');
                return;
            }
            // 编辑时使用当前编辑厂区的ID
            factoryData.id = this.currentEditingFactory.id;
        } else {
            // 添加模式：验证厂区ID和名称
            if (!factoryData.id || !factoryData.name) {
                showError('厂区ID和厂区名称为必填项');
                return;
            }
        }

        try {
            showLoading(this.currentEditingFactory ? '正在更新厂区...' : '正在添加厂区...');

            // 添加用户信息用于权限验证
            const requestData = {
                ...factoryData,
                username: this.currentUser,
                role: this.currentRole,
                department: this.currentDepartment
            };

            let response;
            if (this.currentEditingFactory) {
                // 更新厂区
                response = await apiRequest(`/api/factories/${this.currentEditingFactory.id}`, {
                    method: 'PUT',
                    data: requestData
                });
            } else {
                // 添加厂区
                response = await apiRequest('/api/factories', {
                    method: 'POST',
                    data: requestData
                });
            }

            if (response.success) {
                showSuccess(response.message);
                this.hideFactoryForm();
                this.loadFactories();
                
                // 通知其他组件更新厂区选择
                this.notifyFactoryUpdate();
            } else {
                showError(response.message);
            }
        } catch (error) {
            console.error('保存厂区失败:', error);
            showError('保存厂区失败');
        } finally {
            hideLoading();
        }
    }

    // 编辑厂区
    editFactory(factoryId) {
        // 权限检查：只有管理员可以编辑厂区
        const isAdmin = this.currentRole === 'admin';
        if (!isAdmin) {
            showError('权限不足，只有管理员可以编辑厂区信息');
            return;
        }

        const factory = this.factories.find(f => f.id === factoryId);
        if (factory) {
            this.showFactoryForm(factory);
        }
    }

    // 删除厂区
    async deleteFactory(factoryId) {
        // 权限检查：只有管理员可以删除厂区
        const isAdmin = this.currentRole === 'admin';
        if (!isAdmin) {
            showError('权限不足，只有管理员可以删除厂区信息');
            return;
        }

        const factory = this.factories.find(f => f.id === factoryId);
        if (!factory) return;

        // 检查是否有设备使用该厂区
        const deviceCount = this.devices.filter(device => device.factory === factoryId).length;
        if (deviceCount > 0) {
            showError(`无法删除厂区"${factory.name}"，还有 ${deviceCount} 个设备正在使用该厂区`);
            return;
        }

        if (!confirm(`确定要删除厂区"${factory.name}"吗？删除后无法恢复。`)) {
            return;
        }

        try {
            showLoading('正在删除厂区...');

            const response = await apiRequest(`/api/factories/${factoryId}`, {
                method: 'DELETE',
                data: {
                    username: this.currentUser,
                    role: this.currentRole,
                    department: this.currentDepartment
                }
            });

            if (response.success) {
                showSuccess(response.message);
                this.loadFactories();
                
                // 通知其他组件更新厂区选择
                this.notifyFactoryUpdate();
            } else {
                showError(response.message);
            }
        } catch (error) {
            console.error('删除厂区失败:', error);
            showError('删除厂区失败');
        } finally {
            hideLoading();
        }
    }

    // 通知其他组件厂区数据已更新
    notifyFactoryUpdate() {
        // 通知设备管理组件更新厂区选择
        if (window.deviceManager) {
            window.deviceManager.reloadFactories();
        }

        // 通知维修保养管理组件更新厂区选择
        if (window.maintenanceManager) {
            window.maintenanceManager.reloadFactories();
        }
    }
}

// 全局厂区管理实例
let factoryManager = null;

// 初始化厂区管理
function initFactoryManagement() {
    if (!factoryManager) {
        factoryManager = new FactoryManager();
    }
    return factoryManager;
}
