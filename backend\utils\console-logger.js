/**
 * 全局控制台日志工具
 * 为所有console.log输出添加统一的时间戳格式
 */

/**
 * 获取格式化的时间戳
 */
function getFormattedTimestamp() {
    const now = new Date();
    const timeStr = now.toLocaleString('zh-CN', {
        year: 'numeric',
        month: '2-digit',
        day: '2-digit',
        hour: '2-digit',
        minute: '2-digit',
        second: '2-digit',
        hour12: false
    });
    return `【${timeStr}】`;
}

/**
 * 带时间戳的日志输出
 */
function logWithTime(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}${message}`, ...args);
}

/**
 * 带时间戳的错误输出
 */
function errorWithTime(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.error(`${timestamp}${message}`, ...args);
}

/**
 * 带时间戳的警告输出
 */
function warnWithTime(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.warn(`${timestamp}${message}`, ...args);
}

/**
 * 带时间戳的信息输出
 */
function infoWithTime(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.info(`${timestamp}${message}`, ...args);
}

/**
 * 带时间戳的调试输出
 */
function debugWithTime(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.debug(`${timestamp}${message}`, ...args);
}

/**
 * 系统启动日志
 */
function systemStart(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}🚀 ${message}`, ...args);
}

/**
 * 系统错误日志
 */
function systemError(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.error(`${timestamp}❌ ${message}`, ...args);
}

/**
 * 系统成功日志
 */
function systemSuccess(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}✅ ${message}`, ...args);
}

/**
 * 系统警告日志
 */
function systemWarn(message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.warn(`${timestamp}⚠️ ${message}`, ...args);
}

/**
 * 数据库操作日志
 */
function dbLog(operation, message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}🗄️ [${operation}] ${message}`, ...args);
}

/**
 * API请求日志
 */
function apiLog(method, endpoint, message, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}🌐 [${method}] ${endpoint} ${message}`, ...args);
}

/**
 * 性能监控日志
 */
function perfLog(operation, duration, ...args) {
    const timestamp = getFormattedTimestamp();
    const icon = duration > 1000 ? '🐌' : '⚡';
    console.log(`${timestamp}${icon} [${operation}] ${duration}ms`, ...args);
}

/**
 * 用户操作日志
 */
function userLog(userId, action, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}👤 [${userId}] ${action}`, ...args);
}

/**
 * 文件操作日志
 */
function fileLog(operation, filename, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}📁 [${operation}] ${filename}`, ...args);
}

/**
 * 安全相关日志
 */
function securityLog(event, details, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}🔒 [SECURITY] ${event} ${details}`, ...args);
}

/**
 * 备份操作日志
 */
function backupLog(operation, details, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}💾 [BACKUP] ${operation} ${details}`, ...args);
}

/**
 * 网络相关日志
 */
function networkLog(event, details, ...args) {
    const timestamp = getFormattedTimestamp();
    console.log(`${timestamp}🌐 [NETWORK] ${event} ${details}`, ...args);
}

module.exports = {
    // 基础日志函数
    log: logWithTime,
    error: errorWithTime,
    warn: warnWithTime,
    info: infoWithTime,
    debug: debugWithTime,
    
    // 系统级日志
    systemStart,
    systemError,
    systemSuccess,
    systemWarn,
    
    // 功能分类日志
    dbLog,
    apiLog,
    perfLog,
    userLog,
    fileLog,
    securityLog,
    backupLog,
    networkLog,
    
    // 工具函数
    getFormattedTimestamp
};
