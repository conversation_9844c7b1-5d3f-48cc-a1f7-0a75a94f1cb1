const Database = require('better-sqlite3');
const path = require('path');
const fs = require('fs');

class DatabaseManager {
    constructor() {
        // 确保数据目录存在
        const dataDir = path.join(__dirname, 'data');
        if (!fs.existsSync(dataDir)) {
            fs.mkdirSync(dataDir, { recursive: true });
        }

        // 创建数据库连接
        this.db = new Database(path.join(dataDir, 'application_system.db'));
        this.db.pragma('journal_mode = WAL');
        this.db.pragma('foreign_keys = ON');

        this.initializeTables();
    }

    initializeTables() {
        // 用户表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS users (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                username TEXT UNIQUE NOT NULL,
                password TEXT NOT NULL,
                role TEXT NOT NULL,
                email TEXT DEFAULT '',
                department TEXT DEFAULT '',
                userCode TEXT DEFAULT '',
                userId TEXT UNIQUE,
                signature TEXT DEFAULT '',
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 部门表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS departments (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                code TEXT UNIQUE NOT NULL,
                description TEXT,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 工厂表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS factories (
                id TEXT PRIMARY KEY,
                name TEXT NOT NULL,
                description TEXT DEFAULT '',
                createTime DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 设备表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS devices (
                id TEXT PRIMARY KEY,
                deviceCode TEXT UNIQUE NOT NULL,
                deviceName TEXT NOT NULL,
                factory TEXT NOT NULL,
                location TEXT,
                responsible TEXT,
                entryDate DATE,
                status TEXT DEFAULT '启用',
                createTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                updateTime DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (factory) REFERENCES factories(id)
            )
        `);

        // 申请表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS applications (
                id INTEGER PRIMARY KEY,
                applicant TEXT NOT NULL,
                department TEXT,
                date DATE NOT NULL,
                content TEXT NOT NULL,
                amount DECIMAL(10,2),
                currency TEXT DEFAULT 'CNY',
                priority TEXT DEFAULT 'normal',
                attachments TEXT, -- JSON格式存储附件信息
                username TEXT,
                applicationCode TEXT UNIQUE,
                status TEXT DEFAULT '待厂长审核',
                reminderInfo TEXT, -- JSON格式存储提醒信息
                stageTimestamps TEXT, -- JSON格式存储阶段时间戳
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 申请审批表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS application_approvals (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                applicationId INTEGER NOT NULL,
                approverType TEXT NOT NULL, -- 'director', 'manager', 'chief', 'ceo'
                approverUsername TEXT NOT NULL,
                status TEXT DEFAULT 'pending', -- 'pending', 'approved', 'rejected'
                comment TEXT,
                attachments TEXT, -- JSON格式存储附件信息
                approvedAt DATETIME,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (applicationId) REFERENCES applications(id) ON DELETE CASCADE
            )
        `);

        // 维修记录表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS maintenance_records (
                id INTEGER PRIMARY KEY,
                recordCode TEXT, -- 记录编号
                deviceId TEXT NOT NULL,
                deviceCode TEXT NOT NULL,
                deviceName TEXT NOT NULL,
                factory TEXT NOT NULL,
                location TEXT,
                responsible TEXT,
                maintenanceType TEXT NOT NULL,
                reportedBy TEXT NOT NULL,
                reportDate DATE NOT NULL,
                startTime DATETIME, -- 开始时间
                endTime DATETIME, -- 结束时间
                description TEXT NOT NULL,
                result TEXT, -- 维修结果
                reviewer TEXT, -- 审查人员
                faultSeverity TEXT, -- 故障程度
                customFields TEXT, -- JSON格式存储自定义字段
                priority TEXT DEFAULT 'normal',
                status TEXT DEFAULT 'pending',
                assignedTo TEXT,
                completedBy TEXT,
                completedDate DATE,
                cost DECIMAL(10,2),
                parts TEXT,
                solution TEXT,
                attachments TEXT, -- JSON格式存储附件信息
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP,
                FOREIGN KEY (deviceId) REFERENCES devices(id)
            )
        `);

        // 提醒设置表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS reminder_settings (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                priority TEXT NOT NULL,
                initialDelay INTEGER NOT NULL,
                normalInterval INTEGER NOT NULL,
                mediumInterval INTEGER NOT NULL,
                urgentInterval INTEGER NOT NULL,
                workingDaysEnabled BOOLEAN DEFAULT 0,
                workingDays TEXT, -- JSON格式存储工作日
                workingStartTime TEXT DEFAULT '09:00',
                workingEndTime TEXT DEFAULT '18:00',
                customDatesEnabled BOOLEAN DEFAULT 0,
                skipDates TEXT, -- JSON格式存储跳过的日期
                updatedAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 维修模板表
        this.db.exec(`
            CREATE TABLE IF NOT EXISTS maintenance_templates (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                maintenanceType TEXT NOT NULL,
                field TEXT NOT NULL,
                label TEXT NOT NULL,
                type TEXT NOT NULL,
                required BOOLEAN DEFAULT 0,
                createdAt DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        `);

        // 创建索引
        this.createIndexes();
    }

    createIndexes() {
        const indexes = [
            'CREATE INDEX IF NOT EXISTS idx_users_username ON users(username)',
            'CREATE INDEX IF NOT EXISTS idx_users_userId ON users(userId)',
            'CREATE INDEX IF NOT EXISTS idx_devices_deviceCode ON devices(deviceCode)',
            'CREATE INDEX IF NOT EXISTS idx_devices_factory ON devices(factory)',
            'CREATE INDEX IF NOT EXISTS idx_applications_applicant ON applications(applicant)',
            'CREATE INDEX IF NOT EXISTS idx_applications_date ON applications(date)',
            'CREATE INDEX IF NOT EXISTS idx_applications_status ON applications(status)',
            'CREATE INDEX IF NOT EXISTS idx_application_approvals_applicationId ON application_approvals(applicationId)',
            'CREATE INDEX IF NOT EXISTS idx_maintenance_records_deviceId ON maintenance_records(deviceId)',
            'CREATE INDEX IF NOT EXISTS idx_maintenance_records_status ON maintenance_records(status)'
        ];

        indexes.forEach(indexSql => {
            this.db.exec(indexSql);
        });
    }

    // 获取数据库实例
    getDatabase() {
        return this.db;
    }

    // 关闭数据库连接
    close() {
        this.db.close();
    }

    // 执行事务
    transaction(callback) {
        const transaction = this.db.transaction(callback);
        return transaction;
    }
}

module.exports = DatabaseManager;
