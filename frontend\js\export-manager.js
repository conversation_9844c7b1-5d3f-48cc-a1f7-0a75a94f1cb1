// 导出管理器
class ExportManager {
    constructor() {
        this.isInitialized = false;
        this.factories = [];
    }

    // 初始化
    init() {
        this.bindEvents();
        this.loadFactories();
        this.isInitialized = true;
    }

    // 绑定事件
    bindEvents() {
        // 设备导出按钮事件已在HTML中绑定
        // 记录导出按钮事件已在HTML中绑定
    }

    // 加载厂区列表
    async loadFactories() {
        try {
            const response = await fetch('/api/factories');
            const data = await response.json();

            if (data.success) {
                this.factories = data.factories;
                this.updateFactorySelect();
            } else {
                console.error('加载厂区列表失败:', data.message);
            }
        } catch (error) {
            console.error('加载厂区列表失败:', error);
        }
    }

    // 更新厂区选择下拉框
    updateFactorySelect() {
        const factorySelect = document.getElementById('deviceExportFactory');
        if (!factorySelect) return;

        // 清空现有选项（保留"所有厂区"选项）
        factorySelect.innerHTML = '<option value="all">所有厂区</option>';

        // 添加厂区选项
        this.factories.forEach(factory => {
            const option = document.createElement('option');
            option.value = factory.id;
            option.textContent = factory.name;
            factorySelect.appendChild(option);
        });
    }

    // 导出设备数据
    async exportDevices(buttonElement = null) {
        let button = buttonElement;
        let originalText = '';

        try {
            const factory = document.getElementById('deviceExportFactory').value;
            const range = document.getElementById('deviceExportRange').value;
            const format = document.getElementById('deviceExportFormat').value;

            // 显示加载状态
            if (!button && typeof event !== 'undefined') {
                button = event.target;
            }
            if (button) {
                originalText = button.textContent;
                button.textContent = '导出中...';
                button.disabled = true;
            }

            // 构建筛选条件
            const filters = {};

            // 厂区筛选
            if (factory && factory !== 'all') {
                filters.factory = factory;
            }

            // 状态筛选
            if (range !== 'all') {
                filters.status = range === 'active' ? '启用' : '停用';
            }

            const response = await fetch('/api/export/devices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filters: filters,
                    filename: `设备列表_${new Date().toISOString().slice(0, 10)}.xlsx`
                })
            });

            if (!response.ok) {
                throw new Error('导出失败');
            }

            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = '设备清单.xlsx';
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename="(.+)"/);
                if (matches) {
                    filename = decodeURIComponent(matches[1]);
                }
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 显示成功消息
            this.showMessage('设备数据导出成功！', 'success');

        } catch (error) {
            console.error('导出设备数据失败:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            if (button && originalText) {
                button.textContent = originalText;
                button.disabled = false;
            }
        }
    }

    // 导出维修保养记录
    async exportMaintenanceRecords(buttonElement = null) {
        let button = buttonElement;
        let originalText = '';

        try {
            const type = document.getElementById('recordExportType').value;
            const startDate = document.getElementById('recordExportStartDate').value;
            const endDate = document.getElementById('recordExportEndDate').value;
            const format = document.getElementById('recordExportFormat').value;

            // 显示加载状态
            if (!button && typeof event !== 'undefined') {
                button = event.target;
            }
            if (button) {
                originalText = button.textContent;
                button.textContent = '导出中...';
                button.disabled = true;
            }

            const response = await fetch('/api/export/maintenance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filters: {
                        type: type,
                        startDate: startDate,
                        endDate: endDate
                    },
                    filename: `维修保养记录_${new Date().toISOString().slice(0, 10)}.xlsx`
                })
            });

            if (!response.ok) {
                throw new Error('导出失败');
            }

            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let filename = '维修保养记录.xlsx';
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename="(.+)"/);
                if (matches) {
                    filename = decodeURIComponent(matches[1]);
                }
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = filename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 构建详细的范围信息
            const typeText = type === 'all' ? '所有记录' :
                           type === '维修' ? '维修记录' :
                           type === '保养' ? '保养记录' :
                           type === '临时保养' ? '临时保养记录' : type;

            let rangeInfo = typeText;
            if (startDate && endDate) {
                rangeInfo += ` (${startDate} 至 ${endDate})`;
            } else if (startDate) {
                rangeInfo += ` (${startDate} 起)`;
            } else if (endDate) {
                rangeInfo += ` (至 ${endDate})`;
            }

            // 显示成功消息
            this.showMessage('维修保养记录导出成功！', 'success');

        } catch (error) {
            console.error('导出维修保养记录失败:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        } finally {
            // 恢复按钮状态
            if (button && originalText) {
                button.textContent = originalText;
                button.disabled = false;
            }
        }
    }

    // 导出申请记录（包装现有的导出函数）
    async exportApplicationRecords(button = null) {
        try {
            // 显示加载状态
            if (button) {
                const originalText = button.textContent;
                button.textContent = '导出中...';
                button.disabled = true;

                // 设置恢复函数
                setTimeout(() => {
                    button.textContent = originalText;
                    button.disabled = false;
                }, 3000); // 3秒后恢复按钮状态
            }

            // 调用现有的导出函数
            if (typeof exportApplicationsToExcel === 'function') {
                await exportApplicationsToExcel();

                // 显示成功消息
                this.showMessage('申请记录导出成功！', 'success');
            } else {
                throw new Error('导出函数不可用，请确保您在正确的页面上');
            }

        } catch (error) {
            console.error('导出申请记录失败:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        }
    }

    // 导出记录数据（向后兼容的别名方法）
    async exportRecords(button = null) {
        return this.exportMaintenanceRecords(button);
    }





    // 显示消息
    showMessage(message, type = 'info') {
        // 创建消息元素
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 z-50 p-4 rounded-lg shadow-lg max-w-sm ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        messageDiv.textContent = message;

        // 添加到页面
        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (messageDiv.parentNode) {
                messageDiv.parentNode.removeChild(messageDiv);
            }
        }, 3000);
    }
}

// 创建全局实例
const exportManager = new ExportManager();
