/**
 * API请求工具
 * 包含超时设置、自动重试和错误处理
 */

// 网络配置常量 - 优化国际网络兼容性
const API_CONFIG = {
    // 请求超时时间（毫秒）- 增加超时时间适应国际网络
    timeout: 30000,

    // 重试配置
    retry: {
        // 最大重试次数 - 增加重试次数提高成功率
        maxRetries: 5,
        // 重试延迟（毫秒）- 增加重试延迟适应网络波动
        retryDelay: 2000,
        // 需要重试的HTTP状态码
        retryStatusCodes: [408, 429, 500, 502, 503, 504, 0] // 添加0状态码（网络错误）
    },

    // 网络兼容性配置
    network: {
        // 支持代理和VPN
        supportProxy: true,
        // 自动检测网络状态
        autoDetectNetwork: true,
        // 网络质量检测间隔（毫秒）
        qualityCheckInterval: 30000
    },

    // 缓存配置
    cache: {
        // 启用前端缓存
        enabled: true,
        // 缓存有效期（毫秒）- 5分钟
        ttl: 5 * 60 * 1000,
        // 缓存的API接口
        cachedApis: ['/api/dashboard/overview', '/api/dashboard/charts', '/api/dashboard/activity']
    },

    // 调试模式
    debug: true
};

// 前端缓存管理
const ApiCache = {
    storage: new Map(),

    // 生成缓存键
    generateKey: (url, options = {}) => {
        const method = options.method || 'GET';
        const data = options.data ? JSON.stringify(options.data) : '';
        return `${method}:${url}:${data}`;
    },

    // 获取缓存
    get: (key) => {
        const cached = ApiCache.storage.get(key);
        if (!cached) return null;

        // 检查是否过期
        if (Date.now() > cached.expiry) {
            ApiCache.storage.delete(key);
            return null;
        }

        return cached.data;
    },

    // 设置缓存
    set: (key, data, ttl = API_CONFIG.cache.ttl) => {
        ApiCache.storage.set(key, {
            data: data,
            expiry: Date.now() + ttl,
            timestamp: Date.now()
        });
    },

    // 清除过期缓存
    cleanup: () => {
        const now = Date.now();
        for (const [key, value] of ApiCache.storage.entries()) {
            if (now > value.expiry) {
                ApiCache.storage.delete(key);
            }
        }
    },

    // 清除所有缓存
    clear: () => {
        ApiCache.storage.clear();
    },

    // 检查URL是否应该缓存
    shouldCache: (url, method = 'GET') => {
        if (!API_CONFIG.cache.enabled || method !== 'GET') {
            return false;
        }
        return API_CONFIG.cache.cachedApis.some(pattern => url.includes(pattern));
    }
};

// 定期清理过期缓存
setInterval(() => {
    ApiCache.cleanup();
}, 60000); // 每分钟清理一次

/**
 * 发送API请求
 * @param {string} url - 请求URL
 * @param {Object} options - 请求选项
 * @param {string} options.method - 请求方法 (GET, POST, PUT, DELETE)
 * @param {Object} options.data - 请求数据
 * @param {Object} options.headers - 请求头
 * @param {number} options.timeout - 请求超时时间（毫秒）
 * @param {Object} options.retry - 重试配置
 * @returns {Promise<any>} 响应数据
 */
async function apiRequest(url, options = {}) {
    const {
        method = 'GET',
        data = null,
        headers = {},
        timeout = API_CONFIG.timeout,
        retry = API_CONFIG.retry,
        useCache = true
    } = options;

    // 检查缓存
    if (useCache && ApiCache.shouldCache(url, method)) {
        const cacheKey = ApiCache.generateKey(url, { method, data });
        const cachedData = ApiCache.get(cacheKey);

        if (cachedData) {
            if (API_CONFIG.debug) {
                console.log(`API缓存命中: ${method} ${url}`);
            }
            return cachedData;
        }
    }

    // 合并默认头部
    const requestHeaders = {
        'Content-Type': 'application/json',
        ...headers
    };

    // 构建请求选项
    const fetchOptions = {
        method,
        headers: requestHeaders,
        credentials: 'same-origin',
        // 添加缓存控制，避免代理缓存问题
        cache: 'no-cache',
        // 添加模式设置，确保跨域兼容
        mode: 'cors'
    };

    // 添加请求体
    if (data && (method === 'POST' || method === 'PUT' || method === 'PATCH' || method === 'DELETE')) {
        fetchOptions.body = JSON.stringify(data);
    }

    // 创建AbortController用于超时控制
    const controller = new AbortController();
    fetchOptions.signal = controller.signal;

    // 设置超时
    const timeoutId = setTimeout(() => {
        controller.abort();
    }, timeout);

    // 调试日志
    if (API_CONFIG.debug) {
        console.log(`API请求: ${method} ${url}`, {
            data,
            timeout,
            retry: { ...retry }
        });
    }

    // 重试计数器
    let retries = 0;

    // 执行请求（带重试）
    while (true) {
        try {
            const response = await fetch(url, fetchOptions);

            // 清除超时
            clearTimeout(timeoutId);

            // 检查是否需要重试
            if (!response.ok && retry.retryStatusCodes.includes(response.status) && retries < retry.maxRetries) {
                retries++;

                if (API_CONFIG.debug) {
                    console.log(`请求失败，状态码: ${response.status}，准备第 ${retries} 次重试...`);
                }

                // 等待重试延迟
                await new Promise(resolve => setTimeout(resolve, retry.retryDelay));
                continue;
            }

            // 尝试解析JSON响应
            try {
                const data = await response.json();

                if (!response.ok) {
                    throw {
                        status: response.status,
                        message: data.message || '请求失败',
                        data
                    };
                }

                // 缓存成功的响应
                if (useCache && ApiCache.shouldCache(url, method)) {
                    const cacheKey = ApiCache.generateKey(url, { method, data });
                    ApiCache.set(cacheKey, data);

                    if (API_CONFIG.debug) {
                        console.log(`API响应已缓存: ${method} ${url}`);
                    }
                }

                return data;
            } catch (parseError) {
                // JSON解析错误
                if (response.ok) {
                    // 如果响应成功但JSON解析失败，返回文本内容
                    const text = await response.text();
                    return { text };
                } else {
                    throw {
                        status: response.status,
                        message: '无法解析响应数据',
                        error: parseError
                    };
                }
            }
        } catch (error) {
            // 清除超时
            clearTimeout(timeoutId);

            // 处理请求被中止的情况（超时）
            if (error.name === 'AbortError') {
                if (retries < retry.maxRetries) {
                    retries++;

                    if (API_CONFIG.debug) {
                        console.log(`请求超时，准备第 ${retries} 次重试...`);
                    }

                    // 创建新的AbortController
                    const controller = new AbortController();
                    fetchOptions.signal = controller.signal;

                    // 设置新的超时
                    const timeoutId = setTimeout(() => {
                        controller.abort();
                    }, timeout);

                    // 等待重试延迟
                    await new Promise(resolve => setTimeout(resolve, retry.retryDelay));
                    continue;
                } else {
                    throw {
                        status: 'TIMEOUT',
                        message: '请求超时，请检查网络连接'
                    };
                }
            }

            // 如果是网络错误且还有重试次数
            if ((error.message && (error.message.includes('network') ||
                                  error.message.includes('Failed to fetch') ||
                                  error.message.includes('ERR_NETWORK') ||
                                  error.message.includes('ERR_INTERNET_DISCONNECTED'))) &&
                retries < retry.maxRetries) {
                retries++;

                if (API_CONFIG.debug) {
                    console.log(`网络错误，准备第 ${retries} 次重试...`, error.message);
                }

                // 等待重试延迟，对于网络错误增加延迟时间
                const networkRetryDelay = retry.retryDelay * (retries + 1);
                await new Promise(resolve => setTimeout(resolve, networkRetryDelay));
                continue;
            }

            // 其他错误或重试次数已用完
            throw error;
        }
    }
}

// 导出API方法
window.api = {
    /**
     * 发送GET请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    get: (url, options = {}) => {
        return apiRequest(url, { ...options, method: 'GET' });
    },

    /**
     * 发送POST请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    post: (url, data, options = {}) => {
        return apiRequest(url, { ...options, method: 'POST', data });
    },

    /**
     * 发送PUT请求
     * @param {string} url - 请求URL
     * @param {Object} data - 请求数据
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    put: (url, data, options = {}) => {
        return apiRequest(url, { ...options, method: 'PUT', data });
    },

    /**
     * 发送DELETE请求
     * @param {string} url - 请求URL
     * @param {Object} options - 请求选项
     * @returns {Promise<any>} 响应数据
     */
    delete: (url, options = {}) => {
        return apiRequest(url, { ...options, method: 'DELETE' });
    }
};
