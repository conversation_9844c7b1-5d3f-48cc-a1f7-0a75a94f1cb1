/**
 * 模块统一导出文件
 * 提供所有模块的统一访问入口
 */

// 认证和授权模块
const AuthService = require('./auth/auth-service');
const SessionManager = require('./auth/session-manager');
const AccessControl = require('./auth/access-control');
const PasswordPolicy = require('./auth/password-policy');

// 安全模块
const FileSecurity = require('./security/file-security');
const ValidationMiddleware = require('./security/validation-middleware');

// 数据库模块
const DatabaseOptimizer = require('./database/database-optimizer');
const DataConsistencyChecker = require('./database/data-consistency-checker');
const ConsistencyScheduler = require('./database/consistency-scheduler');

// 备份模块
const BackupManager = require('./backup/backup-manager');
const BackupScheduler = require('./backup/backup-scheduler');

// 监控模块
const PerformanceMonitor = require('./monitoring/performance-monitor');
const Logger = require('./monitoring/logger');
const ErrorHandler = require('./monitoring/error-handler');

// 业务逻辑模块
const DeviceHealth = require('./business/device-health');
const DeviceManagement = require('./business/device-management');
const MaintenanceManagement = require('./business/maintenance-management');
const ExportService = require('./business/export-service');

// 基础设施模块
const CacheManager = require('./infrastructure/cache-manager');
const SystemIntegration = require('./infrastructure/system-integration');

/**
 * 按功能分组导出模块
 */
module.exports = {
    // 认证和授权
    auth: {
        AuthService,
        SessionManager,
        AccessControl,
        PasswordPolicy
    },

    // 安全
    security: {
        FileSecurity,
        ValidationMiddleware
    },

    // 数据库
    database: {
        DatabaseOptimizer,
        DataConsistencyChecker,
        ConsistencyScheduler
    },

    // 备份
    backup: {
        BackupManager,
        BackupScheduler
    },

    // 监控
    monitoring: {
        PerformanceMonitor,
        Logger,
        ErrorHandler
    },

    // 业务逻辑
    business: {
        DeviceHealth,
        DeviceManagement,
        MaintenanceManagement,
        ExportService
    },

    // 基础设施
    infrastructure: {
        CacheManager,
        SystemIntegration
    },

    // 向后兼容的直接导出（保持原有导入方式可用）
    AuthService,
    SessionManager,
    AccessControl,
    PasswordPolicy,
    FileSecurity,
    ValidationMiddleware,
    DatabaseOptimizer,
    DataConsistencyChecker,
    ConsistencyScheduler,
    BackupManager,
    BackupScheduler,
    PerformanceMonitor,
    Logger,
    ErrorHandler,
    DeviceHealth,
    DeviceManagement,
    MaintenanceManagement,
    ExportService,
    CacheManager,
    SystemIntegration
};
