# 维修保养记录页面完整代码实现

## 1. 页面结构 (HTML)

### 主要页面结构
```html
<!-- 维修记录管理 -->
<section id="maintenanceRecordsSection" class="hidden bg-white p-6 rounded-lg shadow-md">
    <div class="device-management-container">
        <!-- 维修保养管理头部 -->
        <div class="device-management-header">
            <h2 class="device-management-title">维修/保养记录管理</h2>
            <div class="device-management-actions">
                <button id="addMaintenanceRecordBtn" class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    添加记录
                </button>
                <button id="batchDeleteMaintenanceBtn" class="btn btn-danger">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    批量删除
                </button>
                <button id="exportMaintenanceBtn" class="btn btn-success">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    导出记录
                </button>
                <button id="refreshMaintenanceBtn" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    刷新
                </button>
            </div>
        </div>

        <!-- 选择计数器 -->
        <div id="selectedRecordCount" class="selection-counter hidden"></div>

        <!-- 维修保养记录筛选器 -->
        <div class="device-filters">
            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">搜索记录</label>
                    <input type="text" id="maintenanceSearchInput" class="filter-input" placeholder="搜索记录编号、描述或操作人...">
                </div>
                <div class="filter-group">
                    <label class="filter-label">记录类型</label>
                    <select id="maintenanceTypeFilter" class="filter-input">
                        <option value="">全部类型</option>
                        <option value="维修">维修</option>
                        <option value="保养">保养</option>
                        <option value="临时保养">临时保养</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">厂区</label>
                    <select id="maintenanceFactoryFilter" class="filter-input">
                        <option value="">全部厂区</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">关联设备</label>
                    <div class="device-search-container" style="position: relative;">
                        <input type="text"
                               id="maintenanceDeviceSearch"
                               class="filter-input"
                               placeholder="全部设备"
                               autocomplete="off"
                               style="background-color: white;">
                        <input type="hidden" id="maintenanceDeviceFilter">
                        <div id="maintenanceDeviceSearchResults"
                             class="device-search-results"
                             style="display: none; position: absolute; top: 100%; left: 0; right: 0; z-index: 1000; background: white; border: 1px solid #d1d5db; border-top: none; border-radius: 0 0 6px 6px; max-height: 200px; overflow-y: auto; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);">
                        </div>
                    </div>
                </div>
                <div class="filter-group">
                    <label class="filter-label">操作人</label>
                    <input type="text" id="maintenanceOperatorFilter" class="filter-input" placeholder="筛选操作人...">
                </div>
                <div class="filter-group">
                    <label class="filter-label">开始日期</label>
                    <input type="date" id="maintenanceStartDate" class="filter-input">
                </div>
                <div class="filter-group">
                    <label class="filter-label">结束日期</label>
                    <input type="date" id="maintenanceEndDate" class="filter-input">
                </div>
            </div>
        </div>

        <!-- 滚动提示 -->
        <div class="scroll-hint"></div>

        <!-- 维修保养记录表格 -->
        <div class="device-table-container">
            <table class="device-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAllMaintenanceRecords" class="form-checkbox">
                        </th>
                        <th>记录编号</th>
                        <th>厂区</th>
                        <th>设备</th>
                        <th>类型</th>
                        <th>日期</th>
                        <th>操作人</th>
                        <th>描述</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="maintenanceTableBody">
                    <tr>
                        <td colspan="10" class="px-6 py-4 text-center text-gray-500">
                            正在加载维修保养记录...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div id="maintenancePagination" class="pagination-container">
            <div class="text-sm text-gray-700">
                正在加载...
            </div>
        </div>
    </div>
</section>
```

### 模态框结构
```html
<!-- 维修保养记录模态框 -->
<div id="maintenanceModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="device-modal-content" style="max-width: 600px;">
        <div class="device-modal-header">
            <h2 id="maintenanceModalTitle" class="device-modal-title">添加维修/保养记录</h2>
        </div>
        <div class="device-modal-body">
            <form id="maintenanceForm" class="space-y-4">
                <div class="form-group">
                    <label class="form-label">记录编号</label>
                    <input type="text" id="recordCode" name="recordCode" class="form-input bg-gray-100" readonly
                           placeholder="选择厂区后自动生成">
                    <p class="text-sm text-gray-500 mt-1">记录编号将根据选择的厂区和日期自动生成</p>
                </div>
                <div class="form-group">
                    <label class="form-label">厂区 *</label>
                    <select id="recordFactory" name="factory" class="form-select" required>
                        <option value="">请选择厂区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">位置</label>
                    <input type="text" id="recordLocation" name="location" class="form-input" placeholder="具体位置">
                </div>
                <div class="form-group">
                    <label class="form-label">关联设备 *</label>
                    <select id="recordDevice" name="deviceId" class="form-select" required>
                        <option value="">请选择设备</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">记录类型 *</label>
                    <select id="recordType" name="type" class="form-select" required>
                        <option value="维修">维修</option>
                        <option value="保养">保养</option>
                        <option value="临时保养">临时保养</option>
                    </select>
                </div>
                <div class="form-group" id="faultSeverityGroup" style="display: none;">
                    <label class="form-label">故障程度 *</label>
                    <select id="faultSeverity" name="faultSeverity" class="form-select">
                        <option value="">请选择故障程度</option>
                        <option value="轻微">轻微</option>
                        <option value="一般">一般</option>
                        <option value="严重">严重</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">日期 *</label>
                    <input type="date" id="recordDate" name="date" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">开始时间 *</label>
                    <input type="time" id="recordStartTime" name="startTime" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">结束时间 *</label>
                    <input type="time" id="recordEndTime" name="endTime" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">操作人 *</label>
                    <input type="text" id="recordOperator" name="operator" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">问题描述/保养内容 *</label>
                    <textarea id="recordDescription" name="description" class="form-input" rows="3" required></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">处理结果</label>
                    <textarea id="recordResult" name="result" class="form-input" rows="3"></textarea>
                </div>
                <div class="form-group">
                    <label class="form-label">审查人员</label>
                    <input type="text" id="recordReviewer" name="reviewer" class="form-input">
                </div>
                <!-- 自定义字段容器 -->
                <div id="customFieldsContainer"></div>
            </form>
        </div>
        <div class="device-modal-footer">
            <button type="button" id="cancelMaintenanceBtn" onclick="window.maintenanceManager.hideRecordModal()" class="btn btn-secondary">取消</button>
            <button type="button" id="saveMaintenanceBtn" class="btn btn-primary">保存记录</button>
        </div>
    </div>
</div>
```

## 2. 样式文件 (CSS)

### 主要样式 - maintenance-management.css
```css
/* 维修保养记录管理页面样式 */

/* 维修保养记录表格容器 - 添加横向滚动 */
#maintenanceRecordsSection .device-table-container {
    background: white;
    border-radius: 0.5rem;
    overflow-x: auto; /* 启用横向滚动 */
    overflow-y: visible;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    /* 确保滚动条可见 */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

/* 维修保养记录表格 */
#maintenanceRecordsSection .device-table {
    width: 100%;
    min-width: 1200px; /* 设置最小宽度，确保所有列都能显示 */
    border-collapse: collapse;
    table-layout: fixed; /* 固定表格布局 */
}

/* 设置各列的固定宽度 */
#maintenanceRecordsSection .device-table th:nth-child(1),
#maintenanceRecordsSection .device-table td:nth-child(1) {
    width: 50px; /* 复选框列 */
    min-width: 50px;
}

#maintenanceRecordsSection .device-table th:nth-child(2),
#maintenanceRecordsSection .device-table td:nth-child(2) {
    width: 120px; /* 记录编号列 */
    min-width: 120px;
}

#maintenanceRecordsSection .device-table th:nth-child(3),
#maintenanceRecordsSection .device-table td:nth-child(3) {
    width: 100px; /* 厂区列 */
    min-width: 100px;
}

#maintenanceRecordsSection .device-table th:nth-child(4),
#maintenanceRecordsSection .device-table td:nth-child(4) {
    width: 150px; /* 设备列 */
    min-width: 150px;
}

#maintenanceRecordsSection .device-table th:nth-child(5),
#maintenanceRecordsSection .device-table td:nth-child(5) {
    width: 80px; /* 类型列 */
    min-width: 80px;
}

#maintenanceRecordsSection .device-table th:nth-child(6),
#maintenanceRecordsSection .device-table td:nth-child(6) {
    width: 100px; /* 日期列 */
    min-width: 100px;
}

#maintenanceRecordsSection .device-table th:nth-child(7),
#maintenanceRecordsSection .device-table td:nth-child(7) {
    width: 100px; /* 操作人列 */
    min-width: 100px;
}

#maintenanceRecordsSection .device-table th:nth-child(8),
#maintenanceRecordsSection .device-table td:nth-child(8) {
    width: 200px; /* 描述列 */
    min-width: 200px;
}

#maintenanceRecordsSection .device-table th:nth-child(9),
#maintenanceRecordsSection .device-table td:nth-child(9) {
    width: 120px; /* 操作列 */
    min-width: 120px;
}

/* 维修保养记录表格头部 */
#maintenanceRecordsSection .device-table th {
    background-color: #f8fafc;
    border-bottom: 2px solid #e2e8f0;
    padding: 0.75rem 0.5rem;
    text-align: left;
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* 维修保养记录表格单元格 */
#maintenanceRecordsSection .device-table td {
    padding: 0.75rem 0.5rem;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.875rem;
    color: #374151;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    vertical-align: middle;
}

/* 记录类型标签样式 */
.record-type-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 0.375rem;
    font-size: 0.75rem;
    font-weight: 500;
    text-align: center;
    min-width: 60px;
}

.record-type-maintenance {
    background-color: #fee2e2;
    color: #dc2626;
}

.record-type-upkeep {
    background-color: #dbeafe;
    color: #2563eb;
}

.record-type-temporary {
    background-color: #d1fae5;
    color: #059669;
}

/* 操作按钮样式 */
.record-action-btn {
    padding: 0.25rem 0.5rem;
    margin: 0 0.125rem;
    border-radius: 0.25rem;
    font-size: 0.75rem;
    font-weight: 500;
    border: none;
    cursor: pointer;
    transition: all 0.2s;
}

.record-action-btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.record-action-btn.view {
    background-color: #3b82f6;
    color: white;
}

.record-action-btn.edit {
    background-color: #10b981;
    color: white;
}

.record-action-btn.delete {
    background-color: #ef4444;
    color: white;
}

/* 响应式设计 */
@media (max-width: 768px) {
    #maintenanceRecordsSection .device-table-container {
        /* 在移动设备上保持横向滚动 */
        overflow-x: auto;
        -webkit-overflow-scrolling: touch;
    }
    
    #maintenanceRecordsSection .device-table {
        min-width: 800px; /* 移动设备上的最小宽度 */
    }
}
```

## 3. JavaScript 核心逻辑

### 主要管理类 - MaintenanceManagement
```javascript
// 维修保养管理前端模块
class MaintenanceManagement {
    constructor() {
        this.records = [];
        this.devices = [];
        this.factories = [];
        this.templates = { maintenance: [], upkeep: [], '临时保养': [] };
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.filters = {};
        this.selectedRecords = new Set();
        this.searchTimeout = null;
        this.isInitialized = false;
        this.isLoading = false; // 防止重复加载
        this.refreshDebounceTimer = null; // 防抖定时器
        this.init();
    }

    // 防抖工具函数
    debounce(func, wait) {
        return (...args) => {
            clearTimeout(this.refreshDebounceTimer);
            this.refreshDebounceTimer = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // 初始化
    async init(recordType = '维修') {
        if (this.isInitialized) {
            return; // 避免重复初始化
        }
        this.recordType = recordType;

        // 初始化权限控制
        this.initPermissions();

        this.bindEvents();

        // 先加载基础数据，再加载记录
        await Promise.all([
            this.loadFactories(),
            this.loadDevices(),
            this.loadTemplates()
        ]);

        // 基础数据加载完成后再加载记录
        // 第一次初始化时不显示全屏加载器，避免页面切换时的闪烁
        this.loadRecords(false);
        this.isInitialized = true;
    }

    // 初始化权限控制
    initPermissions() {
        // 获取当前用户信息
        this.currentUser = sessionStorage.getItem('username');
        this.currentRole = sessionStorage.getItem('role');
        this.currentDepartment = sessionStorage.getItem('department');

        // 根据权限控制按钮显示
        this.updateButtonPermissions();
    }

    // 更新按钮权限
    updateButtonPermissions() {
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';

        // 添加记录按钮
        const addBtn = document.getElementById('addMaintenanceRecordBtn');
        if (addBtn) {
            addBtn.style.display = (isAdmin || isElectricalDept) ? 'flex' : 'none';
        }

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batchDeleteMaintenanceBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.style.display = (isAdmin || isElectricalDept) ? 'flex' : 'none';
        }
    }

    // 绑定事件
    bindEvents() {
        // 添加记录按钮
        const addBtn = document.getElementById('addMaintenanceRecordBtn');
        if (addBtn) {
            addBtn.addEventListener('click', () => this.showAddRecordModal());
        }

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batchDeleteMaintenanceBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', () => this.batchDeleteRecords());
        }

        // 导出按钮
        const exportBtn = document.getElementById('exportMaintenanceBtn');
        if (exportBtn) {
            exportBtn.addEventListener('click', () => this.exportRecords());
        }

        // 刷新按钮
        const refreshBtn = document.getElementById('refreshMaintenanceBtn');
        if (refreshBtn) {
            refreshBtn.addEventListener('click', () => this.loadRecords(true));
        }

        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAllMaintenanceRecords');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => this.toggleSelectAll(e.target.checked));
        }

        // 搜索输入框
        const searchInput = document.getElementById('maintenanceSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
        }

        // 筛选器
        this.bindFilterEvents();

        // 记录类型变化事件
        const recordTypeSelect = document.getElementById('recordType');
        if (recordTypeSelect) {
            recordTypeSelect.addEventListener('change', (e) => this.handleRecordTypeChange(e.target.value));
        }

        // 厂区变化事件
        const factorySelect = document.getElementById('recordFactory');
        if (factorySelect) {
            factorySelect.addEventListener('change', (e) => this.handleFactoryChange(e.target.value));
        }

        // 设备搜索功能
        this.bindDeviceSearchEvents();
    }

    // 绑定筛选器事件
    bindFilterEvents() {
        const filters = [
            'maintenanceTypeFilter',
            'maintenanceFactoryFilter',
            'maintenanceOperatorFilter',
            'maintenanceStartDate',
            'maintenanceEndDate'
        ];

        filters.forEach(filterId => {
            const element = document.getElementById(filterId);
            if (element) {
                element.addEventListener('change', () => this.applyFilters());
            }
        });
    }

    // 绑定设备搜索事件
    bindDeviceSearchEvents() {
        const searchInput = document.getElementById('maintenanceDeviceSearch');
        const resultsDiv = document.getElementById('maintenanceDeviceSearchResults');
        const hiddenInput = document.getElementById('maintenanceDeviceFilter');

        if (!searchInput || !resultsDiv || !hiddenInput) return;

        let searchTimeout;

        searchInput.addEventListener('input', (e) => {
            clearTimeout(searchTimeout);
            const query = e.target.value.trim();

            if (query.length < 1) {
                resultsDiv.style.display = 'none';
                hiddenInput.value = '';
                this.applyFilters();
                return;
            }

            searchTimeout = setTimeout(() => {
                this.searchDevices(query, resultsDiv, searchInput, hiddenInput);
            }, 300);
        });

        // 点击外部关闭搜索结果
        document.addEventListener('click', (e) => {
            if (!searchInput.contains(e.target) && !resultsDiv.contains(e.target)) {
                resultsDiv.style.display = 'none';
            }
        });
    }

    // 加载记录数据
    async loadRecords(showLoadingIndicator = true) {
        if (this.isLoading) {
            return; // 防止重复加载
        }

        try {
            this.isLoading = true;
            console.log('开始加载维修保养记录数据');

            if (showLoadingIndicator) {
                showLoading('正在加载维修/保养记录...');
            }

            const queryParams = new URLSearchParams();
            Object.keys(this.filters).forEach(key => {
                if (this.filters[key]) {
                    queryParams.append(key, this.filters[key]);
                }
            });

            const response = await apiRequest(`/api/maintenance-records?${queryParams.toString()}`);

            if (response.success) {
                this.records = response.records;
                this.updateRecordTable();
                this.updatePagination();
                this.updateSelectedCount();
                console.log('维修保养记录数据加载成功');
            } else {
                showError('加载维修/保养记录失败: ' + response.message);
            }
        } catch (error) {
            console.error('加载维修/保养记录失败:', error);
            showError('加载维修/保养记录失败，请检查网络连接');
        } finally {
            this.isLoading = false;
            if (showLoadingIndicator) {
                hideLoading();
            }
        }
    }

    // 更新记录表格
    updateRecordTable() {
        const tbody = document.getElementById('maintenanceTableBody');
        if (!tbody) return;

        if (this.records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                        暂无维修/保养记录
                    </td>
                </tr>
            `;
            return;
        }

        // 计算分页
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageRecords = this.records.slice(startIndex, endIndex);
        this.totalPages = Math.ceil(this.records.length / this.pageSize);

        // 创建设备映射
        const deviceMap = {};
        this.devices.forEach(device => {
            deviceMap[device.id] = device;
        });

        tbody.innerHTML = pageRecords.map(record => {
            const device = deviceMap[record.deviceId] || {};
            const isSelected = this.selectedRecords.has(record.id);

            // 根据记录类型设置样式
            let typeClass = 'record-type-maintenance';
            if (record.type === '保养') {
                typeClass = 'record-type-upkeep';
            } else if (record.type === '临时保养') {
                typeClass = 'record-type-temporary';
            }

            return `
                <tr class="hover:bg-gray-50">
                    <td>
                        <input type="checkbox"
                               class="form-checkbox record-checkbox"
                               data-record-id="${record.id}"
                               ${isSelected ? 'checked' : ''}>
                    </td>
                    <td class="font-medium">${record.recordCode || '-'}</td>
                    <td>${record.factory || '-'}</td>
                    <td>${device.name || '-'}</td>
                    <td>
                        <span class="record-type-badge ${typeClass}">
                            ${record.type}
                        </span>
                    </td>
                    <td>${record.date || '-'}</td>
                    <td>${record.operator || '-'}</td>
                    <td class="max-w-xs truncate" title="${record.description || '-'}">
                        ${record.description || '-'}
                    </td>
                    <td>
                        <div class="flex space-x-1">
                            <button onclick="window.maintenanceManager.viewRecord('${record.id}')"
                                    class="record-action-btn view" title="查看详情">
                                查看
                            </button>
                            ${this.canEditRecord(record) ? `
                                <button onclick="window.maintenanceManager.editRecord('${record.id}')"
                                        class="record-action-btn edit" title="编辑记录">
                                    编辑
                                </button>
                            ` : ''}
                            ${this.canDeleteRecord(record) ? `
                                <button onclick="window.maintenanceManager.deleteRecord('${record.id}')"
                                        class="record-action-btn delete" title="删除记录">
                                    删除
                                </button>
                            ` : ''}
                        </div>
                    </td>
                </tr>
            `;
        }).join('');

        // 绑定复选框事件
        this.bindCheckboxEvents();
    }

    // 权限检查：是否可以编辑记录
    canEditRecord(record) {
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';
        const isOperator = record.operator === this.currentUser;

        return isAdmin || (isElectricalDept && isOperator);
    }

    // 权限检查：是否可以删除记录
    canDeleteRecord(record) {
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';
        const isOperator = record.operator === this.currentUser;

        return isAdmin || (isElectricalDept && isOperator);
    }

    // 绑定复选框事件
    bindCheckboxEvents() {
        const checkboxes = document.querySelectorAll('.record-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const recordId = e.target.dataset.recordId;
                if (e.target.checked) {
                    this.selectedRecords.add(recordId);
                } else {
                    this.selectedRecords.delete(recordId);
                }
                this.updateSelectedCount();
                this.updateSelectAllCheckbox();
            });
        });
    }

    // 更新选中计数
    updateSelectedCount() {
        const countElement = document.getElementById('selectedRecordCount');
        if (!countElement) return;

        const count = this.selectedRecords.size;
        if (count > 0) {
            countElement.textContent = `已选择 ${count} 条记录`;
            countElement.classList.remove('hidden');
        } else {
            countElement.classList.add('hidden');
        }
    }

    // 更新全选复选框状态
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAllMaintenanceRecords');
        if (!selectAllCheckbox) return;

        const checkboxes = document.querySelectorAll('.record-checkbox');
        const checkedCount = this.selectedRecords.size;
        const totalCount = checkboxes.length;

        if (checkedCount === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (checkedCount === totalCount) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    // 显示添加记录模态框
    showAddRecordModal() {
        // 权限检查：只有管理员和机电部用户可以添加记录
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';

        if (!isAdmin && !isElectricalDept) {
            showError('权限不足，只有管理员和机电部用户可以添加维修保养记录');
            return;
        }

        this.showRecordModal();
    }

    // 查看记录详情
    async viewRecord(recordId) {
        try {
            const response = await apiRequest(`/api/maintenance-records/${recordId}`);
            if (response.success) {
                this.showRecordModal(response.record, true);
            } else {
                showError('获取记录详情失败: ' + response.message);
            }
        } catch (error) {
            console.error('获取记录详情失败:', error);
            showError('获取记录详情失败');
        }
    }

    // 编辑记录
    async editRecord(recordId) {
        try {
            const response = await apiRequest(`/api/maintenance-records/${recordId}`);
            if (response.success) {
                const record = response.record;

                // 权限检查：管理员可以编辑所有记录，机电部用户只能编辑自己创建的记录
                const isAdmin = this.currentRole === 'admin';
                const isElectricalDept = this.currentDepartment === '机电部';
                const isOperator = record.operator === this.currentUser;

                if (!isAdmin && (!isElectricalDept || !isOperator)) {
                    if (!isElectricalDept) {
                        showError('权限不足，只有管理员和机电部用户可以编辑维修保养记录');
                    } else {
                        showError('权限不足，您只能编辑自己创建的记录');
                    }
                    return;
                }

                this.showRecordModal(record, false);
            } else {
                showError('获取记录信息失败: ' + response.message);
            }
        } catch (error) {
            console.error('获取记录信息失败:', error);
            showError('获取记录信息失败');
        }
    }

    // 删除记录
    async deleteRecord(recordId) {
        if (!confirm('确定要删除这条维修/保养记录吗？此操作不可撤销。')) {
            return;
        }

        try {
            const response = await apiRequest(`/api/maintenance-records/${recordId}`, {
                method: 'DELETE',
                data: {
                    username: this.currentUser,
                    role: this.currentRole,
                    department: this.currentDepartment
                }
            });

            if (response.success) {
                showSuccess('记录删除成功');
                this.loadRecords(true);
                this.selectedRecords.delete(recordId);
                this.updateSelectedCount();
            } else {
                showError('删除记录失败: ' + response.message);
            }
        } catch (error) {
            console.error('删除记录失败:', error);
            showError('删除记录失败');
        }
    }

    // 批量删除记录
    async batchDeleteRecords() {
        if (this.selectedRecords.size === 0) {
            showError('请先选择要删除的记录');
            return;
        }

        if (!confirm(`确定要删除选中的 ${this.selectedRecords.size} 条记录吗？此操作不可撤销。`)) {
            return;
        }

        try {
            showLoading('正在删除记录...');

            const deletePromises = Array.from(this.selectedRecords).map(recordId =>
                apiRequest(`/api/maintenance-records/${recordId}`, {
                    method: 'DELETE',
                    data: {
                        username: this.currentUser,
                        role: this.currentRole,
                        department: this.currentDepartment
                    }
                })
            );

            const results = await Promise.allSettled(deletePromises);
            const successCount = results.filter(result => result.status === 'fulfilled' && result.value.success).length;
            const failCount = this.selectedRecords.size - successCount;

            if (successCount > 0) {
                showSuccess(`成功删除 ${successCount} 条记录${failCount > 0 ? `，${failCount} 条记录删除失败` : ''}`);
                this.selectedRecords.clear();
                this.loadRecords(true);
            } else {
                showError('批量删除失败');
            }
        } catch (error) {
            console.error('批量删除失败:', error);
            showError('批量删除失败');
        } finally {
            hideLoading();
        }
    }

    // 显示记录模态框
    showRecordModal(record = null, readonly = false) {
        const modal = document.getElementById('maintenanceModal');
        const form = document.getElementById('maintenanceForm');
        const title = document.getElementById('maintenanceModalTitle');

        if (!modal || !form || !title) return;

        // 设置标题
        if (readonly) {
            title.textContent = '记录详情';
        } else if (record) {
            title.textContent = '编辑记录';
        } else {
            title.textContent = '添加记录';
        }

        // 重置表单
        form.reset();

        // 填充表单数据
        if (record) {
            // 设置记录编号
            document.getElementById('recordCode').value = record.recordCode || '';

            // 设置厂区
            const factorySelect = document.getElementById('recordFactory');
            if (factorySelect) {
                factorySelect.value = record.factory || '';
            }

            // 设置位置
            document.getElementById('recordLocation').value = record.location || '';

            // 设置设备
            const deviceSelect = document.getElementById('recordDevice');
            if (deviceSelect) {
                deviceSelect.value = record.deviceId || '';
            }

            // 设置记录类型
            document.getElementById('recordType').value = record.type || '维修';

            // 设置故障程度（如果是维修记录）
            if (record.type === '维修' && record.faultSeverity) {
                document.getElementById('faultSeverity').value = record.faultSeverity;
                document.getElementById('faultSeverityGroup').style.display = 'block';
            }

            // 设置日期和时间
            document.getElementById('recordDate').value = record.date || '';
            document.getElementById('recordStartTime').value = record.startTime || '';
            document.getElementById('recordEndTime').value = record.endTime || '';

            // 设置操作人员字段
            const operatorField = document.getElementById('recordOperator');
            if (operatorField) {
                operatorField.value = record.operator || '';
                // 如果是编辑模式且不是管理员，操作人员字段应该只读
                if (!readonly && this.currentRole !== 'admin') {
                    operatorField.readOnly = true;
                    operatorField.style.backgroundColor = '#f3f4f6';
                }
            }

            // 设置描述和结果
            document.getElementById('recordDescription').value = record.description || '';
            document.getElementById('recordResult').value = record.result || '';

            // 设置审查人员
            const reviewerField = document.getElementById('recordReviewer');
            if (reviewerField) {
                reviewerField.value = record.reviewer || '';
            }

            // 处理自定义字段
            if (record.customFields) {
                this.populateCustomFields(record.customFields);
            }
        } else {
            // 新建记录时设置默认值
            const operatorField = document.getElementById('recordOperator');
            if (operatorField) {
                operatorField.value = this.currentUser;
                operatorField.readOnly = this.currentRole !== 'admin';
                if (operatorField.readOnly) {
                    operatorField.style.backgroundColor = '#f3f4f6';
                }
            }

            // 设置默认日期为今天
            const today = new Date().toISOString().split('T')[0];
            document.getElementById('recordDate').value = today;
        }

        // 设置表单字段的只读状态
        if (readonly) {
            const inputs = form.querySelectorAll('input, select, textarea');
            inputs.forEach(input => {
                input.disabled = true;
            });
        }

        // 设置按钮状态
        const saveBtn = document.getElementById('saveMaintenanceBtn');
        const cancelBtn = document.getElementById('cancelMaintenanceBtn');

        if (saveBtn) {
            saveBtn.style.display = readonly ? 'none' : 'block';
            saveBtn.textContent = record ? '更新记录' : '添加记录';
        }

        if (cancelBtn) {
            cancelBtn.textContent = readonly ? '关闭' : '取消';
        }

        // 绑定保存事件
        if (!readonly) {
            const newSaveBtn = saveBtn.cloneNode(true);
            saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

            newSaveBtn.addEventListener('click', () => {
                this.saveRecord(record ? record.id : null);
            });
        }

        // 显示模态框
        modal.classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 隐藏记录模态框
    hideRecordModal() {
        const modal = document.getElementById('maintenanceModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.classList.remove('modal-open');
        }
    }

    // 保存记录
    async saveRecord(recordId = null) {
        const form = document.getElementById('maintenanceForm');
        if (!form) return;

        const formData = new FormData(form);

        // 收集自定义字段数据
        const customFields = {};
        const customInputs = form.querySelectorAll('[name^="custom_"]');
        customInputs.forEach(input => {
            const fieldName = input.name.replace('custom_', '');
            customFields[fieldName] = input.value;
        });

        const recordData = {
            recordCode: formData.get('recordCode'),
            factory: formData.get('factory'),
            location: formData.get('location'),
            deviceId: formData.get('deviceId'),
            type: formData.get('type'),
            faultSeverity: formData.get('faultSeverity'), // 添加故障程度字段
            date: formData.get('date'),
            startTime: formData.get('startTime'),
            endTime: formData.get('endTime'),
            operator: formData.get('operator'),
            description: formData.get('description'),
            result: formData.get('result'),
            reviewer: formData.get('reviewer'), // 添加审查人员字段
            customFields: customFields
        };

        // 验证必填字段
        if (!recordData.deviceId || !recordData.type || !recordData.date ||
            !recordData.startTime || !recordData.endTime ||
            !recordData.operator || !recordData.description) {
            showError('请填写所有必填字段');
            return;
        }

        // 验证时间逻辑
        if (recordData.startTime >= recordData.endTime) {
            showError('结束时间必须晚于开始时间');
            return;
        }

        // 如果是维修记录，验证故障程度
        if (recordData.type === '维修' && !recordData.faultSeverity) {
            showError('维修记录必须选择故障程度');
            return;
        }

        try {
            showLoading(recordId ? '正在更新记录...' : '正在添加记录...');

            // 添加用户信息用于权限验证
            recordData.username = this.currentUser;
            recordData.role = this.currentRole;
            recordData.department = this.currentDepartment;

            const url = recordId ? `/api/maintenance-records/${recordId}` : '/api/maintenance-records';
            const method = recordId ? 'PUT' : 'POST';

            const response = await apiRequest(url, {
                method: method,
                data: recordData
            });

            if (response.success) {
                showSuccess(recordId ? '记录更新成功' : '记录添加成功');
                this.hideRecordModal();
                this.loadRecords(true);
            } else {
                showError((recordId ? '更新记录失败: ' : '添加记录失败: ') + response.message);
            }
        } catch (error) {
            console.error('保存记录失败:', error);
            showError('保存记录失败');
        } finally {
            hideLoading();
        }
    }

    // 处理记录类型变化
    handleRecordTypeChange(type) {
        const faultSeverityGroup = document.getElementById('faultSeverityGroup');
        if (faultSeverityGroup) {
            // 只有维修记录才显示故障程度选择
            faultSeverityGroup.style.display = type === '维修' ? 'block' : 'none';

            // 如果不是维修记录，清空故障程度值
            if (type !== '维修') {
                const faultSeveritySelect = document.getElementById('faultSeverity');
                if (faultSeveritySelect) {
                    faultSeveritySelect.value = '';
                }
            }
        }

        // 根据类型加载相应的模板
        this.loadTemplateFields(type);
    }

    // 处理厂区变化
    async handleFactoryChange(factoryId) {
        if (!factoryId) {
            document.getElementById('recordCode').value = '';
            return;
        }

        // 生成记录编号
        await this.generateRecordCode(factoryId);

        // 根据厂区筛选设备
        this.updateDeviceOptions(factoryId);
    }

    // 生成记录编号
    async generateRecordCode(factoryId) {
        try {
            const type = document.getElementById('recordType').value;
            const response = await apiRequest('/api/maintenance-records/generate-code', {
                method: 'POST',
                data: { factoryId, type }
            });

            if (response.success) {
                document.getElementById('recordCode').value = response.recordCode;
            }
        } catch (error) {
            console.error('生成记录编号失败:', error);
        }
    }

    // 更新设备选项
    updateDeviceOptions(factoryId) {
        const deviceSelect = document.getElementById('recordDevice');
        if (!deviceSelect) return;

        // 清空现有选项
        deviceSelect.innerHTML = '<option value="">请选择设备</option>';

        // 根据厂区筛选设备
        const filteredDevices = this.devices.filter(device =>
            !factoryId || device.factory === factoryId
        );

        filteredDevices.forEach(device => {
            const option = document.createElement('option');
            option.value = device.id;
            option.textContent = `${device.name} (${device.model || '未知型号'})`;
            deviceSelect.appendChild(option);
        });
    }

    // 加载模板字段
    async loadTemplateFields(type) {
        const container = document.getElementById('customFieldsContainer');
        if (!container) return;

        // 清空现有自定义字段
        container.innerHTML = '';

        const templates = this.templates[type] || [];
        if (templates.length === 0) return;

        // 创建自定义字段
        templates.forEach(template => {
            const fieldDiv = document.createElement('div');
            fieldDiv.className = 'form-group';

            const label = document.createElement('label');
            label.className = 'form-label';
            label.textContent = template.label + (template.required ? ' *' : '');

            let input;
            if (template.type === 'textarea') {
                input = document.createElement('textarea');
                input.rows = 3;
            } else if (template.type === 'select') {
                input = document.createElement('select');
                template.options.forEach(option => {
                    const optionElement = document.createElement('option');
                    optionElement.value = option;
                    optionElement.textContent = option;
                    input.appendChild(optionElement);
                });
            } else {
                input = document.createElement('input');
                input.type = template.type || 'text';
            }

            input.className = 'form-input';
            input.name = `custom_${template.name}`;
            input.placeholder = template.placeholder || '';
            if (template.required) {
                input.required = true;
            }

            fieldDiv.appendChild(label);
            fieldDiv.appendChild(input);
            container.appendChild(fieldDiv);
        });
    }

    // 填充自定义字段
    populateCustomFields(customFields) {
        Object.keys(customFields).forEach(fieldName => {
            const input = document.querySelector(`[name="custom_${fieldName}"]`);
            if (input) {
                input.value = customFields[fieldName];
            }
        });
    }

    // 应用筛选器
    applyFilters() {
        this.filters = {
            search: document.getElementById('maintenanceSearchInput')?.value || '',
            type: document.getElementById('maintenanceTypeFilter')?.value || '',
            factory: document.getElementById('maintenanceFactoryFilter')?.value || '',
            deviceId: document.getElementById('maintenanceDeviceFilter')?.value || '',
            operator: document.getElementById('maintenanceOperatorFilter')?.value || '',
            startDate: document.getElementById('maintenanceStartDate')?.value || '',
            endDate: document.getElementById('maintenanceEndDate')?.value || ''
        };

        this.currentPage = 1; // 重置到第一页
        this.loadRecords(true);
    }

    // 处理搜索
    handleSearch(query) {
        clearTimeout(this.searchTimeout);
        this.searchTimeout = setTimeout(() => {
            this.filters.search = query;
            this.currentPage = 1;
            this.loadRecords(true);
        }, 300);
    }

    // 全选/取消全选
    toggleSelectAll(checked) {
        const checkboxes = document.querySelectorAll('.record-checkbox');
        checkboxes.forEach(checkbox => {
            checkbox.checked = checked;
            const recordId = checkbox.dataset.recordId;
            if (checked) {
                this.selectedRecords.add(recordId);
            } else {
                this.selectedRecords.delete(recordId);
            }
        });
        this.updateSelectedCount();
    }

    // 导出记录
    exportRecords() {
        if (window.exportManager) {
            window.exportManager.exportMaintenanceRecords();
        } else {
            showError('导出功能暂不可用');
        }
    }
}

// 初始化函数
function initMaintenanceManagement() {
    return new MaintenanceManagement();
}

// 全局暴露
window.initMaintenanceManagement = initMaintenanceManagement;
```

## 4. 后端API接口 (Node.js)

### 主要API路由
```javascript
// ==================== 维修保养记录API接口 ====================

// 获取维修保养记录列表
app.get('/api/maintenance-records', (req, res) => {
    try {
        const filters = {
            deviceId: req.query.deviceId,
            factory: req.query.factory,
            type: req.query.type,
            operator: req.query.operator,
            startDate: req.query.startDate,
            endDate: req.query.endDate,
            search: req.query.search
        };

        const records = maintenanceManager.getAllRecords(filters);
        res.json({ success: true, records });
    } catch (error) {
        console.error('获取维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '获取维修保养记录失败' });
    }
});

// 获取单个维修保养记录
app.get('/api/maintenance-records/:id', (req, res) => {
    try {
        const recordId = req.params.id;
        const record = maintenanceManager.getRecordById(recordId);

        if (record) {
            res.json({ success: true, record });
        } else {
            res.status(404).json({ success: false, message: '记录不存在' });
        }
    } catch (error) {
        console.error('获取维修保养记录详情失败:', error);
        res.status(500).json({ success: false, message: '获取记录详情失败' });
    }
});

// 添加维修保养记录
app.post('/api/maintenance-records', upload.array('attachments', 10), (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员和机电部用户可以添加维修保养记录
        if (role !== 'admin' && department !== '机电部') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员和机电部用户可以添加维修保养记录' });
        }

        const recordData = {
            ...req.body,
            operator: username, // 设置操作人员为当前登录用户
            attachments: req.files ? req.files.map(file => ({
                filename: file.filename,
                originalname: file.originalname,
                size: file.size,
                uploadTime: new Date().toISOString()
            })) : []
        };

        const result = maintenanceManager.addRecord(recordData);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('添加维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '添加维修保养记录失败' });
    }
});

// 更新维修保养记录
app.put('/api/maintenance-records/:id', upload.array('attachments', 10), (req, res) => {
    try {
        const { username, role, department } = req.body;
        const recordId = req.params.id;

        // 获取现有记录以检查权限
        const existingRecord = maintenanceManager.getRecordById(recordId);
        if (!existingRecord) {
            return res.status(404).json({ success: false, message: '记录不存在' });
        }

        // 权限检查：管理员可以修改所有记录，机电部用户只能修改自己创建的记录
        if (role !== 'admin') {
            if (department !== '机电部') {
                return res.status(403).json({ success: false, message: '权限不足，只有管理员和机电部用户可以修改维修保养记录' });
            }
            if (existingRecord.operator !== username) {
                return res.status(403).json({ success: false, message: '权限不足，您只能修改自己创建的记录' });
            }
        }

        const recordData = {
            ...req.body,
            id: recordId,
            attachments: req.files ? req.files.map(file => ({
                filename: file.filename,
                originalname: file.originalname,
                size: file.size,
                uploadTime: new Date().toISOString()
            })) : existingRecord.attachments || []
        };

        const result = maintenanceManager.updateRecord(recordId, recordData);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '更新维修保养记录失败' });
    }
});

// 删除维修保养记录
app.delete('/api/maintenance-records/:id', (req, res) => {
    try {
        const { username, role, department } = req.body;
        const recordId = req.params.id;

        // 获取现有记录以检查权限
        const existingRecord = maintenanceManager.getRecordById(recordId);
        if (!existingRecord) {
            return res.status(404).json({ success: false, message: '记录不存在' });
        }

        // 权限检查：管理员可以删除所有记录，机电部用户只能删除自己创建的记录
        if (role !== 'admin') {
            if (department !== '机电部') {
                return res.status(403).json({ success: false, message: '权限不足，只有管理员和机电部用户可以删除维修保养记录' });
            }
            if (existingRecord.operator !== username) {
                return res.status(403).json({ success: false, message: '权限不足，您只能删除自己创建的记录' });
            }
        }

        const result = maintenanceManager.deleteRecord(recordId);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('删除维修保养记录失败:', error);
        res.status(500).json({ success: false, message: '删除维修保养记录失败' });
    }
});

// 生成维修保养记录编号
app.post('/api/maintenance-records/generate-code', (req, res) => {
    try {
        const { factoryId, type } = req.body;

        if (!factoryId || !type) {
            return res.status(400).json({ success: false, message: '厂区ID和记录类型为必填项' });
        }

        const recordCode = maintenanceManager.generateRecordCode(factoryId, type);
        res.json({ success: true, recordCode });
    } catch (error) {
        console.error('生成记录编号失败:', error);
        res.status(500).json({ success: false, message: '生成记录编号失败' });
    }
});

// 获取维修保养记录统计信息
app.get('/api/maintenance-records/stats', (req, res) => {
    try {
        const stats = maintenanceManager.getRecordStats();
        res.json({ success: true, stats });
    } catch (error) {
        console.error('获取记录统计失败:', error);
        res.status(500).json({ success: false, message: '获取记录统计失败' });
    }
});
```

## 5. 数据管理类 (MaintenanceManagement)

### 后端数据管理
```javascript
const fs = require('fs');
const path = require('path');
const DataAccess = require('../../data-access');

// 维修保养管理模块
class MaintenanceManagement {
    constructor() {
        this.dataAccess = new DataAccess();
        // 保留文件路径用于向后兼容，但不再使用
        this.recordsDir = path.join(__dirname, '..', 'data', 'maintenance-records');
        this.templatesFile = path.join(__dirname, '..', 'data', 'maintenance-templates.json');
        this.cache = {
            records: null,
            lastRecordUpdate: 0,
            cacheTTL: 30000 // 30秒缓存
        };
    }

    // 获取所有记录（带筛选）
    getAllRecords(filters = {}) {
        try {
            let records = this.getRecords();

            // 应用筛选器
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                records = records.filter(record =>
                    (record.recordCode && record.recordCode.toLowerCase().includes(searchTerm)) ||
                    (record.description && record.description.toLowerCase().includes(searchTerm)) ||
                    (record.operator && record.operator.toLowerCase().includes(searchTerm))
                );
            }

            if (filters.type) {
                records = records.filter(record => record.type === filters.type);
            }

            if (filters.factory) {
                records = records.filter(record => record.factory === filters.factory);
            }

            if (filters.deviceId) {
                records = records.filter(record => record.deviceId === filters.deviceId);
            }

            if (filters.operator) {
                const operatorTerm = filters.operator.toLowerCase();
                records = records.filter(record =>
                    record.operator && record.operator.toLowerCase().includes(operatorTerm)
                );
            }

            if (filters.startDate) {
                records = records.filter(record => record.date >= filters.startDate);
            }

            if (filters.endDate) {
                records = records.filter(record => record.date <= filters.endDate);
            }

            // 按日期降序排序
            records.sort((a, b) => new Date(b.date) - new Date(a.date));

            return records;
        } catch (error) {
            console.error('获取维修保养记录失败:', error);
            return [];
        }
    }

    // 从SQLite数据库中读取所有记录数据（保持接口兼容性）
    getAllRecordsFromFiles() {
        try {
            return this.dataAccess.getMaintenanceRecords();
        } catch (error) {
            console.error('读取记录数据失败:', error);
            return [];
        }
    }

    // 读取记录数据（带缓存）
    getRecords() {
        const now = Date.now();
        if (this.cache.records && (now - this.cache.lastRecordUpdate < this.cache.cacheTTL)) {
            return this.cache.records;
        }

        try {
            const records = this.dataAccess.getMaintenanceRecords();

            this.cache.records = records;
            this.cache.lastRecordUpdate = now;
            return this.cache.records;
        } catch (error) {
            console.error('读取记录数据失败:', error);
            return [];
        }
    }

    // 根据ID获取记录
    getRecordById(recordId) {
        try {
            return this.dataAccess.getMaintenanceRecordById(recordId);
        } catch (error) {
            console.error('获取记录详情失败:', error);
            return null;
        }
    }

    // 添加记录
    addRecord(recordData) {
        try {
            // 生成记录ID
            recordData.id = this.generateRecordId();

            // 生成记录编号（如果没有提供）
            if (!recordData.recordCode && recordData.factory && recordData.type) {
                recordData.recordCode = this.generateRecordCode(recordData.factory, recordData.type);
            }

            // 设置创建时间
            recordData.createdAt = new Date().toISOString();
            recordData.updatedAt = recordData.createdAt;

            // 验证必填字段
            const requiredFields = ['deviceId', 'type', 'date', 'startTime', 'endTime', 'operator', 'description'];
            for (const field of requiredFields) {
                if (!recordData[field]) {
                    return { success: false, message: `缺少必填字段: ${field}` };
                }
            }

            // 验证时间逻辑
            if (recordData.startTime >= recordData.endTime) {
                return { success: false, message: '结束时间必须晚于开始时间' };
            }

            // 如果是维修记录，验证故障程度
            if (recordData.type === '维修' && !recordData.faultSeverity) {
                return { success: false, message: '维修记录必须选择故障程度' };
            }

            // 保存到数据库
            this.dataAccess.addMaintenanceRecord(recordData);

            // 清除缓存
            this.cache.records = null;
            this.cache.lastRecordUpdate = 0;

            return { success: true, message: '记录添加成功', record: recordData };
        } catch (error) {
            console.error('添加维修保养记录失败:', error);
            return { success: false, message: '添加记录失败: ' + error.message };
        }
    }

    // 更新记录
    updateRecord(recordId, recordData) {
        try {
            // 验证记录是否存在
            const existingRecord = this.getRecordById(recordId);
            if (!existingRecord) {
                return { success: false, message: '记录不存在' };
            }

            // 设置更新时间
            recordData.updatedAt = new Date().toISOString();
            recordData.id = recordId;

            // 验证必填字段
            const requiredFields = ['deviceId', 'type', 'date', 'startTime', 'endTime', 'operator', 'description'];
            for (const field of requiredFields) {
                if (!recordData[field]) {
                    return { success: false, message: `缺少必填字段: ${field}` };
                }
            }

            // 验证时间逻辑
            if (recordData.startTime >= recordData.endTime) {
                return { success: false, message: '结束时间必须晚于开始时间' };
            }

            // 如果是维修记录，验证故障程度
            if (recordData.type === '维修' && !recordData.faultSeverity) {
                return { success: false, message: '维修记录必须选择故障程度' };
            }

            // 更新数据库
            this.dataAccess.updateMaintenanceRecord(recordId, recordData);

            // 清除缓存
            this.cache.records = null;
            this.cache.lastRecordUpdate = 0;

            return { success: true, message: '记录更新成功', record: recordData };
        } catch (error) {
            console.error('更新维修保养记录失败:', error);
            return { success: false, message: '更新记录失败: ' + error.message };
        }
    }

    // 删除记录
    deleteRecord(recordId) {
        try {
            // 验证记录是否存在
            const existingRecord = this.getRecordById(recordId);
            if (!existingRecord) {
                return { success: false, message: '记录不存在' };
            }

            // 从数据库删除
            this.dataAccess.deleteMaintenanceRecord(recordId);

            // 清除缓存
            this.cache.records = null;
            this.cache.lastRecordUpdate = 0;

            return { success: true, message: '记录删除成功' };
        } catch (error) {
            console.error('删除维修保养记录失败:', error);
            return { success: false, message: '删除记录失败: ' + error.message };
        }
    }

    // 生成记录ID
    generateRecordId() {
        return 'MR_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
    }

    // 生成记录编号
    generateRecordCode(factoryId, type) {
        try {
            const now = new Date();
            const year = now.getFullYear();
            const month = String(now.getMonth() + 1).padStart(2, '0');
            const day = String(now.getDate()).padStart(2, '0');

            // 根据类型确定前缀
            let prefix = 'MR'; // 默认维修记录
            if (type === '保养') {
                prefix = 'UR'; // Upkeep Record
            } else if (type === '临时保养') {
                prefix = 'TR'; // Temporary Record
            }

            // 获取当天同类型记录数量
            const records = this.getAllRecords({
                type: type,
                startDate: `${year}-${month}-${day}`,
                endDate: `${year}-${month}-${day}`
            });

            const sequence = String(records.length + 1).padStart(3, '0');

            return `${prefix}-${factoryId}-${year}${month}${day}-${sequence}`;
        } catch (error) {
            console.error('生成记录编号失败:', error);
            return `MR-${factoryId}-${Date.now()}`;
        }
    }

    // 获取记录统计信息
    getRecordStats() {
        try {
            const records = this.getRecords();
            const now = new Date();
            const thisMonth = new Date(now.getFullYear(), now.getMonth(), 1);
            const lastMonth = new Date(now.getFullYear(), now.getMonth() - 1, 1);

            const stats = {
                total: records.length,
                thisMonth: records.filter(r => new Date(r.date) >= thisMonth).length,
                lastMonth: records.filter(r => new Date(r.date) >= lastMonth && new Date(r.date) < thisMonth).length,
                byType: {
                    维修: records.filter(r => r.type === '维修').length,
                    保养: records.filter(r => r.type === '保养').length,
                    临时保养: records.filter(r => r.type === '临时保养').length
                },
                recentRecords: records.slice(0, 5) // 最近5条记录
            };

            return stats;
        } catch (error) {
            console.error('获取记录统计失败:', error);
            return {
                total: 0,
                thisMonth: 0,
                lastMonth: 0,
                byType: { 维修: 0, 保养: 0, 临时保养: 0 },
                recentRecords: []
            };
        }
    }

    // 获取模板数据
    getTemplates() {
        try {
            return this.dataAccess.getMaintenanceTemplates();
        } catch (error) {
            console.error('读取模板数据失败:', error);
            return { maintenance: [], upkeep: [], '临时保养': [] };
        }
    }

    // 保存模板数据
    saveTemplates(templates) {
        try {
            this.dataAccess.updateMaintenanceTemplates(templates);
            return true;
        } catch (error) {
            console.error('保存模板数据失败:', error);
            return false;
        }
    }
}

module.exports = MaintenanceManagement;
```

## 6. 数据库表结构

### 维修保养记录表 (maintenance_records)
```sql
CREATE TABLE IF NOT EXISTS maintenance_records (
    id TEXT PRIMARY KEY,
    recordCode TEXT UNIQUE,
    factory TEXT NOT NULL,
    location TEXT,
    deviceId TEXT NOT NULL,
    type TEXT NOT NULL CHECK (type IN ('维修', '保养', '临时保养')),
    faultSeverity TEXT CHECK (faultSeverity IN ('轻微', '一般', '严重')),
    date TEXT NOT NULL,
    startTime TEXT NOT NULL,
    endTime TEXT NOT NULL,
    operator TEXT NOT NULL,
    description TEXT NOT NULL,
    result TEXT,
    reviewer TEXT,
    customFields TEXT, -- JSON格式存储自定义字段
    attachments TEXT, -- JSON格式存储附件信息
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL,
    FOREIGN KEY (deviceId) REFERENCES devices (id)
);

-- 创建索引提高查询性能
CREATE INDEX IF NOT EXISTS idx_maintenance_records_date ON maintenance_records (date);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_type ON maintenance_records (type);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_device ON maintenance_records (deviceId);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_operator ON maintenance_records (operator);
CREATE INDEX IF NOT EXISTS idx_maintenance_records_factory ON maintenance_records (factory);
```

### 维修保养模板表 (maintenance_templates)
```sql
CREATE TABLE IF NOT EXISTS maintenance_templates (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    type TEXT NOT NULL CHECK (type IN ('维修', '保养', '临时保养')),
    name TEXT NOT NULL,
    label TEXT NOT NULL,
    fieldType TEXT NOT NULL,
    placeholder TEXT,
    required INTEGER DEFAULT 0,
    options TEXT, -- JSON格式存储选项
    createdAt TEXT NOT NULL,
    updatedAt TEXT NOT NULL
);
```



## 8. 功能特性说明

### 核心功能
1. **记录管理**
   - 添加维修/保养/临时保养记录
   - 编辑和删除记录（权限控制）
   - 查看记录详情
   - 批量删除记录

2. **智能筛选**
   - 按记录类型筛选
   - 按厂区筛选
   - 按设备筛选（支持搜索）
   - 按操作人筛选
   - 按日期范围筛选
   - 全文搜索

3. **动态表单**
   - 根据记录类型显示不同字段
   - 维修记录显示故障程度选择
   - 自定义字段支持
   - 表单验证

4. **权限控制**
   - 基于角色和部门的权限控制
   - 操作人员只能编辑自己的记录
   - 管理员拥有全部权限

5. **数据导出**
   - 支持Excel格式导出
   - 可按条件筛选导出
   - 包含完整记录信息

### 技术特性
1. **响应式设计**
   - 支持桌面和移动设备
   - 表格横向滚动
   - 自适应布局

2. **性能优化**
   - 数据缓存机制
   - 分页显示
   - 防抖搜索
   - 懒加载

3. **用户体验**
   - 实时搜索
   - 批量操作
   - 状态反馈
   - 错误处理

4. **数据安全**
   - 权限验证
   - 数据验证
   - SQL注入防护
   - 操作日志

## 9. 部署和配置

### 前端配置
1. 将HTML代码添加到主页面
2. 引入CSS样式文件
3. 引入JavaScript文件
4. 配置路由和导航

### 后端配置
1. 安装依赖包
2. 配置数据库连接
3. 添加API路由
4. 配置文件上传
5. 设置权限中间件

### 数据库初始化
1. 创建数据表
2. 创建索引
3. 插入初始数据
4. 配置备份策略

### 使用说明
1. **添加记录**：点击"添加记录"按钮，填写表单信息
2. **编辑记录**：点击记录行的"编辑"按钮
3. **删除记录**：点击"删除"按钮或使用批量删除
4. **筛选记录**：使用顶部筛选器进行条件筛选
5. **导出数据**：点击"导出记录"按钮下载Excel文件

这个完整的维修保养记录页面实现包含了前端界面、后端API、数据管理、权限控制等所有核心功能，可以直接用于生产环境。
```
