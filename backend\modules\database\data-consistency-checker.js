const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * 数据一致性检查器
 * 确保文件存储和数据库存储的数据同步
 */
class DataConsistencyChecker {
    constructor(dataAccess, logger) {
        this.dataAccess = dataAccess;
        this.logger = logger;
        this.uploadsDir = path.join(__dirname, '../../uploads');
        this.archiveDir = path.join(__dirname, '../../archive');
        
        // 检查结果统计
        this.stats = {
            totalChecks: 0,
            inconsistencies: 0,
            orphanedFiles: 0,
            missingFiles: 0,
            lastCheckTime: null
        };
    }

    /**
     * 执行完整的数据一致性检查
     */
    async performFullConsistencyCheck() {
        this.logger.info('开始执行数据一致性检查');
        const startTime = Date.now();
        
        try {
            const results = {
                attachmentConsistency: await this.checkAttachmentConsistency(),
                fileIntegrity: await this.checkFileIntegrity(),
                databaseIntegrity: await this.checkDatabaseIntegrity(),
                orphanedFiles: await this.findOrphanedFiles(),
                missingFiles: await this.findMissingFiles()
            };

            // 更新统计信息
            this.updateStats(results);
            
            const duration = Date.now() - startTime;
            this.logger.info(`数据一致性检查完成，耗时: ${duration}ms`);
            
            return {
                success: true,
                results,
                stats: this.stats,
                duration
            };
        } catch (error) {
            this.logger.error('数据一致性检查失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                stats: this.stats
            };
        }
    }

    /**
     * 检查附件一致性
     */
    async checkAttachmentConsistency() {
        const inconsistencies = [];
        
        try {
            // 获取所有申请记录
            const applications = this.dataAccess.getApplications();
            
            for (const app of applications) {
                if (app.attachments && app.attachments.length > 0) {
                    for (const attachment of app.attachments) {
                        const filePath = path.join(this.uploadsDir, attachment.path);
                        
                        // 检查文件是否存在
                        if (!fs.existsSync(filePath)) {
                            inconsistencies.push({
                                type: 'missing_file',
                                applicationId: app.id,
                                attachmentPath: attachment.path,
                                message: '数据库中记录的附件文件不存在'
                            });
                        } else {
                            // 检查文件大小是否匹配
                            const stats = fs.statSync(filePath);
                            if (attachment.size && stats.size !== attachment.size) {
                                inconsistencies.push({
                                    type: 'size_mismatch',
                                    applicationId: app.id,
                                    attachmentPath: attachment.path,
                                    expectedSize: attachment.size,
                                    actualSize: stats.size,
                                    message: '附件文件大小不匹配'
                                });
                            }
                        }
                    }
                }
            }
            
            return {
                checked: applications.length,
                inconsistencies: inconsistencies.length,
                details: inconsistencies
            };
        } catch (error) {
            this.logger.error('检查附件一致性失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 检查文件完整性
     */
    async checkFileIntegrity() {
        const corruptedFiles = [];
        
        try {
            if (!fs.existsSync(this.uploadsDir)) {
                return { checked: 0, corrupted: 0, details: [] };
            }

            const files = this.getAllFiles(this.uploadsDir);
            
            for (const filePath of files) {
                try {
                    // 尝试读取文件头部来检查文件是否损坏
                    const buffer = fs.readFileSync(filePath, { start: 0, end: 1023 });
                    
                    // 检查文件是否为空
                    if (buffer.length === 0) {
                        corruptedFiles.push({
                            path: filePath,
                            issue: 'empty_file',
                            message: '文件为空'
                        });
                    }
                    
                    // 计算文件哈希用于完整性验证
                    const fullBuffer = fs.readFileSync(filePath);
                    const hash = crypto.createHash('sha256').update(fullBuffer).digest('hex');
                    
                    // 这里可以与存储的哈希值进行比较（如果有的话）
                    
                } catch (fileError) {
                    corruptedFiles.push({
                        path: filePath,
                        issue: 'read_error',
                        message: `无法读取文件: ${fileError.message}`
                    });
                }
            }
            
            return {
                checked: files.length,
                corrupted: corruptedFiles.length,
                details: corruptedFiles
            };
        } catch (error) {
            this.logger.error('检查文件完整性失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 检查数据库完整性
     */
    async checkDatabaseIntegrity() {
        const issues = [];
        
        try {
            const db = this.dataAccess.getDatabase();
            
            // 执行SQLite内置的完整性检查
            const integrityResult = db.prepare('PRAGMA integrity_check').all();
            
            if (integrityResult.length > 1 || integrityResult[0].integrity_check !== 'ok') {
                issues.push({
                    type: 'database_corruption',
                    details: integrityResult,
                    message: '数据库完整性检查失败'
                });
            }
            
            // 检查外键约束
            const foreignKeyResult = db.prepare('PRAGMA foreign_key_check').all();
            if (foreignKeyResult.length > 0) {
                issues.push({
                    type: 'foreign_key_violation',
                    details: foreignKeyResult,
                    message: '外键约束违反'
                });
            }
            
            // 检查数据一致性
            const consistencyIssues = await this.checkDataConsistency();
            issues.push(...consistencyIssues);
            
            return {
                passed: issues.length === 0,
                issues: issues.length,
                details: issues
            };
        } catch (error) {
            this.logger.error('检查数据库完整性失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 查找孤立文件
     */
    async findOrphanedFiles() {
        const orphanedFiles = [];
        
        try {
            if (!fs.existsSync(this.uploadsDir)) {
                return { found: 0, details: [] };
            }

            const allFiles = this.getAllFiles(this.uploadsDir);
            const referencedFiles = new Set();
            
            // 获取所有被引用的文件
            const applications = this.dataAccess.getApplications();
            for (const app of applications) {
                if (app.attachments && app.attachments.length > 0) {
                    for (const attachment of app.attachments) {
                        referencedFiles.add(path.join(this.uploadsDir, attachment.path));
                    }
                }
            }
            
            // 查找未被引用的文件
            for (const filePath of allFiles) {
                if (!referencedFiles.has(filePath)) {
                    const stats = fs.statSync(filePath);
                    orphanedFiles.push({
                        path: filePath,
                        size: stats.size,
                        lastModified: stats.mtime,
                        message: '文件未被任何记录引用'
                    });
                }
            }
            
            return {
                found: orphanedFiles.length,
                details: orphanedFiles
            };
        } catch (error) {
            this.logger.error('查找孤立文件失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 查找缺失文件
     */
    async findMissingFiles() {
        const missingFiles = [];
        
        try {
            const applications = this.dataAccess.getApplications();
            
            for (const app of applications) {
                if (app.attachments && app.attachments.length > 0) {
                    for (const attachment of app.attachments) {
                        const filePath = path.join(this.uploadsDir, attachment.path);
                        
                        if (!fs.existsSync(filePath)) {
                            missingFiles.push({
                                applicationId: app.id,
                                attachmentPath: attachment.path,
                                attachmentName: attachment.name,
                                message: '数据库记录的文件在文件系统中不存在'
                            });
                        }
                    }
                }
            }
            
            return {
                found: missingFiles.length,
                details: missingFiles
            };
        } catch (error) {
            this.logger.error('查找缺失文件失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 检查数据一致性
     */
    async checkDataConsistency() {
        const issues = [];
        
        try {
            const db = this.dataAccess.getDatabase();
            
            // 检查申请表中的用户是否存在
            const orphanedApplications = db.prepare(`
                SELECT a.id, a.username 
                FROM applications a 
                LEFT JOIN users u ON a.username = u.username 
                WHERE u.username IS NULL AND a.username IS NOT NULL
            `).all();
            
            if (orphanedApplications.length > 0) {
                issues.push({
                    type: 'orphaned_applications',
                    count: orphanedApplications.length,
                    details: orphanedApplications,
                    message: '申请记录引用了不存在的用户'
                });
            }
            
            // 检查设备表中的厂区是否存在
            const orphanedDevices = db.prepare(`
                SELECT d.id, d.deviceCode, d.factory 
                FROM devices d 
                LEFT JOIN factories f ON d.factory = f.name 
                WHERE f.name IS NULL AND d.factory IS NOT NULL
            `).all();
            
            if (orphanedDevices.length > 0) {
                issues.push({
                    type: 'orphaned_devices',
                    count: orphanedDevices.length,
                    details: orphanedDevices,
                    message: '设备记录引用了不存在的厂区'
                });
            }
            
            return issues;
        } catch (error) {
            this.logger.error('检查数据一致性失败', { error: error.message });
            throw error;
        }
    }

    /**
     * 获取目录下所有文件
     */
    getAllFiles(dir) {
        const files = [];
        
        function traverse(currentDir) {
            const items = fs.readdirSync(currentDir);
            
            for (const item of items) {
                const fullPath = path.join(currentDir, item);
                const stats = fs.statSync(fullPath);
                
                if (stats.isDirectory()) {
                    traverse(fullPath);
                } else {
                    files.push(fullPath);
                }
            }
        }
        
        if (fs.existsSync(dir)) {
            traverse(dir);
        }
        
        return files;
    }

    /**
     * 更新统计信息
     */
    updateStats(results) {
        this.stats.totalChecks++;
        this.stats.lastCheckTime = new Date().toISOString();
        
        // 统计不一致问题
        let totalInconsistencies = 0;
        if (results.attachmentConsistency) {
            totalInconsistencies += results.attachmentConsistency.inconsistencies;
        }
        if (results.databaseIntegrity) {
            totalInconsistencies += results.databaseIntegrity.issues;
        }
        
        this.stats.inconsistencies = totalInconsistencies;
        this.stats.orphanedFiles = results.orphanedFiles ? results.orphanedFiles.found : 0;
        this.stats.missingFiles = results.missingFiles ? results.missingFiles.found : 0;
    }

    /**
     * 获取检查统计信息
     */
    getStats() {
        return { ...this.stats };
    }

    /**
     * 修复数据一致性问题
     */
    async repairInconsistencies(repairOptions = {}) {
        this.logger.info('开始修复数据一致性问题');
        
        const results = {
            orphanedFilesRemoved: 0,
            missingFileRecordsFixed: 0,
            errors: []
        };
        
        try {
            // 如果启用了删除孤立文件选项
            if (repairOptions.removeOrphanedFiles) {
                const orphanedFiles = await this.findOrphanedFiles();
                for (const file of orphanedFiles.details) {
                    try {
                        fs.unlinkSync(file.path);
                        results.orphanedFilesRemoved++;
                        this.logger.info(`删除孤立文件: ${file.path}`);
                    } catch (error) {
                        results.errors.push(`删除文件失败: ${file.path} - ${error.message}`);
                    }
                }
            }
            
            // 如果启用了修复缺失文件记录选项
            if (repairOptions.fixMissingFileRecords) {
                const missingFiles = await this.findMissingFiles();
                for (const missing of missingFiles.details) {
                    try {
                        // 从申请的附件列表中移除缺失的文件记录
                        const application = this.dataAccess.getApplicationById(missing.applicationId);
                        if (application && application.attachments) {
                            application.attachments = application.attachments.filter(
                                att => att.path !== missing.attachmentPath
                            );
                            
                            this.dataAccess.updateApplication(missing.applicationId, {
                                attachments: JSON.stringify(application.attachments)
                            });
                            
                            results.missingFileRecordsFixed++;
                            this.logger.info(`修复缺失文件记录: ${missing.attachmentPath}`);
                        }
                    } catch (error) {
                        results.errors.push(`修复记录失败: ${missing.attachmentPath} - ${error.message}`);
                    }
                }
            }
            
            return {
                success: true,
                results
            };
        } catch (error) {
            this.logger.error('修复数据一致性问题失败', { error: error.message });
            return {
                success: false,
                error: error.message,
                results
            };
        }
    }
}

module.exports = DataConsistencyChecker;
