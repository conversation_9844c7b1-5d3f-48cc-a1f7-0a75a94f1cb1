const fs = require('fs');
const path = require('path');
const crypto = require('crypto');

/**
 * 文件安全验证模块
 * 提供文件上传安全检查，包括文件类型、大小、内容验证
 * 防止恶意文件上传和安全威胁
 */
class FileSecurity {
    constructor() {
        // 允许的文件类型配置
        this.allowedTypes = {
            // 文档类型
            'application/pdf': { ext: ['.pdf'], maxSize: 15 * 1024 * 1024, category: 'document' },
            'application/msword': { ext: ['.doc'], maxSize: 10 * 1024 * 1024, category: 'document' },
            'application/vnd.openxmlformats-officedocument.wordprocessingml.document': { 
                ext: ['.docx'], maxSize: 10 * 1024 * 1024, category: 'document' 
            },
            'application/vnd.ms-excel': { ext: ['.xls'], maxSize: 10 * 1024 * 1024, category: 'document' },
            'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': { 
                ext: ['.xlsx'], maxSize: 10 * 1024 * 1024, category: 'document' 
            },
            
            // 图片类型
            'image/jpeg': { ext: ['.jpg', '.jpeg'], maxSize: 5 * 1024 * 1024, category: 'image' },
            'image/png': { ext: ['.png'], maxSize: 5 * 1024 * 1024, category: 'image' },
            'image/gif': { ext: ['.gif'], maxSize: 2 * 1024 * 1024, category: 'image' },
            'image/webp': { ext: ['.webp'], maxSize: 3 * 1024 * 1024, category: 'image' },
            
            // 文本类型
            'text/plain': { ext: ['.txt'], maxSize: 1 * 1024 * 1024, category: 'text' },
            'text/csv': { ext: ['.csv'], maxSize: 5 * 1024 * 1024, category: 'text' }
        };

        // 危险文件扩展名黑名单
        this.dangerousExtensions = [
            '.exe', '.bat', '.cmd', '.com', '.pif', '.scr', '.vbs', '.js', '.jar',
            '.app', '.deb', '.pkg', '.dmg', '.rpm', '.msi', '.run', '.bin',
            '.sh', '.ps1', '.php', '.asp', '.aspx', '.jsp', '.py', '.rb', '.pl'
        ];

        // 危险MIME类型黑名单
        this.dangerousMimeTypes = [
            'application/x-executable',
            'application/x-msdownload',
            'application/x-msdos-program',
            'application/x-msi',
            'application/x-bat',
            'application/x-sh',
            'application/javascript',
            'text/javascript',
            'application/x-php',
            'text/x-php'
        ];

        // 文件魔数（文件头）验证
        this.fileMagicNumbers = {
            'pdf': [0x25, 0x50, 0x44, 0x46], // %PDF
            'jpg': [0xFF, 0xD8, 0xFF],
            'png': [0x89, 0x50, 0x4E, 0x47, 0x0D, 0x0A, 0x1A, 0x0A],
            'gif': [0x47, 0x49, 0x46, 0x38],
            'zip': [0x50, 0x4B, 0x03, 0x04], // ZIP文件（包括docx, xlsx等）
            'doc': [0xD0, 0xCF, 0x11, 0xE0, 0xA1, 0xB1, 0x1A, 0xE1] // MS Office 97-2003
        };

        // 病毒签名模式（简单示例）
        this.virusPatterns = [
            Buffer.from('EICAR-STANDARD-ANTIVIRUS-TEST-FILE'),
            Buffer.from('X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR'),
        ];

        // 增强的恶意文件特征库
        this.malwareSignatures = [
            // 常见恶意软件特征
            { pattern: Buffer.from('X5O!P%@AP[4\\PZX54(P^)7CC)7}$EICAR-STANDARD-ANTIVIRUS-TEST-FILE!$H+H*'), name: 'EICAR-Test-File' },
            { pattern: Buffer.from('MZ'), name: 'PE-Executable-Header' },
            { pattern: Buffer.from('#!/bin/sh'), name: 'Shell-Script' },
            { pattern: Buffer.from('#!/bin/bash'), name: 'Bash-Script' },
            { pattern: Buffer.from('<?php'), name: 'PHP-Script' },
            { pattern: Buffer.from('<script'), name: 'JavaScript-Tag' },
            { pattern: Buffer.from('eval('), name: 'JavaScript-Eval' },
            { pattern: Buffer.from('document.write'), name: 'JavaScript-DocumentWrite' },
            { pattern: Buffer.from('powershell'), name: 'PowerShell-Command' },
            { pattern: Buffer.from('cmd.exe'), name: 'Command-Prompt' }
        ];

        // 文件内容扫描配置
        this.scanConfig = {
            // 最大扫描文件大小（50MB）
            maxScanSize: 50 * 1024 * 1024,
            // 扫描缓冲区大小
            scanBufferSize: 64 * 1024,
            // 是否启用深度扫描
            deepScan: true,
            // 扫描超时时间（秒）
            scanTimeout: 30,
            // 是否启用启发式检测
            heuristicScan: true
        };

        // 文件隔离目录
        this.quarantineDir = path.join(__dirname, '../../quarantine');
        this.ensureQuarantineDir();

        // 扫描统计
        this.scanStats = {
            totalScans: 0,
            threatsDetected: 0,
            filesQuarantined: 0,
            lastScanTime: null
        };
    }

    /**
     * 确保隔离目录存在
     */
    ensureQuarantineDir() {
        if (!fs.existsSync(this.quarantineDir)) {
            fs.mkdirSync(this.quarantineDir, { recursive: true });
        }
    }

    /**
     * 执行深度恶意软件扫描
     */
    async performDeepScan(filePath) {
        const scanResult = {
            isClean: true,
            threats: [],
            scanTime: 0,
            scannedBytes: 0
        };

        const startTime = Date.now();

        try {
            const stats = fs.statSync(filePath);

            // 检查文件大小限制
            if (stats.size > this.scanConfig.maxScanSize) {
                return {
                    isClean: false,
                    threats: [{ name: 'File-Too-Large', severity: 'medium' }],
                    scanTime: Date.now() - startTime,
                    scannedBytes: 0
                };
            }

            // 读取文件内容进行扫描
            const fileBuffer = fs.readFileSync(filePath);
            scanResult.scannedBytes = fileBuffer.length;

            // 检查恶意软件特征
            for (const signature of this.malwareSignatures) {
                if (fileBuffer.includes(signature.pattern)) {
                    scanResult.isClean = false;
                    scanResult.threats.push({
                        name: signature.name,
                        severity: 'high',
                        type: 'signature_match'
                    });
                }
            }

            // 启发式检测
            if (this.scanConfig.heuristicScan) {
                const heuristicThreats = this.performHeuristicScan(fileBuffer);
                scanResult.threats.push(...heuristicThreats);
                if (heuristicThreats.length > 0) {
                    scanResult.isClean = false;
                }
            }

            scanResult.scanTime = Date.now() - startTime;

            // 更新统计信息
            this.scanStats.totalScans++;
            this.scanStats.lastScanTime = new Date().toISOString();
            if (!scanResult.isClean) {
                this.scanStats.threatsDetected++;
            }

            return scanResult;
        } catch (error) {
            return {
                isClean: false,
                threats: [{ name: 'Scan-Error', severity: 'high', error: error.message }],
                scanTime: Date.now() - startTime,
                scannedBytes: 0
            };
        }
    }

    /**
     * 启发式扫描
     */
    performHeuristicScan(fileBuffer) {
        const threats = [];
        const content = fileBuffer.toString('utf8', 0, Math.min(fileBuffer.length, 10240)); // 检查前10KB

        // 检查可疑脚本内容
        const suspiciousPatterns = [
            { pattern: /eval\s*\(/gi, name: 'Suspicious-Eval-Usage' },
            { pattern: /document\.write\s*\(/gi, name: 'Suspicious-DocumentWrite' },
            { pattern: /window\.location\s*=/gi, name: 'Suspicious-Redirect' },
            { pattern: /base64_decode\s*\(/gi, name: 'Suspicious-Base64-Decode' },
            { pattern: /shell_exec\s*\(/gi, name: 'Suspicious-Shell-Exec' },
            { pattern: /system\s*\(/gi, name: 'Suspicious-System-Call' },
            { pattern: /\$_GET\[.*\]\s*\(/gi, name: 'Suspicious-GET-Execution' },
            { pattern: /\$_POST\[.*\]\s*\(/gi, name: 'Suspicious-POST-Execution' }
        ];

        for (const { pattern, name } of suspiciousPatterns) {
            if (pattern.test(content)) {
                threats.push({
                    name,
                    severity: 'medium',
                    type: 'heuristic'
                });
            }
        }

        // 检查高熵内容（可能的加密/混淆代码）
        const entropy = this.calculateEntropy(content);
        if (entropy > 7.5) {
            threats.push({
                name: 'High-Entropy-Content',
                severity: 'medium',
                type: 'heuristic',
                entropy
            });
        }

        return threats;
    }

    /**
     * 计算字符串熵值
     */
    calculateEntropy(str) {
        const freq = {};
        for (const char of str) {
            freq[char] = (freq[char] || 0) + 1;
        }

        let entropy = 0;
        const len = str.length;
        for (const count of Object.values(freq)) {
            const p = count / len;
            entropy -= p * Math.log2(p);
        }

        return entropy;
    }

    /**
     * 隔离可疑文件
     */
    async quarantineFile(filePath, threatInfo) {
        try {
            const fileName = path.basename(filePath);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const quarantineName = `${timestamp}_${fileName}`;
            const quarantinePath = path.join(this.quarantineDir, quarantineName);

            // 移动文件到隔离区
            fs.renameSync(filePath, quarantinePath);

            // 创建威胁信息文件
            const threatInfoPath = path.join(this.quarantineDir, `${quarantineName}.info`);
            fs.writeFileSync(threatInfoPath, JSON.stringify({
                originalPath: filePath,
                quarantineTime: new Date().toISOString(),
                threats: threatInfo.threats,
                scanResult: threatInfo
            }, null, 2));

            this.scanStats.filesQuarantined++;

            return {
                success: true,
                quarantinePath,
                quarantineName
            };
        } catch (error) {
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 验证上传的文件
     * @param {Object} file - Multer文件对象
     * @param {Object} options - 验证选项
     * @returns {Object} 验证结果
     */
    async validateFile(file, options = {}) {
        try {
            const result = {
                isValid: true,
                errors: [],
                warnings: [],
                fileInfo: {
                    originalName: file.originalname,
                    size: file.size,
                    mimeType: file.mimetype,
                    extension: path.extname(file.originalname).toLowerCase(),
                    hash: null
                }
            };

            // 1. 基本信息验证
            const basicValidation = this.validateBasicInfo(file);
            if (!basicValidation.isValid) {
                result.isValid = false;
                result.errors.push(...basicValidation.errors);
            }

            // 2. 文件扩展名验证
            const extensionValidation = this.validateExtension(file);
            if (!extensionValidation.isValid) {
                result.isValid = false;
                result.errors.push(...extensionValidation.errors);
            }

            // 3. MIME类型验证
            const mimeValidation = this.validateMimeType(file);
            if (!mimeValidation.isValid) {
                result.isValid = false;
                result.errors.push(...mimeValidation.errors);
            }

            // 4. 文件大小验证
            const sizeValidation = this.validateFileSize(file);
            if (!sizeValidation.isValid) {
                result.isValid = false;
                result.errors.push(...sizeValidation.errors);
            }

            // 5. 文件内容验证（如果文件存在）
            if (file.path && fs.existsSync(file.path)) {
                const contentValidation = await this.validateFileContent(file);
                if (!contentValidation.isValid) {
                    result.isValid = false;
                    result.errors.push(...contentValidation.errors);
                }
                result.warnings.push(...contentValidation.warnings);

                // 6. 深度恶意软件扫描
                if (options.enableDeepScan !== false) {
                    const scanResult = await this.performDeepScan(file.path);
                    result.scanResult = scanResult;

                    if (!scanResult.isClean) {
                        result.isValid = false;
                        result.errors.push(`检测到安全威胁: ${scanResult.threats.map(t => t.name).join(', ')}`);

                        // 如果检测到威胁，自动隔离文件
                        if (options.autoQuarantine !== false) {
                            const quarantineResult = await this.quarantineFile(file.path, scanResult);
                            result.quarantineResult = quarantineResult;

                            if (quarantineResult.success) {
                                result.warnings.push(`文件已被隔离到: ${quarantineResult.quarantineName}`);
                            } else {
                                result.errors.push(`文件隔离失败: ${quarantineResult.error}`);
                            }
                        }
                    }
                }
                result.fileInfo.hash = contentValidation.hash;
            }

            // 6. 文件名安全验证
            const nameValidation = this.validateFileName(file.originalname);
            if (!nameValidation.isValid) {
                result.isValid = false;
                result.errors.push(...nameValidation.errors);
            }

            return result;
        } catch (error) {
            console.error('文件验证失败:', error);
            return {
                isValid: false,
                errors: ['文件验证过程中发生错误'],
                warnings: [],
                fileInfo: null
            };
        }
    }

    /**
     * 验证基本文件信息
     */
    validateBasicInfo(file) {
        const errors = [];

        if (!file) {
            errors.push('文件对象不存在');
        }

        if (!file.originalname) {
            errors.push('文件名不能为空');
        }

        if (!file.mimetype) {
            errors.push('文件MIME类型不能为空');
        }

        if (file.size === undefined || file.size === null) {
            errors.push('文件大小信息缺失');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证文件扩展名
     */
    validateExtension(file) {
        const errors = [];
        const extension = path.extname(file.originalname).toLowerCase();

        // 检查是否为危险扩展名
        if (this.dangerousExtensions.includes(extension)) {
            errors.push(`不允许上传 ${extension} 类型的文件`);
        }

        // 检查扩展名是否在允许列表中
        const isAllowed = Object.values(this.allowedTypes).some(type => 
            type.ext.includes(extension)
        );

        if (!isAllowed) {
            errors.push(`不支持的文件类型: ${extension}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证MIME类型
     */
    validateMimeType(file) {
        const errors = [];

        // 检查是否为危险MIME类型
        if (this.dangerousMimeTypes.includes(file.mimetype)) {
            errors.push(`不允许的文件类型: ${file.mimetype}`);
        }

        // 检查MIME类型是否在允许列表中
        if (!this.allowedTypes[file.mimetype]) {
            errors.push(`不支持的MIME类型: ${file.mimetype}`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证文件大小
     */
    validateFileSize(file) {
        const errors = [];
        const typeConfig = this.allowedTypes[file.mimetype];

        if (file.size <= 0) {
            errors.push('文件大小不能为0');
        }

        if (typeConfig && file.size > typeConfig.maxSize) {
            const maxSizeMB = (typeConfig.maxSize / (1024 * 1024)).toFixed(1);
            const fileSizeMB = (file.size / (1024 * 1024)).toFixed(1);
            errors.push(`文件大小 ${fileSizeMB}MB 超过限制 ${maxSizeMB}MB`);
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证文件内容
     */
    async validateFileContent(file) {
        const errors = [];
        const warnings = [];
        let hash = null;

        try {
            const fileBuffer = fs.readFileSync(file.path);
            
            // 计算文件哈希
            hash = crypto.createHash('sha256').update(fileBuffer).digest('hex');

            // 验证文件魔数
            const magicValidation = this.validateFileMagicNumber(fileBuffer, file.mimetype);
            if (!magicValidation.isValid) {
                errors.push(...magicValidation.errors);
            }

            // 病毒扫描（简单模式匹配）
            const virusValidation = this.scanForVirus(fileBuffer);
            if (!virusValidation.isValid) {
                errors.push(...virusValidation.errors);
            }

            // 检查文件是否为空
            if (fileBuffer.length === 0) {
                errors.push('文件内容为空');
            }

            // 检查文件是否过大（内存保护）
            if (fileBuffer.length > 50 * 1024 * 1024) { // 50MB
                warnings.push('文件较大，处理可能需要更多时间');
            }

        } catch (error) {
            console.error('文件内容验证失败:', error);
            errors.push('无法读取文件内容');
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            hash
        };
    }

    /**
     * 验证文件魔数
     */
    validateFileMagicNumber(buffer, mimeType) {
        const errors = [];

        // 根据MIME类型确定应该检查的魔数
        let expectedMagic = null;
        
        if (mimeType === 'application/pdf') {
            expectedMagic = this.fileMagicNumbers.pdf;
        } else if (mimeType === 'image/jpeg') {
            expectedMagic = this.fileMagicNumbers.jpg;
        } else if (mimeType === 'image/png') {
            expectedMagic = this.fileMagicNumbers.png;
        } else if (mimeType === 'image/gif') {
            expectedMagic = this.fileMagicNumbers.gif;
        } else if (mimeType.includes('officedocument')) {
            expectedMagic = this.fileMagicNumbers.zip; // Office 2007+格式基于ZIP
        } else if (mimeType === 'application/msword' || mimeType === 'application/vnd.ms-excel') {
            expectedMagic = this.fileMagicNumbers.doc; // Office 97-2003格式
        }

        if (expectedMagic) {
            const actualMagic = Array.from(buffer.slice(0, expectedMagic.length));
            const matches = expectedMagic.every((byte, index) => actualMagic[index] === byte);
            
            if (!matches) {
                errors.push('文件内容与声明的文件类型不匹配');
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 简单病毒扫描
     */
    scanForVirus(buffer) {
        const errors = [];

        for (const pattern of this.virusPatterns) {
            if (buffer.includes(pattern)) {
                errors.push('检测到潜在的恶意内容');
                break;
            }
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 验证文件名
     */
    validateFileName(filename) {
        const errors = [];

        // 检查文件名长度
        if (filename.length > 255) {
            errors.push('文件名过长');
        }

        // 检查危险字符
        const dangerousChars = /[<>:"|?*\x00-\x1f]/;
        if (dangerousChars.test(filename)) {
            errors.push('文件名包含非法字符');
        }

        // 检查保留名称（Windows）
        const reservedNames = ['CON', 'PRN', 'AUX', 'NUL', 'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9', 'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'];
        const nameWithoutExt = path.basename(filename, path.extname(filename)).toUpperCase();
        if (reservedNames.includes(nameWithoutExt)) {
            errors.push('文件名为系统保留名称');
        }

        // 检查是否以点开头或结尾
        if (filename.startsWith('.') || filename.endsWith('.')) {
            errors.push('文件名不能以点开头或结尾');
        }

        return {
            isValid: errors.length === 0,
            errors
        };
    }

    /**
     * 生成安全的文件名
     * @param {string} originalName - 原始文件名
     * @returns {string} 安全的文件名
     */
    generateSafeFileName(originalName) {
        const timestamp = Date.now();
        const random = Math.random().toString(36).substring(2, 8);
        const extension = path.extname(originalName).toLowerCase();
        const baseName = path.basename(originalName, extension)
            .replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_') // 只保留字母、数字和中文
            .substring(0, 50); // 限制长度

        return `${timestamp}-${random}-${baseName}${extension}`;
    }

    /**
     * 获取文件类型配置
     * @param {string} mimeType - MIME类型
     * @returns {Object|null} 类型配置
     */
    getTypeConfig(mimeType) {
        return this.allowedTypes[mimeType] || null;
    }

    /**
     * 检查文件是否为允许的类型
     * @param {string} mimeType - MIME类型
     * @returns {boolean} 是否允许
     */
    isAllowedType(mimeType) {
        return !!this.allowedTypes[mimeType];
    }

    /**
     * 获取扫描统计信息
     */
    getScanStats() {
        return { ...this.scanStats };
    }

    /**
     * 重置扫描统计信息
     */
    resetScanStats() {
        this.scanStats = {
            totalScans: 0,
            threatsDetected: 0,
            filesQuarantined: 0,
            lastScanTime: null
        };
    }

    /**
     * 获取隔离文件列表
     */
    getQuarantinedFiles() {
        try {
            if (!fs.existsSync(this.quarantineDir)) {
                return [];
            }

            const files = fs.readdirSync(this.quarantineDir);
            const quarantinedFiles = [];

            for (const file of files) {
                if (file.endsWith('.info')) {
                    const infoPath = path.join(this.quarantineDir, file);
                    try {
                        const info = JSON.parse(fs.readFileSync(infoPath, 'utf8'));
                        const filePath = path.join(this.quarantineDir, file.replace('.info', ''));
                        const fileExists = fs.existsSync(filePath);

                        quarantinedFiles.push({
                            name: file.replace('.info', ''),
                            info,
                            fileExists,
                            size: fileExists ? fs.statSync(filePath).size : 0
                        });
                    } catch (error) {
                        // 忽略损坏的信息文件
                    }
                }
            }

            return quarantinedFiles.sort((a, b) =>
                new Date(b.info.quarantineTime) - new Date(a.info.quarantineTime)
            );
        } catch (error) {
            return [];
        }
    }

    /**
     * 删除隔离文件
     */
    deleteQuarantinedFile(fileName) {
        try {
            const filePath = path.join(this.quarantineDir, fileName);
            const infoPath = path.join(this.quarantineDir, `${fileName}.info`);

            if (fs.existsSync(filePath)) {
                fs.unlinkSync(filePath);
            }

            if (fs.existsSync(infoPath)) {
                fs.unlinkSync(infoPath);
            }

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 恢复隔离文件
     */
    restoreQuarantinedFile(fileName, targetPath) {
        try {
            const filePath = path.join(this.quarantineDir, fileName);
            const infoPath = path.join(this.quarantineDir, `${fileName}.info`);

            if (!fs.existsSync(filePath)) {
                return { success: false, error: '隔离文件不存在' };
            }

            // 确保目标目录存在
            const targetDir = path.dirname(targetPath);
            if (!fs.existsSync(targetDir)) {
                fs.mkdirSync(targetDir, { recursive: true });
            }

            // 移动文件
            fs.renameSync(filePath, targetPath);

            // 删除信息文件
            if (fs.existsSync(infoPath)) {
                fs.unlinkSync(infoPath);
            }

            return { success: true };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 清理隔离区（删除超过指定天数的文件）
     */
    cleanupQuarantine(daysOld = 30) {
        try {
            const cutoffTime = Date.now() - (daysOld * 24 * 60 * 60 * 1000);
            const quarantinedFiles = this.getQuarantinedFiles();
            let cleanedCount = 0;

            for (const file of quarantinedFiles) {
                const quarantineTime = new Date(file.info.quarantineTime).getTime();
                if (quarantineTime < cutoffTime) {
                    const deleteResult = this.deleteQuarantinedFile(file.name);
                    if (deleteResult.success) {
                        cleanedCount++;
                    }
                }
            }

            return { success: true, cleanedCount };
        } catch (error) {
            return { success: false, error: error.message };
        }
    }

    /**
     * 更新扫描配置
     */
    updateScanConfig(newConfig) {
        this.scanConfig = { ...this.scanConfig, ...newConfig };
    }

    /**
     * 获取扫描配置
     */
    getScanConfig() {
        return { ...this.scanConfig };
    }

    /**
     * 添加自定义恶意软件特征
     */
    addMalwareSignature(pattern, name) {
        this.malwareSignatures.push({
            pattern: Buffer.from(pattern),
            name
        });
    }

    /**
     * 获取安全报告
     */
    getSecurityReport() {
        const quarantinedFiles = this.getQuarantinedFiles();
        const threatTypes = {};

        for (const file of quarantinedFiles) {
            for (const threat of file.info.threats || []) {
                threatTypes[threat.name] = (threatTypes[threat.name] || 0) + 1;
            }
        }

        return {
            scanStats: this.getScanStats(),
            quarantineStats: {
                totalFiles: quarantinedFiles.length,
                totalSize: quarantinedFiles.reduce((sum, file) => sum + file.size, 0)
            },
            threatDistribution: threatTypes,
            lastScanTime: this.scanStats.lastScanTime,
            scanConfig: this.getScanConfig()
        };
    }
}

module.exports = FileSecurity;
