const DatabaseManager = require('./database');

class DataAccess {
    constructor() {
        this.dbManager = new DatabaseManager();
        this.db = this.dbManager.getDatabase();
    }

    // ==================== 用户相关操作 ====================

    getUsers() {
        const stmt = this.db.prepare('SELECT * FROM users ORDER BY createdAt DESC');
        return stmt.all();
    }

    getUserByUsername(username) {
        const stmt = this.db.prepare('SELECT * FROM users WHERE username = ?');
        return stmt.get(username);
    }

    getUserByUserId(userId) {
        const stmt = this.db.prepare('SELECT * FROM users WHERE userId = ?');
        return stmt.get(userId);
    }

    addUser(userData) {
        const stmt = this.db.prepare(`
            INSERT INTO users (username, password, role, email, department, userCode, userId, signature, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const now = new Date().toISOString();
        return stmt.run(
            userData.username,
            userData.password,
            userData.role,
            userData.email || null,
            userData.department || null,
            userData.userCode || null,
            userData.userId,
            userData.signature || null,
            now,
            now
        );
    }

    updateUser(username, userData) {
        const stmt = this.db.prepare(`
            UPDATE users
            SET password = ?, role = ?, email = ?, department = ?, userCode = ?, signature = ?, updatedAt = ?
            WHERE username = ?
        `);

        return stmt.run(
            userData.password,
            userData.role,
            userData.email || null,
            userData.department || null,
            userData.userCode || null,
            userData.signature || null,
            new Date().toISOString(),
            username
        );
    }

    deleteUser(username) {
        const stmt = this.db.prepare('DELETE FROM users WHERE username = ?');
        return stmt.run(username);
    }

    // ==================== 部门相关操作 ====================

    getDepartments() {
        const stmt = this.db.prepare('SELECT * FROM departments ORDER BY name');
        return stmt.all();
    }

    addDepartment(deptData) {
        const stmt = this.db.prepare(`
            INSERT INTO departments (id, name, code, description, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        const now = new Date().toISOString();
        return stmt.run(
            deptData.id,
            deptData.name,
            deptData.code,
            deptData.description || null,
            now,
            now
        );
    }

    updateDepartment(id, deptData) {
        const stmt = this.db.prepare(`
            UPDATE departments
            SET name = ?, code = ?, description = ?, updatedAt = ?
            WHERE id = ?
        `);

        return stmt.run(
            deptData.name,
            deptData.code,
            deptData.description || null,
            new Date().toISOString(),
            id
        );
    }

    deleteDepartment(id) {
        const stmt = this.db.prepare('DELETE FROM departments WHERE id = ?');
        return stmt.run(id);
    }

    // ==================== 工厂相关操作 ====================

    getFactories() {
        const stmt = this.db.prepare('SELECT * FROM factories ORDER BY name');
        return stmt.all();
    }

    addFactory(factoryData) {
        const stmt = this.db.prepare(`
            INSERT INTO factories (id, name, description, createTime)
            VALUES (?, ?, ?, ?)
        `);

        return stmt.run(
            factoryData.id,
            factoryData.name,
            factoryData.description || null,
            new Date().toISOString()
        );
    }

    updateFactory(id, factoryData) {
        const stmt = this.db.prepare(`
            UPDATE factories
            SET name = ?, description = ?
            WHERE id = ?
        `);

        return stmt.run(
            factoryData.name,
            factoryData.description || null,
            id
        );
    }

    deleteFactory(id) {
        const stmt = this.db.prepare('DELETE FROM factories WHERE id = ?');
        return stmt.run(id);
    }

    // ==================== 设备相关操作 ====================

    getDevices() {
        const stmt = this.db.prepare('SELECT * FROM devices ORDER BY createTime DESC');
        return stmt.all();
    }

    getDeviceById(id) {
        const stmt = this.db.prepare('SELECT * FROM devices WHERE id = ?');
        return stmt.get(id);
    }

    getDevicesByFactory(factory) {
        const stmt = this.db.prepare('SELECT * FROM devices WHERE factory = ? ORDER BY deviceCode');
        return stmt.all(factory);
    }

    addDevice(deviceData) {
        const stmt = this.db.prepare(`
            INSERT INTO devices (id, deviceCode, deviceName, factory, location, responsible, entryDate, status, createTime, updateTime)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const now = new Date().toISOString();
        return stmt.run(
            deviceData.id,
            deviceData.deviceCode,
            deviceData.deviceName,
            deviceData.factory,
            deviceData.location || null,
            deviceData.responsible || null,
            deviceData.entryDate || null,
            deviceData.status || '启用',
            now,
            now
        );
    }

    updateDevice(id, deviceData) {
        const stmt = this.db.prepare(`
            UPDATE devices
            SET deviceCode = ?, deviceName = ?, factory = ?, location = ?, responsible = ?,
                entryDate = ?, status = ?, updateTime = ?
            WHERE id = ?
        `);

        return stmt.run(
            deviceData.deviceCode,
            deviceData.deviceName,
            deviceData.factory,
            deviceData.location || null,
            deviceData.responsible || null,
            deviceData.entryDate || null,
            deviceData.status || '启用',
            new Date().toISOString(),
            id
        );
    }

    deleteDevice(id) {
        const stmt = this.db.prepare('DELETE FROM devices WHERE id = ?');
        return stmt.run(id);
    }

    // ==================== 申请相关操作 ====================

    getApplications(limit = null, offset = 0) {
        let sql = 'SELECT * FROM applications ORDER BY date DESC, id DESC';
        if (limit) {
            sql += ` LIMIT ${limit} OFFSET ${offset}`;
        }
        const stmt = this.db.prepare(sql);
        const applications = stmt.all();

        // 为每个申请获取完整的审批信息
        applications.forEach(app => {
            // 解析JSON字段
            app.attachments = JSON.parse(app.attachments || '[]');
            app.reminderInfo = JSON.parse(app.reminderInfo || '{}');
            app.stageTimestamps = JSON.parse(app.stageTimestamps || '{}');

            // 获取审批信息
            const approvalStmt = this.db.prepare('SELECT * FROM application_approvals WHERE applicationId = ?');
            const approvals = approvalStmt.all(app.id);

            // 重构审批信息格式
            app.approvals = {
                directors: {},
                managers: {},
                chief: { status: 'pending', attachments: [] },
                ceo: { status: 'pending', attachments: [] }
            };

            approvals.forEach(approval => {
                const attachments = JSON.parse(approval.attachments || '[]');
                const approvalData = {
                    status: approval.status,
                    comment: approval.comment,
                    attachments: attachments,
                    date: approval.approvedAt,
                    approverUsername: approval.approverUsername
                };

                if (approval.approverType === 'director') {
                    app.approvals.directors[approval.approverUsername] = approvalData;
                } else if (approval.approverType === 'manager') {
                    app.approvals.managers[approval.approverUsername] = approvalData;
                } else if (approval.approverType === 'chief') {
                    app.approvals.chief = { ...approvalData, approverUsername: approval.approverUsername };
                } else if (approval.approverType === 'ceo') {
                    app.approvals.ceo = { ...approvalData, approverUsername: approval.approverUsername };
                }
            });
        });

        return applications;
    }

    getApplicationById(id) {
        const stmt = this.db.prepare('SELECT * FROM applications WHERE id = ?');
        const application = stmt.get(id);

        if (application) {
            // 解析JSON字段
            application.attachments = JSON.parse(application.attachments || '[]');
            application.reminderInfo = JSON.parse(application.reminderInfo || '{}');
            application.stageTimestamps = JSON.parse(application.stageTimestamps || '{}');

            // 获取审批信息
            const approvalStmt = this.db.prepare('SELECT * FROM application_approvals WHERE applicationId = ?');
            const approvals = approvalStmt.all(id);

            // 重构审批信息格式
            application.approvals = {
                directors: {},
                managers: {},
                chief: { status: 'pending', attachments: [] },
                ceo: { status: 'pending', attachments: [] }
            };

            approvals.forEach(approval => {
                const attachments = JSON.parse(approval.attachments || '[]');
                const approvalData = {
                    status: approval.status,
                    comment: approval.comment,
                    attachments: attachments,
                    date: approval.approvedAt,
                    approverUsername: approval.approverUsername
                };

                if (approval.approverType === 'director') {
                    application.approvals.directors[approval.approverUsername] = approvalData;
                } else if (approval.approverType === 'manager') {
                    application.approvals.managers[approval.approverUsername] = approvalData;
                } else if (approval.approverType === 'chief') {
                    application.approvals.chief = { ...approvalData, approverUsername: approval.approverUsername };
                } else if (approval.approverType === 'ceo') {
                    application.approvals.ceo = { ...approvalData, approverUsername: approval.approverUsername };
                }
            });
        }

        return application;
    }

    addApplication(applicationData) {
        const stmt = this.db.prepare(`
            INSERT INTO applications
            (id, applicant, department, date, content, amount, currency, priority, attachments,
             username, applicationCode, status, reminderInfo, stageTimestamps, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const now = new Date().toISOString();
        return stmt.run(
            applicationData.id,
            applicationData.applicant,
            applicationData.department || null,
            applicationData.date,
            applicationData.content,
            applicationData.amount || null,
            applicationData.currency || 'CNY',
            applicationData.priority || 'normal',
            JSON.stringify(applicationData.attachments || []),
            applicationData.username || applicationData.applicant,
            applicationData.applicationCode || null,
            applicationData.status || '待厂长审核',
            JSON.stringify(applicationData.reminderInfo || {}),
            JSON.stringify(applicationData.stageTimestamps || {}),
            now,
            now
        );
    }

    updateApplication(id, applicationData) {
        const stmt = this.db.prepare(`
            UPDATE applications
            SET applicant = ?, department = ?, date = ?, content = ?, amount = ?, currency = ?,
                priority = ?, attachments = ?, status = ?, reminderInfo = ?, stageTimestamps = ?, updatedAt = ?
            WHERE id = ?
        `);

        return stmt.run(
            applicationData.applicant,
            applicationData.department || null,
            applicationData.date,
            applicationData.content,
            applicationData.amount || null,
            applicationData.currency || 'CNY',
            applicationData.priority || 'normal',
            JSON.stringify(applicationData.attachments || []),
            applicationData.status || '待厂长审核',
            JSON.stringify(applicationData.reminderInfo || {}),
            JSON.stringify(applicationData.stageTimestamps || {}),
            new Date().toISOString(),
            id
        );
    }

    deleteApplication(id) {
        // 使用事务删除申请和相关审批记录
        const deleteTransaction = this.db.transaction(() => {
            const deleteApprovals = this.db.prepare('DELETE FROM application_approvals WHERE applicationId = ?');
            const deleteApplication = this.db.prepare('DELETE FROM applications WHERE id = ?');

            deleteApprovals.run(id);
            deleteApplication.run(id);
        });

        return deleteTransaction();
    }

    // ==================== 申请审批相关操作 ====================

    addOrUpdateApproval(approvalData) {
        const stmt = this.db.prepare(`
            INSERT OR REPLACE INTO application_approvals
            (applicationId, approverType, approverUsername, status, comment, attachments, approvedAt, createdAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?)
        `);

        return stmt.run(
            approvalData.applicationId,
            approvalData.approverType,
            approvalData.approverUsername,
            approvalData.status,
            approvalData.comment || null,
            JSON.stringify(approvalData.attachments || []),
            approvalData.approvedAt || null,
            new Date().toISOString()
        );
    }

    // ==================== 维修记录相关操作 ====================

    getMaintenanceRecords(limit = null, offset = 0) {
        let sql = 'SELECT * FROM maintenance_records ORDER BY reportDate DESC, id DESC';
        if (limit) {
            sql += ` LIMIT ${limit} OFFSET ${offset}`;
        }
        const stmt = this.db.prepare(sql);
        const records = stmt.all();

        // 解析JSON字段并映射字段名以保持前端兼容性
        records.forEach(record => {
            record.attachments = JSON.parse(record.attachments || '[]');
            record.customFields = JSON.parse(record.customFields || '{}');

            // 字段映射：SQLite字段名 -> 前端期望的字段名
            record.type = record.maintenanceType; // maintenanceType -> type
            record.date = record.reportDate; // reportDate -> date
            record.operator = record.reportedBy; // reportedBy -> operator

            // 如果recordCode为空，使用ID作为记录编号
            if (!record.recordCode) {
                record.recordCode = record.id;
            }

            // 保留原有字段以防其他地方使用
        });

        return records;
    }

    getMaintenanceRecordById(id) {
        const stmt = this.db.prepare('SELECT * FROM maintenance_records WHERE id = ?');
        const record = stmt.get(id);

        if (record) {
            record.attachments = JSON.parse(record.attachments || '[]');
            record.customFields = JSON.parse(record.customFields || '{}');

            // 字段映射：SQLite字段名 -> 前端期望的字段名
            record.type = record.maintenanceType; // maintenanceType -> type
            record.date = record.reportDate; // reportDate -> date
            record.operator = record.reportedBy; // reportedBy -> operator

            // 如果recordCode为空，使用ID作为记录编号
            if (!record.recordCode) {
                record.recordCode = record.id;
            }
        }

        return record;
    }

    addMaintenanceRecord(recordData) {
        const stmt = this.db.prepare(`
            INSERT INTO maintenance_records
            (id, recordCode, deviceId, deviceCode, deviceName, factory, location, responsible, maintenanceType,
             reportedBy, reportDate, startTime, endTime, description, result, reviewer, faultSeverity, customFields,
             priority, status, assignedTo, completedBy, completedDate, cost, parts, solution, attachments, createdAt, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const now = new Date().toISOString();
        return stmt.run(
            recordData.id,
            recordData.recordCode || null,
            recordData.deviceId,
            recordData.deviceCode,
            recordData.deviceName,
            recordData.factory,
            recordData.location || null,
            recordData.responsible || null,
            recordData.maintenanceType,
            recordData.reportedBy,
            recordData.reportDate,
            recordData.startTime || null,
            recordData.endTime || null,
            recordData.description,
            recordData.result || null,
            recordData.reviewer || null,
            recordData.faultSeverity || null,
            JSON.stringify(recordData.customFields || {}),
            recordData.priority || 'normal',
            recordData.status || 'pending',
            recordData.assignedTo || null,
            recordData.completedBy || null,
            recordData.completedDate || null,
            recordData.cost || null,
            recordData.parts || null,
            recordData.solution || null,
            JSON.stringify(recordData.attachments || []),
            now,
            now
        );
    }

    updateMaintenanceRecord(id, recordData) {
        const stmt = this.db.prepare(`
            UPDATE maintenance_records
            SET recordCode = ?, deviceId = ?, deviceCode = ?, deviceName = ?, factory = ?, location = ?, responsible = ?,
                maintenanceType = ?, reportedBy = ?, reportDate = ?, startTime = ?, endTime = ?, description = ?,
                result = ?, reviewer = ?, faultSeverity = ?, customFields = ?, priority = ?, status = ?,
                assignedTo = ?, completedBy = ?, completedDate = ?, cost = ?, parts = ?, solution = ?,
                attachments = ?, updatedAt = ?
            WHERE id = ?
        `);

        return stmt.run(
            recordData.recordCode || null,
            recordData.deviceId,
            recordData.deviceCode,
            recordData.deviceName,
            recordData.factory,
            recordData.location || null,
            recordData.responsible || null,
            recordData.maintenanceType,
            recordData.reportedBy,
            recordData.reportDate,
            recordData.startTime || null,
            recordData.endTime || null,
            recordData.description,
            recordData.result || null,
            recordData.reviewer || null,
            recordData.faultSeverity || null,
            JSON.stringify(recordData.customFields || {}),
            recordData.priority || 'normal',
            recordData.status || 'pending',
            recordData.assignedTo || null,
            recordData.completedBy || null,
            recordData.completedDate || null,
            recordData.cost || null,
            recordData.parts || null,
            recordData.solution || null,
            JSON.stringify(recordData.attachments || []),
            new Date().toISOString(),
            id
        );
    }

    deleteMaintenanceRecord(id) {
        const stmt = this.db.prepare('DELETE FROM maintenance_records WHERE id = ?');
        return stmt.run(id);
    }

    // ==================== 提醒设置相关操作 ====================

    getReminderSettings() {
        const stmt = this.db.prepare('SELECT * FROM reminder_settings');
        const settings = stmt.all();

        // 重构为原始格式
        const result = {
            priority: {},
            timeControl: {
                workingDays: { enabled: false, days: [], startTime: '09:00', endTime: '18:00' },
                customDates: { enabled: false, skipDates: [] }
            }
        };

        settings.forEach(setting => {
            result.priority[setting.priority] = {
                initialDelay: setting.initialDelay,
                normalInterval: setting.normalInterval,
                mediumInterval: setting.mediumInterval,
                urgentInterval: setting.urgentInterval
            };

            // 使用最后一条记录的时间控制设置
            result.timeControl.workingDays.enabled = setting.workingDaysEnabled;
            result.timeControl.workingDays.days = JSON.parse(setting.workingDays || '[]');
            result.timeControl.workingDays.startTime = setting.workingStartTime;
            result.timeControl.workingDays.endTime = setting.workingEndTime;
            result.timeControl.customDates.enabled = setting.customDatesEnabled;
            result.timeControl.customDates.skipDates = JSON.parse(setting.skipDates || '[]');
        });

        return result;
    }

    updateReminderSettings(settings) {
        // 删除现有设置
        const deleteStmt = this.db.prepare('DELETE FROM reminder_settings');
        deleteStmt.run();

        // 插入新设置
        const insertStmt = this.db.prepare(`
            INSERT INTO reminder_settings
            (priority, initialDelay, normalInterval, mediumInterval, urgentInterval,
             workingDaysEnabled, workingDays, workingStartTime, workingEndTime,
             customDatesEnabled, skipDates, updatedAt)
            VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
        `);

        const now = new Date().toISOString();
        Object.entries(settings.priority || {}).forEach(([priority, config]) => {
            insertStmt.run(
                priority,
                config.initialDelay,
                config.normalInterval,
                config.mediumInterval,
                config.urgentInterval,
                settings.timeControl?.workingDays?.enabled || false,
                JSON.stringify(settings.timeControl?.workingDays?.days || []),
                settings.timeControl?.workingDays?.startTime || '09:00',
                settings.timeControl?.workingDays?.endTime || '18:00',
                settings.timeControl?.customDates?.enabled || false,
                JSON.stringify(settings.timeControl?.customDates?.skipDates || []),
                now
            );
        });
    }

    // ==================== 维修模板相关操作 ====================

    getMaintenanceTemplates() {
        const stmt = this.db.prepare('SELECT * FROM maintenance_templates ORDER BY maintenanceType, id');
        const templates = stmt.all();

        // 重构为原始格式
        const result = {};
        templates.forEach(template => {
            if (!result[template.maintenanceType]) {
                result[template.maintenanceType] = [];
            }
            result[template.maintenanceType].push({
                field: template.field,
                label: template.label,
                type: template.type,
                required: template.required
            });
        });

        return result;
    }

    updateMaintenanceTemplates(templates) {
        // 删除现有模板
        const deleteStmt = this.db.prepare('DELETE FROM maintenance_templates');
        deleteStmt.run();

        // 插入新模板
        const insertStmt = this.db.prepare(`
            INSERT INTO maintenance_templates
            (maintenanceType, field, label, type, required, createdAt)
            VALUES (?, ?, ?, ?, ?, ?)
        `);

        const now = new Date().toISOString();
        Object.entries(templates).forEach(([maintenanceType, fields]) => {
            fields.forEach(field => {
                insertStmt.run(
                    maintenanceType,
                    field.field,
                    field.label,
                    field.type,
                    field.required || false,
                    now
                );
            });
        });
    }

    // ==================== 搜索和过滤方法 ====================

    searchApplications(filters = {}) {
        let sql = 'SELECT * FROM applications WHERE 1=1';
        const params = [];

        if (filters.applicant) {
            sql += ' AND applicant LIKE ?';
            params.push(`%${filters.applicant}%`);
        }

        if (filters.department) {
            sql += ' AND department = ?';
            params.push(filters.department);
        }

        if (filters.status) {
            sql += ' AND status = ?';
            params.push(filters.status);
        }

        if (filters.dateFrom) {
            sql += ' AND date >= ?';
            params.push(filters.dateFrom);
        }

        if (filters.dateTo) {
            sql += ' AND date <= ?';
            params.push(filters.dateTo);
        }

        sql += ' ORDER BY date DESC, id DESC';

        if (filters.limit) {
            sql += ` LIMIT ${filters.limit}`;
            if (filters.offset) {
                sql += ` OFFSET ${filters.offset}`;
            }
        }

        const stmt = this.db.prepare(sql);
        const applications = stmt.all(...params);

        // 解析JSON字段
        applications.forEach(app => {
            app.attachments = JSON.parse(app.attachments || '[]');
            app.reminderInfo = JSON.parse(app.reminderInfo || '{}');
            app.stageTimestamps = JSON.parse(app.stageTimestamps || '{}');
        });

        return applications;
    }

    searchDevices(filters = {}) {
        let sql = 'SELECT * FROM devices WHERE 1=1';
        const params = [];

        if (filters.deviceCode) {
            sql += ' AND deviceCode LIKE ?';
            params.push(`%${filters.deviceCode}%`);
        }

        if (filters.deviceName) {
            sql += ' AND deviceName LIKE ?';
            params.push(`%${filters.deviceName}%`);
        }

        if (filters.factory) {
            sql += ' AND factory = ?';
            params.push(filters.factory);
        }

        if (filters.status) {
            sql += ' AND status = ?';
            params.push(filters.status);
        }

        if (filters.responsible) {
            sql += ' AND responsible LIKE ?';
            params.push(`%${filters.responsible}%`);
        }

        sql += ' ORDER BY createTime DESC';

        const stmt = this.db.prepare(sql);
        return stmt.all(...params);
    }

    searchMaintenanceRecords(filters = {}) {
        let sql = 'SELECT * FROM maintenance_records WHERE 1=1';
        const params = [];

        if (filters.deviceCode) {
            sql += ' AND deviceCode LIKE ?';
            params.push(`%${filters.deviceCode}%`);
        }

        if (filters.factory) {
            sql += ' AND factory = ?';
            params.push(filters.factory);
        }

        if (filters.status) {
            sql += ' AND status = ?';
            params.push(filters.status);
        }

        if (filters.maintenanceType) {
            sql += ' AND maintenanceType = ?';
            params.push(filters.maintenanceType);
        }

        if (filters.reportedBy) {
            sql += ' AND reportedBy LIKE ?';
            params.push(`%${filters.reportedBy}%`);
        }

        if (filters.dateFrom) {
            sql += ' AND reportDate >= ?';
            params.push(filters.dateFrom);
        }

        if (filters.dateTo) {
            sql += ' AND reportDate <= ?';
            params.push(filters.dateTo);
        }

        sql += ' ORDER BY reportDate DESC, id DESC';

        if (filters.limit) {
            sql += ` LIMIT ${filters.limit}`;
            if (filters.offset) {
                sql += ` OFFSET ${filters.offset}`;
            }
        }

        const stmt = this.db.prepare(sql);
        const records = stmt.all(...params);

        // 解析JSON字段并映射字段名以保持前端兼容性
        records.forEach(record => {
            record.attachments = JSON.parse(record.attachments || '[]');
            record.customFields = JSON.parse(record.customFields || '{}');

            // 字段映射：SQLite字段名 -> 前端期望的字段名
            record.type = record.maintenanceType; // maintenanceType -> type
            record.date = record.reportDate; // reportDate -> date
            record.operator = record.reportedBy; // reportedBy -> operator

            // 如果recordCode为空，使用ID作为记录编号
            if (!record.recordCode) {
                record.recordCode = record.id;
            }
        });

        return records;
    }

    // ==================== 统计方法 ====================

    getApplicationStats() {
        const totalStmt = this.db.prepare('SELECT COUNT(*) as total FROM applications');
        const pendingStmt = this.db.prepare('SELECT COUNT(*) as pending FROM applications WHERE status LIKE ?');
        const approvedStmt = this.db.prepare('SELECT COUNT(*) as approved FROM applications WHERE status = ?');
        const rejectedStmt = this.db.prepare('SELECT COUNT(*) as rejected FROM applications WHERE status = ?');

        return {
            total: totalStmt.get().total,
            pending: pendingStmt.get('%待%').pending,
            approved: approvedStmt.get('已通过').approved,
            rejected: rejectedStmt.get('已拒绝').rejected
        };
    }

    getDeviceStats() {
        const totalStmt = this.db.prepare('SELECT COUNT(*) as total FROM devices');
        const activeStmt = this.db.prepare('SELECT COUNT(*) as active FROM devices WHERE status = ?');
        const inactiveStmt = this.db.prepare('SELECT COUNT(*) as inactive FROM devices WHERE status = ?');
        const factoryStmt = this.db.prepare('SELECT factory, COUNT(*) as count FROM devices GROUP BY factory');

        return {
            total: totalStmt.get().total,
            active: activeStmt.get('启用').active,
            inactive: inactiveStmt.get('停用').inactive,
            byFactory: factoryStmt.all()
        };
    }

    getMaintenanceStats() {
        const totalStmt = this.db.prepare('SELECT COUNT(*) as total FROM maintenance_records');
        const pendingStmt = this.db.prepare('SELECT COUNT(*) as pending FROM maintenance_records WHERE status = ?');
        const completedStmt = this.db.prepare('SELECT COUNT(*) as completed FROM maintenance_records WHERE status = ?');
        const typeStmt = this.db.prepare('SELECT maintenanceType, COUNT(*) as count FROM maintenance_records GROUP BY maintenanceType');

        return {
            total: totalStmt.get().total,
            pending: pendingStmt.get('pending').pending,
            completed: completedStmt.get('completed').completed,
            byType: typeStmt.all()
        };
    }

    // ==================== 批量操作方法 ====================

    batchUpdateApplicationStatus(applicationIds, status) {
        const placeholders = applicationIds.map(() => '?').join(',');
        const stmt = this.db.prepare(`
            UPDATE applications
            SET status = ?, updatedAt = ?
            WHERE id IN (${placeholders})
        `);

        return stmt.run(status, new Date().toISOString(), ...applicationIds);
    }

    batchDeleteApplications(applicationIds) {
        const deleteTransaction = this.db.transaction(() => {
            const placeholders = applicationIds.map(() => '?').join(',');

            const deleteApprovals = this.db.prepare(`DELETE FROM application_approvals WHERE applicationId IN (${placeholders})`);
            const deleteApplications = this.db.prepare(`DELETE FROM applications WHERE id IN (${placeholders})`);

            deleteApprovals.run(...applicationIds);
            deleteApplications.run(...applicationIds);
        });

        return deleteTransaction();
    }

    // ==================== 数据验证方法 ====================

    validateUserData(userData) {
        const errors = [];

        if (!userData.username || userData.username.trim() === '') {
            errors.push('用户名不能为空');
        }

        if (!userData.password || userData.password.trim() === '') {
            errors.push('密码不能为空');
        } else if (userData.password.length < 6) {
            errors.push('密码至少需要6位');
        }

        if (!userData.role || !['admin', 'manager', 'director', 'ceo', 'chief'].includes(userData.role)) {
            errors.push('角色无效');
        }

        if (!userData.userId || userData.userId.trim() === '') {
            errors.push('用户ID不能为空');
        }

        // 检查用户名是否已存在
        const existingUser = this.getUserByUsername(userData.username);
        if (existingUser) {
            errors.push('用户名已存在');
        }

        // 检查用户ID是否已存在
        const existingUserId = this.getUserByUserId(userData.userId);
        if (existingUserId) {
            errors.push('用户ID已存在');
        }

        return errors;
    }

    validateDeviceData(deviceData) {
        const errors = [];

        if (!deviceData.deviceCode || deviceData.deviceCode.trim() === '') {
            errors.push('设备编号不能为空');
        }

        if (!deviceData.deviceName || deviceData.deviceName.trim() === '') {
            errors.push('设备名称不能为空');
        }

        if (!deviceData.factory || deviceData.factory.trim() === '') {
            errors.push('所属工厂不能为空');
        }

        // 检查设备编号是否已存在
        const existingDevice = this.db.prepare('SELECT id FROM devices WHERE deviceCode = ?').get(deviceData.deviceCode);
        if (existingDevice) {
            errors.push('设备编号已存在');
        }

        return errors;
    }

    // ==================== 通用方法 ====================

    close() {
        this.dbManager.close();
    }

    // 执行事务
    transaction(callback) {
        return this.dbManager.transaction(callback);
    }

    // 获取数据库实例（用于复杂查询）
    getDatabase() {
        return this.db;
    }
}

module.exports = DataAccess;
