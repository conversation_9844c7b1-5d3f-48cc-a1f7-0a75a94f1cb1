/* 用户管理页面样式优化 - 现代化设计 */

/* 全局select样式重置 - 防止重复下拉图标 */
select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* 用户管理容器 */
.user-management-container {
    max-width: 100%;
    margin: 0 auto;
}

/* 用户统计卡片网格 */
.user-stats-grid {
    margin-bottom: 2rem;
}

.stat-card {
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
}

/* 高级筛选器样式 */
.advanced-filters {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    border: 1px solid #e2e8f0;
    border-radius: 12px;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.filter-group label {
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.5rem;
}

.filter-group select,
.filter-group input {
    transition: all 0.2s ease-in-out;
    border: 1px solid #d1d5db;
    border-radius: 8px;
    padding: 0.75rem;
    font-size: 0.875rem;
}

.filter-group select:focus,
.filter-group input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
    transform: translateY(-1px);
}

/* 批量操作栏 */
.batch-operation-bar {
    background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
    border: 1px solid #93c5fd;
    border-radius: 10px;
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.batch-operation-bar button {
    transition: all 0.2s ease-in-out;
    border-radius: 6px;
    font-weight: 500;
}

.batch-operation-bar button:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* 现代化表格样式 */
.user-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.user-table {
    width: 100%;
    border-collapse: collapse;
    border-spacing: 0;
}

.user-table thead {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
}

.user-table th {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: sticky;
    top: 0;
    z-index: 10;
}

.user-table td {
    vertical-align: middle;
    font-size: 0.875rem;
}

.user-table tbody tr {
    transition: all 0.2s ease-in-out;
}

.user-table tbody tr:hover {
    background-color: #f9fafb;
}

.user-table tbody tr:last-child td {
    border-bottom: none;
}

/* 表格单元格样式 */
#usersList td {
  vertical-align: middle;
  text-align: center;
}

#usersList th {
  background-color: #f3f4f6;
  vertical-align: middle;
  font-weight: 600;
  text-align: center;
}

/* 设置表格为固定布局，控制列宽 */
#manageUsersSection .user-table {
  table-layout: fixed;
  width: 100%;
  border-collapse: collapse;
  border-spacing: 0;
}

/* 设置各列的宽度 - 优化列宽分配，所有列居中对齐 */
#manageUsersSection th:nth-child(1),
#manageUsersSection td:nth-child(1) {
  width: 5%;
  text-align: center;
  min-width: 50px;
}

#manageUsersSection th:nth-child(2),
#manageUsersSection td:nth-child(2) {
  width: 18%;
  text-align: center;
}

#manageUsersSection th:nth-child(3),
#manageUsersSection td:nth-child(3) {
  width: 15%;
  text-align: center;
}

#manageUsersSection th:nth-child(4),
#manageUsersSection td:nth-child(4) {
  width: 12%;
  text-align: center;
}

#manageUsersSection th:nth-child(5),
#manageUsersSection td:nth-child(5) {
  width: 15%;
  text-align: center;
}

#manageUsersSection th:nth-child(6),
#manageUsersSection td:nth-child(6) {
  width: 20%;
  text-align: center;
}

#manageUsersSection th:nth-child(7),
#manageUsersSection td:nth-child(7) {
  width: 15%;
  text-align: center;
}

/* 美化表格行 */
#usersList tr:hover {
  background-color: #f9fafb;
}

/* 表格边框样式 - 只保留横线 */
#manageUsersSection table {
  border-radius: 0.375rem;
  overflow: hidden;
}

/* 确保表格内容垂直居中 */
#manageUsersSection td img {
  vertical-align: middle;
  display: inline-block;
}

/* 优化表格内容对齐 */
#manageUsersSection td {
  word-wrap: break-word;
  word-break: break-all;
}



/* 优化电子签名图片显示 */
#manageUsersSection td:nth-child(6) img {
  max-height: 40px;
  max-width: 80px;
  object-fit: contain;
  margin: 0 auto;
}

/* 优化操作按钮间距 */
#manageUsersSection td:nth-child(7) .flex {
  justify-content: center;
  align-items: center;
}

/* 确保复选框列紧凑 */
#manageUsersSection th:nth-child(1),
#manageUsersSection td:nth-child(1) {
  padding: 4px !important;
}

/* 优化表格行高 */
#manageUsersSection tr {
  height: 60px;
}

/* 优化表格整体布局 */
#manageUsersSection .overflow-x-auto {
  border-radius: 0.5rem;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

/* 恢复默认字体样式 */
#manageUsersSection th {
  font-weight: 600;
  color: #374151;
}

#manageUsersSection td {
  color: #1f2937;
}

/* 排序表头样式 - 与设备管理保持一致 */
.sortable {
    user-select: none;
    transition: background-color 0.15s ease-in-out;
}

.sortable:hover {
    background-color: #f3f4f6 !important;
}

.sortable svg {
    transition: color 0.15s ease-in-out;
}

/* 部门管理表格样式 */
.department-table-container {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    max-height: 400px;
    overflow-y: auto;
}

.department-table-container table {
    width: 100%;
    border-collapse: collapse;
    table-layout: fixed;
}

.department-table-container thead {
    background: linear-gradient(135deg, #f8fafc 0%, #f1f5f9 100%);
    position: sticky;
    top: 0;
    z-index: 10;
}

.department-table-container th {
    font-weight: 600;
    color: #374151;
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    padding: 8px;
    border-bottom: 2px solid #e5e7eb;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

.department-table-container td {
    vertical-align: middle;
    font-size: 0.875rem;
    padding: 8px;
    border-bottom: 1px solid #f3f4f6;
    overflow: hidden;
    text-overflow: ellipsis;
}

.department-table-container tbody tr {
    transition: all 0.2s ease-in-out;
}

.department-table-container tbody tr:hover {
    background-color: #f9fafb;
}

.department-table-container tbody tr:last-child td {
    border-bottom: none;
}

/* 部门描述列的特殊样式 */
.department-table-container td.max-w-xs {
    max-width: 200px;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
    cursor: help;
}

/* 操作按钮样式优化 */
.department-table-container .flex.space-x-1 button {
    font-size: 0.75rem;
    padding: 4px 8px;
    min-width: 40px;
}

/* 表格响应式处理 */
@media (max-width: 768px) {
  #manageUsersSection .min-w-full {
    width: 100%;
    table-layout: auto;
  }

  #manageUsersSection th,
  #manageUsersSection td {
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
  }

  #manageUsersSection th:nth-child(1),
  #manageUsersSection td:nth-child(1) {
    min-width: 50px;
  }

  #manageUsersSection th:nth-child(2),
  #manageUsersSection td:nth-child(2) {
    min-width: 120px;
  }

  #manageUsersSection th:nth-child(3),
  #manageUsersSection td:nth-child(3) {
    min-width: 80px;
  }

  #manageUsersSection th:nth-child(4),
  #manageUsersSection td:nth-child(4) {
    min-width: 70px;
  }

  #manageUsersSection th:nth-child(5),
  #manageUsersSection td:nth-child(5) {
    min-width: 80px;
  }

  #manageUsersSection th:nth-child(6),
  #manageUsersSection td:nth-child(6) {
    min-width: 100px;
  }

  #manageUsersSection th:nth-child(7),
  #manageUsersSection td:nth-child(7) {
    min-width: 80px;
  }
}

/* 现代化复选框样式 */
.user-checkbox {
    width: 1.125rem;
    height: 1.125rem;
    border: 2px solid #d1d5db;
    border-radius: 4px;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
}

.user-checkbox:checked {
    background-color: #3b82f6;
    border-color: #3b82f6;
    background-image: url("data:image/svg+xml,%3csvg viewBox='0 0 16 16' fill='white' xmlns='http://www.w3.org/2000/svg'%3e%3cpath d='m13.854 3.646-7.5 7.5a.5.5 0 0 1-.708 0l-3.5-3.5a.5.5 0 1 1 .708-.708L6 10.293l7.146-7.147a.5.5 0 0 1 .708.708z'/%3e%3c/svg%3e");
}

.user-checkbox:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.user-checkbox:indeterminate {
    background-color: #3b82f6;
    border-color: #3b82f6;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 16 16'%3e%3cpath stroke='white' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M4 8h8'/%3e%3c/svg%3e");
}

/* 现代化按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    font-weight: 500;
    font-size: 0.875rem;
    transition: all 0.2s ease-in-out;
    cursor: pointer;
    border: none;
    text-decoration: none;
    gap: 0.5rem;
}

.btn:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.btn:active {
    transform: translateY(0);
}

.btn-primary {
    background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%);
    color: white;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%);
}

.btn-danger {
    background: linear-gradient(135deg, #ef4444 0%, #dc2626 100%);
    color: white;
}

.btn-danger:hover {
    background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
}

.btn-success {
    background: linear-gradient(135deg, #10b981 0%, #059669 100%);
    color: white;
}

.btn-success:hover {
    background: linear-gradient(135deg, #059669 0%, #047857 100%);
}

.btn-secondary {
    background: linear-gradient(135deg, #6b7280 0%, #4b5563 100%);
    color: white;
}

.btn-secondary:hover {
    background: linear-gradient(135deg, #4b5563 0%, #374151 100%);
}

/* 编辑按钮样式 */
.edit-user-btn {
  background-color: #2563eb;
  color: white;
  padding: 0.375rem 0.75rem;
  border-radius: 0.375rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.edit-user-btn:hover {
  background-color: #1d4ed8;
}

/* 删除按钮样式 */
.delete-user-btn {
  background-color: #dc2626;
  color: white;
  width: 2rem;
  height: 2rem;
  border-radius: 9999px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 1.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
}

.delete-user-btn:hover {
  background-color: #b91c1c;
}

/* 加载指示器样式 */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
}

.loading-container {
  background-color: white;
  padding: 1rem;
  border-radius: 0.375rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
}

/* 加载动画 */
.spinner {
  width: 40px;
  height: 40px;
  margin: 0 auto;
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 现代化模态框样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 50;
  animation: fadeIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.modal-container {
  background: white;
  border-radius: 16px;
  padding: 2rem;
  width: 100%;
  max-width: 36rem;
  max-height: 90vh;
  overflow-y: auto;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: slideUp 0.3s ease-out;
  border: 1px solid #e5e7eb;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid #e5e7eb;
}

.modal-title {
  font-size: 1.5rem;
  font-weight: 700;
  color: #111827;
  margin: 0;
}

.modal-close {
  color: #6b7280;
  cursor: pointer;
  padding: 0.5rem;
  border-radius: 8px;
  transition: all 0.2s ease-in-out;
  font-size: 1.5rem;
  line-height: 1;
}

.modal-close:hover {
  color: #374151;
  background-color: #f3f4f6;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1.5rem;
  margin-bottom: 1.5rem;
}

.form-field {
  margin-bottom: 1.5rem;
}

.form-field label {
  display: block;
  font-weight: 600;
  color: #374151;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
}

.form-field input,
.form-field select,
.form-field textarea {
  width: 100%;
  padding: 0.75rem;
  border: 1px solid #d1d5db;
  border-radius: 8px;
  font-size: 0.875rem;
  transition: all 0.2s ease-in-out;
  background-color: white;
}

.form-field input:focus,
.form-field select:focus,
.form-field textarea:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
  transform: translateY(-1px);
}

.form-field input:invalid,
.form-field select:invalid {
  border-color: #ef4444;
}

.form-field input:invalid:focus,
.form-field select:invalid:focus {
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

.form-label {
  display: block;
  color: #374151;
  margin-bottom: 0.5rem;
}

.form-input,
.form-select {
  width: 100%;
  padding: 0.5rem;
  border: 1px solid #d1d5db;
  border-radius: 0.375rem;
}

.form-select {
  background-color: white;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 0.5rem center;
  background-repeat: no-repeat;
  background-size: 1.5em 1.5em;
  padding-right: 2.5rem;
  -webkit-appearance: none;
  -moz-appearance: none;
  appearance: none;
}

.form-input:focus,
.form-select:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.5);
  border-color: #3b82f6;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  margin-top: 1.5rem;
}

.btn-cancel {
  padding: 0.5rem 1rem;
  background-color: #d1d5db;
  color: #1f2937;
  border-radius: 0.375rem;
  cursor: pointer;
}

.btn-cancel:hover {
  background-color: #9ca3af;
}

.btn-save {
  padding: 0.5rem 1rem;
  background-color: #2563eb;
  color: white;
  border-radius: 0.375rem;
  cursor: pointer;
}

.btn-save:hover {
  background-color: #1d4ed8;
}

/* 签名预览样式 */
.signature-preview {
  margin-top: 0.5rem;
}

.signature-image {
  max-height: 4rem;
  /* 删除边框样式，避免电子签名显示黑框 */
}
