# 后端备份系统规则说明

## 📋 备份系统概述

当前系统使用了双层备份架构：**BackupManager（备份管理器）** + **BackupScheduler（备份调度器）**，提供自动化的数据备份和恢复功能。

## 🗂️ 备份目录结构

### 主要备份目录
- **backend/backups/**: 系统自动备份存储目录
- **backups/**: 根目录备份（当前不存在）

### 备份文件结构
```
backend/backups/
├── full_backup_2025-07-04T07-03-03-595Z/
│   ├── manifest.json           # 备份清单文件
│   ├── database/              # 数据库备份
│   │   └── application_system.db.backup
│   ├── config/                # 配置文件备份
│   ├── uploads/               # 上传文件备份（如果存在）
│   └── archive/               # 归档文件备份（如果存在）
└── full_backup_2025-07-04T07-13-16-152Z/
    └── ...
```

## ⏰ 备份调度规则

### 1. 自动备份时间表
```javascript
{
    // 月末备份时间（每月最后一天17:00）
    monthlyBackupTime: '17:00',

    // 是否启用自动备份
    autoBackup: true
}
```

### 2. 备份类型
- **月末完整备份**: 每月最后一天17:00执行
- **手动备份**: 通过API触发
- **首次备份**: 系统启动时如果从未备份过会执行一次

## 🗄️ 备份内容

### 1. 数据库备份
- **文件**: `application_system.db`
- **方法**: 使用SQLite的`VACUUM INTO`命令进行热备份
- **验证**: SHA256哈希校验

### 2. 上传文件备份
- **目录**: `uploads/`（如果存在）
- **内容**: 用户上传的所有文件

### 3. 归档文件备份
- **目录**: `archive/`（如果存在）
- **内容**: 历史归档数据

### 4. 配置文件备份
- **内容**: 系统配置文件
- **格式**: JSON格式

## 🧹 备份保留策略

### 1. 基础保留规则
```javascript
{
    // 自动清理旧备份
    autoCleanup: true
}
```

### 2. 月备份保留策略
```javascript
retention: {
    monthly: 24  // 保留24个月的月备份
}
```

### 3. 保留逻辑
- **按月保留**: 每个月只保留一个备份（最新的）
- **保留期限**: 保留最近24个月的备份
- **自动清理**: 超出24个月的备份自动删除

## 🔧 备份配置

### 1. 备份调度器配置
```javascript
{
    monthlyBackupTime: '17:00',  // 月末备份时间
    autoBackup: true,           // 启用自动备份
    retention: {
        monthly: 24             // 保留24个月的备份
    }
}
```

### 2. 备份管理器配置
```javascript
{
    enableCompression: true,     // 启用备份压缩
    enableVerification: true,    // 启用备份验证
    autoCleanup: true,          // 自动清理旧备份
    backupNameFormat: 'backup_{timestamp}'
}
```

### 2. 备份验证
- **完整性检查**: SHA256哈希验证
- **文件大小验证**: 确保备份文件完整
- **结构验证**: 检查备份目录结构

## 📊 备份统计信息

系统会跟踪以下统计数据：
```javascript
stats: {
    totalBackups: 0,        // 总备份数量
    lastBackupTime: null,   // 最后备份时间
    lastBackupSize: 0,      // 最后备份大小
    totalBackupSize: 0,     // 总备份大小
    successfulBackups: 0,   // 成功备份数
    failedBackups: 0        // 失败备份数
}
```

## 🌐 备份API接口

### 1. 手动备份
```
POST /api/system/backup/create
```

### 2. 列出备份
```
GET /api/system/backup/list
```

### 3. 恢复备份
```
POST /api/system/backup/restore
Body: { backupName: "backup_name", options: {} }
```

### 4. 备份统计
```
GET /api/system/backup/stats
```

### 5. 调度器状态
```
GET /api/system/backup-scheduler/status
```

### 6. 手动触发备份
```
POST /api/system/backup-scheduler/trigger
Body: { type: "manual" }
```

## 🔄 备份流程

### 1. 完整备份流程
1. 创建备份目录（时间戳命名）
2. 备份数据库（热备份）
3. 备份上传文件
4. 备份归档文件
5. 备份配置文件
6. 生成备份清单（manifest.json）
7. 验证备份完整性
8. 压缩备份（如果启用）
9. 更新统计信息
10. 清理旧备份（如果启用）

### 2. 增量备份流程
- 检查自上次备份以来的变更
- 仅备份变更的文件
- 更新增量备份记录

## ⚠️ 注意事项

1. **备份目录权限**: 确保备份目录有足够的读写权限
2. **磁盘空间**: 监控备份目录的磁盘空间使用情况
3. **备份验证**: 定期验证备份文件的完整性
4. **恢复测试**: 定期进行备份恢复测试
5. **网络备份**: 考虑将备份文件同步到远程存储

## 🚀 优化建议

1. **异地备份**: 配置远程备份存储
2. **压缩优化**: 根据文件类型选择最佳压缩算法
3. **增量备份**: 实现更精细的增量备份策略
4. **监控告警**: 添加备份失败的告警机制
5. **性能优化**: 在低峰期执行备份任务
