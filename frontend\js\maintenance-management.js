// 维修保养管理前端模块
class MaintenanceManagement {
    constructor() {
        this.records = [];
        this.devices = [];
        this.factories = [];
        this.templates = { maintenance: [], upkeep: [], '临时保养': [] };
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.filters = {};
        this.selectedRecords = new Set();
        this.searchTimeout = null;
        this.isInitialized = false;
        this.isLoading = false; // 防止重复加载
        this.refreshDebounceTimer = null; // 防抖定时器
        this.init();
    }

    // 防抖工具函数
    debounce(func, wait) {
        return (...args) => {
            clearTimeout(this.refreshDebounceTimer);
            this.refreshDebounceTimer = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // 初始化
    async init(recordType = '维修') {
        if (this.isInitialized) {
            return; // 避免重复初始化
        }
        this.recordType = recordType;

        // 初始化权限控制
        this.initPermissions();

        this.bindEvents();

        // 先加载基础数据，再加载记录
        await Promise.all([
            this.loadFactories(),
            this.loadDevices(),
            this.loadTemplates()
        ]);

        // 基础数据加载完成后再加载记录
        // 第一次初始化时不显示全屏加载器，避免页面切换时的闪烁
        this.loadRecords(false);
        this.isInitialized = true;
    }

    // 初始化权限控制
    initPermissions() {
        // 获取当前用户信息
        this.currentUser = sessionStorage.getItem('username');
        this.currentRole = sessionStorage.getItem('role');
        this.currentDepartment = sessionStorage.getItem('department');

        // 根据权限控制按钮显示
        this.updateButtonPermissions();
    }

    // 更新按钮权限
    updateButtonPermissions() {
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';
        const canAddRecord = isAdmin || isElectricalDept;

        // 维修保养记录管理按钮权限控制
        const addRecordBtn = document.getElementById('addMaintenanceRecordBtn');
        const batchDeleteBtn = document.getElementById('batchDeleteRecordsBtn');
        const exportRecordsBtn = document.getElementById('exportMaintenanceBtn');

        if (addRecordBtn) {
            if (canAddRecord) {
                addRecordBtn.style.display = '';
                addRecordBtn.style.visibility = 'visible';
            } else {
                addRecordBtn.style.display = 'none';
            }
        }
        if (batchDeleteBtn) {
            if (canAddRecord) {
                batchDeleteBtn.style.display = '';
                batchDeleteBtn.style.visibility = 'visible';
            } else {
                batchDeleteBtn.style.display = 'none';
            }
        }
        if (exportRecordsBtn) {
            // 所有用户都可以导出维修保养记录
            exportRecordsBtn.style.display = '';
            exportRecordsBtn.style.visibility = 'visible';
        }
    }

    // 检查是否可以编辑记录
    canEditRecord(record) {
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';
        const isOperator = record.operator === this.currentUser;

        return isAdmin || (isElectricalDept && isOperator);
    }

    // 检查是否可以删除记录
    canDeleteRecord(record) {
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';
        const isOperator = record.operator === this.currentUser;

        return isAdmin || (isElectricalDept && isOperator);
    }

    // 绑定事件
    bindEvents() {
        // 搜索功能 - 添加防抖机制
        const searchInput = document.getElementById('maintenanceSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.currentPage = 1;

                // 清除之前的搜索超时
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }

                // 延迟搜索，避免频繁请求
                this.searchTimeout = setTimeout(() => {
                    this.loadRecords(false); // 搜索时不显示loading
                }, 300);
            });
        }

        // 类型筛选
        const typeFilter = document.getElementById('maintenanceTypeFilter');
        if (typeFilter) {
            typeFilter.addEventListener('change', (e) => {
                this.filters.type = e.target.value;
                this.currentPage = 1;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(typeFilter);
                this.loadRecords(false).finally(() => {
                    this.hideFilterLoading(typeFilter);
                });
            });
        }

        // 厂区筛选
        const factoryFilter = document.getElementById('maintenanceFactoryFilter');
        if (factoryFilter) {
            factoryFilter.addEventListener('change', (e) => {
                this.filters.factory = e.target.value;
                this.currentPage = 1;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(factoryFilter);
                this.loadRecords(false).finally(() => {
                    this.hideFilterLoading(factoryFilter);
                });

                // 更新设备搜索组件的厂区过滤
                if (window.filterDeviceSearchInstance) {
                    window.filterDeviceSearchInstance.setFactoryFilter(e.target.value);
                }
            });
        }

        // 初始化筛选器设备搜索组件
        if (!window.filterDeviceSearchInstance) {
            window.filterDeviceSearchInstance = new FilterDeviceSearch(
                'maintenanceDeviceSearch',
                'maintenanceDeviceFilter',
                'maintenanceDeviceSearchResults'
            );

            // 设置设备选择变化回调
            window.filterDeviceSearchInstance.setOnSelectionChange((deviceId) => {
                this.filters.deviceId = deviceId;
                this.currentPage = 1;
                this.loadRecords();
            });
        }

        // 操作人筛选 - 添加防抖机制
        const operatorFilter = document.getElementById('maintenanceOperatorFilter');
        if (operatorFilter) {
            operatorFilter.addEventListener('input', (e) => {
                this.filters.operator = e.target.value;
                this.currentPage = 1;

                // 清除之前的搜索超时
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }

                // 延迟搜索，避免频繁请求
                this.searchTimeout = setTimeout(() => {
                    this.loadRecords(false); // 搜索时不显示loading
                }, 300);
            });
        }

        // 日期范围筛选
        const startDateFilter = document.getElementById('maintenanceStartDate');
        const endDateFilter = document.getElementById('maintenanceEndDate');

        if (startDateFilter) {
            startDateFilter.addEventListener('change', (e) => {
                this.filters.startDate = e.target.value;
                this.currentPage = 1;
                this.loadRecords();
            });
        }

        if (endDateFilter) {
            endDateFilter.addEventListener('change', (e) => {
                this.filters.endDate = e.target.value;
                this.currentPage = 1;
                this.loadRecords();
            });
        }

        // 添加记录按钮 - 防止重复绑定
        const addRecordBtn = document.getElementById('addMaintenanceRecordBtn');
        if (addRecordBtn) {
            addRecordBtn.removeEventListener('click', this.addRecordHandler);
            this.addRecordHandler = () => this.showAddRecordModal();
            addRecordBtn.addEventListener('click', this.addRecordHandler);
        }

        // 批量删除按钮 - 防止重复绑定
        const batchDeleteBtn = document.getElementById('batchDeleteRecordsBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.removeEventListener('click', this.batchDeleteHandler);
            this.batchDeleteHandler = () => this.batchDeleteRecords();
            batchDeleteBtn.addEventListener('click', this.batchDeleteHandler);
        }

        // 导出记录按钮 - 防止重复绑定
        const exportRecordsBtn = document.getElementById('exportMaintenanceBtn');
        if (exportRecordsBtn) {
            // 移除之前的事件监听器（如果存在）
            exportRecordsBtn.removeEventListener('click', this.exportRecordsHandler);
            // 创建绑定的处理函数
            this.exportRecordsHandler = () => this.exportRecords();
            exportRecordsBtn.addEventListener('click', this.exportRecordsHandler);
        }

        // 刷新按钮 - 添加防抖机制
        const refreshRecordsBtn = document.getElementById('refreshMaintenanceBtn');
        if (refreshRecordsBtn) {
            refreshRecordsBtn.removeEventListener('click', this.debouncedMaintenanceRefresh);

            // 创建防抖刷新函数
            this.debouncedMaintenanceRefresh = this.debounce(() => {
                if (this.isLoading) {
                    console.log('维修保养记录正在加载中，跳过重复请求');
                    return;
                }
                console.log('执行维修保养记录数据刷新');
                // 使用按钮内的加载动画，不显示全屏遮罩
                this.showButtonLoading(refreshRecordsBtn, '刷新中...');
                this.loadRecords(false).finally(() => {
                    this.hideButtonLoading(refreshRecordsBtn, '刷新');
                });
            }, 1000); // 1秒防抖

            refreshRecordsBtn.addEventListener('click', this.debouncedMaintenanceRefresh);
        }

        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAllRecords');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => this.toggleSelectAll(e.target.checked));
        }

        // 记录类型变化事件
        const recordTypeSelect = document.getElementById('recordType');
        if (recordTypeSelect) {
            recordTypeSelect.addEventListener('change', (e) => {
                this.updateCustomFields(e.target.value);
                this.toggleFaultSeverityField(e.target.value);
                // 类型变化时重新生成记录编号
                const factorySelect = document.getElementById('recordFactory');
                if (factorySelect && factorySelect.value) {
                    this.generateRecordCode(factorySelect.value);
                }
            });
        }

        // 厂区选择变化事件
        const factorySelect = document.getElementById('recordFactory');
        if (factorySelect) {
            factorySelect.addEventListener('change', (e) => {
                // 厂区选择后自动生成记录编号
                this.generateRecordCode(e.target.value);

                // 更新位置选择
                this.updateLocationSelect(e.target.value);
            });
        }

        // 位置选择变化事件
        const locationSelect = document.getElementById('recordLocation');
        if (locationSelect) {
            locationSelect.addEventListener('change', (e) => {
                // 更新设备选择
                this.updateDeviceSelect(e.target.value);
            });
        }
    }

    // 加载厂区列表
    async loadFactories() {
        try {
            const response = await apiRequest('/api/factories');
            if (response.success) {
                this.factories = response.factories;
                this.updateFactorySelects();
            } else {
                console.error('加载厂区列表失败:', response.message);
            }
        } catch (error) {
            console.error('加载厂区列表失败:', error);
        }
    }

    // 重新加载厂区数据（供厂区管理调用）
    async reloadFactories() {
        await this.loadFactories();
    }

    // 更新厂区选择框
    updateFactorySelects() {
        // 更新记录表单中的厂区选择
        const factorySelect = document.getElementById('recordFactory');
        if (factorySelect) {
            factorySelect.innerHTML = '<option value="">请选择厂区</option>' +
                this.factories.map(factory =>
                    `<option value="${factory.id}">${factory.name}</option>`
                ).join('');
        }

        // 更新筛选器中的厂区选择
        const maintenanceFactoryFilter = document.getElementById('maintenanceFactoryFilter');
        if (maintenanceFactoryFilter) {
            maintenanceFactoryFilter.innerHTML = '<option value="">全部厂区</option>' +
                this.factories.map(factory =>
                    `<option value="${factory.id}">${factory.name}</option>`
                ).join('');
        }
    }

    // 更新位置选择框
    updateLocationSelect(factoryId) {
        const locationSelect = document.getElementById('recordLocation');
        const deviceSelect = document.getElementById('recordDevice');

        if (!locationSelect) return;

        if (!factoryId) {
            locationSelect.innerHTML = '<option value="">请先选择厂区</option>';
            locationSelect.disabled = true;

            // 重置设备选择
            if (deviceSelect) {
                deviceSelect.innerHTML = '<option value="">请先选择位置</option>';
                deviceSelect.disabled = true;
            }
            return;
        }

        // 获取该厂区下的所有位置
        const factoryDevices = this.devices.filter(device => device.factory === factoryId);
        const locations = [...new Set(factoryDevices.map(device => device.location))].filter(Boolean);

        if (locations.length === 0) {
            locationSelect.innerHTML = '<option value="">该厂区暂无设备位置</option>';
            locationSelect.disabled = true;
        } else {
            locationSelect.innerHTML = '<option value="">请选择位置</option>' +
                locations.map(location => `<option value="${location}">${location}</option>`).join('');
            locationSelect.disabled = false;
        }

        // 重置设备选择
        if (deviceSelect) {
            deviceSelect.innerHTML = '<option value="">请先选择位置</option>';
            deviceSelect.disabled = true;
        }
    }

    // 更新设备选择框
    updateDeviceSelect(location) {
        const deviceSelect = document.getElementById('recordDevice');
        const factorySelect = document.getElementById('recordFactory');

        if (!deviceSelect || !factorySelect) return;

        const factoryId = factorySelect.value;

        if (!location || !factoryId) {
            deviceSelect.innerHTML = '<option value="">请先选择位置</option>';
            deviceSelect.disabled = true;
            return;
        }

        // 获取该厂区和位置下的所有设备
        const locationDevices = this.devices.filter(device =>
            device.factory === factoryId && device.location === location && device.status === '启用'
        );

        if (locationDevices.length === 0) {
            deviceSelect.innerHTML = '<option value="">该位置暂无可用设备</option>';
            deviceSelect.disabled = true;
        } else {
            deviceSelect.innerHTML = '<option value="">请选择设备</option>' +
                locationDevices.map(device =>
                    `<option value="${device.id}">${device.deviceCode} - ${device.deviceName}</option>`
                ).join('');
            deviceSelect.disabled = false;
        }
    }



    // 生成记录编号
    async generateRecordCode(factoryId) {
        const recordCodeInput = document.getElementById('recordCode');
        if (!recordCodeInput) return;

        if (!factoryId) {
            recordCodeInput.value = '';
            recordCodeInput.placeholder = '选择厂区后自动生成';
            return;
        }

        try {
            // 获取记录类型
            const recordType = document.getElementById('recordType')?.value || '维修';

            // 调用后端API生成记录编号
            const response = await apiRequest('/api/maintenance-records/generate-code', {
                method: 'POST',
                data: {
                    factoryId: factoryId,
                    type: recordType
                }
            });

            if (response.success) {
                recordCodeInput.value = response.recordCode;
                recordCodeInput.placeholder = '';
            } else {
                console.error('生成记录编号失败:', response.message);
                recordCodeInput.value = '';
                recordCodeInput.placeholder = '生成编号失败';
            }
        } catch (error) {
            console.error('生成记录编号失败:', error);
            recordCodeInput.value = '';
            recordCodeInput.placeholder = '生成编号失败';
        }
    }

    // 加载设备列表
    async loadDevices() {
        try {
            const response = await apiRequest('/api/devices');
            if (response.success) {
                this.devices = response.devices || [];
                console.log('设备数据加载成功:', this.devices.length, '台设备');

                // 刷新设备搜索组件的数据
                if (window.maintenanceDeviceSearchInstance) {
                    window.maintenanceDeviceSearchInstance.refresh();
                }
                if (window.filterDeviceSearchInstance) {
                    window.filterDeviceSearchInstance.refresh();
                }
            } else {
                console.error('加载设备列表失败:', response.message);
                this.devices = [];
            }
        } catch (error) {
            console.error('加载设备列表失败:', error);
            this.devices = [];
        }
    }

    // 加载模板
    async loadTemplates() {
        try {
            const response = await apiRequest('/api/maintenance-templates');
            if (response.success) {
                this.templates = response.templates;
            }
        } catch (error) {
            console.error('加载模板失败:', error);
        }
    }

    // 切换故障程度字段的显示/隐藏
    toggleFaultSeverityField(type) {
        const faultSeverityGroup = document.getElementById('faultSeverityGroup');
        const faultSeveritySelect = document.getElementById('faultSeverity');

        if (faultSeverityGroup && faultSeveritySelect) {
            if (type === '维修') {
                faultSeverityGroup.style.display = 'block';
                faultSeveritySelect.required = true;
            } else {
                faultSeverityGroup.style.display = 'none';
                faultSeveritySelect.required = false;
                faultSeveritySelect.value = ''; // 清空选择
            }
        }
    }

    // 更新自定义字段
    updateCustomFields(type) {
        const container = document.getElementById('customFieldsContainer');
        if (!container) return;

        const fields = this.templates[type] || [];

        if (fields.length === 0) {
            container.innerHTML = '';
            return;
        }

        container.innerHTML = fields.map(field => {
            let inputHtml = '';
            switch (field.type) {
                case 'text':
                    inputHtml = `<input type="text" id="custom_${field.field}" name="custom_${field.field}"
                                class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                                ${field.required ? 'required' : ''}>`;
                    break;
                case 'number':
                    inputHtml = `<input type="number" id="custom_${field.field}" name="custom_${field.field}"
                                class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                                ${field.required ? 'required' : ''}>`;
                    break;
                case 'date':
                    inputHtml = `<input type="date" id="custom_${field.field}" name="custom_${field.field}"
                                class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                                ${field.required ? 'required' : ''}>`;
                    break;
                case 'textarea':
                    inputHtml = `<textarea id="custom_${field.field}" name="custom_${field.field}" rows="3"
                                class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                                ${field.required ? 'required' : ''}></textarea>`;
                    break;
                default:
                    inputHtml = `<input type="text" id="custom_${field.field}" name="custom_${field.field}"
                                class="w-full p-2 border rounded-md focus:ring-2 focus:ring-blue-500"
                                ${field.required ? 'required' : ''}>`;
            }

            return `
                <div class="mb-4">
                    <label class="block text-gray-700 mb-2">
                        ${field.label}${field.required ? ' *' : ''}
                    </label>
                    ${inputHtml}
                </div>
            `;
        }).join('');
    }

    // 加载记录列表
    async loadRecords(showLoadingIndicator = true) {
        if (this.isLoading) {
            console.log('维修保养记录正在加载中，跳过重复请求');
            return;
        }

        try {
            this.isLoading = true;
            console.log('开始加载维修保养记录数据');

            if (showLoadingIndicator) {
                showLoading('正在加载维修/保养记录...');
            }

            const queryParams = new URLSearchParams();
            Object.keys(this.filters).forEach(key => {
                if (this.filters[key]) {
                    queryParams.append(key, this.filters[key]);
                }
            });

            const response = await apiRequest(`/api/maintenance-records?${queryParams.toString()}`);

            if (response.success) {
                this.records = response.records;
                this.updateRecordTable();
                this.updatePagination();
                this.updateSelectedCount();
                console.log('维修保养记录数据加载成功');
            } else {
                showError('加载维修/保养记录失败: ' + response.message);
            }
        } catch (error) {
            console.error('加载维修/保养记录失败:', error);
            showError('加载维修/保养记录失败，请检查网络连接');
        } finally {
            this.isLoading = false;
            if (showLoadingIndicator) {
                hideLoading();
            }
        }
    }

    // 更新记录表格
    updateRecordTable() {
        const tbody = document.getElementById('maintenanceTableBody');
        if (!tbody) return;

        if (this.records.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="8" class="px-6 py-4 text-center text-gray-500">
                        暂无维修/保养记录
                    </td>
                </tr>
            `;
            return;
        }

        // 计算分页
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageRecords = this.records.slice(startIndex, endIndex);
        this.totalPages = Math.ceil(this.records.length / this.pageSize);

        // 创建设备映射
        const deviceMap = {};
        this.devices.forEach(device => {
            deviceMap[device.id] = device;
        });

        // 调试信息：输出设备映射状态
        console.log('设备映射状态:', {
            totalDevices: this.devices.length,
            deviceMapSize: Object.keys(deviceMap).length,
            sampleDeviceIds: this.devices.slice(0, 3).map(d => d.id),
            sampleRecordDeviceIds: pageRecords.slice(0, 3).map(r => r.deviceId)
        });

        tbody.innerHTML = pageRecords.map(record => {
            const device = deviceMap[record.deviceId] || {};

            // 调试信息：检查设备映射
            if (!device.deviceCode && !device.deviceName) {
                console.warn('设备信息缺失:', {
                    recordId: record.id,
                    deviceId: record.deviceId,
                    deviceIdType: typeof record.deviceId,
                    availableDevices: this.devices.length,
                    deviceMapKeys: Object.keys(deviceMap).slice(0, 5),
                    exactMatch: deviceMap.hasOwnProperty(record.deviceId)
                });
            }

            // 改进设备显示逻辑
            let deviceDisplay = '';
            if (device.deviceCode && device.deviceName) {
                deviceDisplay = `${device.deviceCode} - ${device.deviceName}`;
            } else if (device.deviceName) {
                deviceDisplay = device.deviceName;
            } else if (device.deviceCode) {
                deviceDisplay = device.deviceCode;
            } else {
                // 如果设备信息完全缺失，尝试从记录中获取设备信息
                deviceDisplay = record.deviceName || `设备ID: ${record.deviceId || '未知'}`;
            }

            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox"
                               class="record-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                               value="${record.id}"
                               ${this.selectedRecords.has(record.id) ? 'checked' : ''}
                               onchange="maintenanceManager.toggleRecordSelection('${record.id}', this.checked)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${record.recordCode || '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${deviceDisplay}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        <span class="px-2 inline-block text-xs leading-5 font-semibold rounded-full ${
                            record.type === '维修'
                                ? 'bg-red-100 text-red-800'
                                : record.type === '保养'
                                ? 'bg-blue-100 text-blue-800'
                                : 'bg-green-100 text-green-800'
                        }">
                            ${record.type}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${record.date || '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${record.startTime ? this.formatTimeOnly(record.startTime) : '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${record.endTime ? this.formatTimeOnly(record.endTime) : '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${record.operator || '-'}
                    </td>
                    <td class="px-6 py-4 text-sm text-gray-900 max-w-xs truncate">
                        ${record.description || '-'}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="maintenanceManager.viewRecord('${record.id}')"
                                class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                        ${this.canEditRecord(record) ? `
                        <button onclick="maintenanceManager.editRecord('${record.id}')"
                                class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                        ` : ''}
                        ${this.canDeleteRecord(record) ? `
                        <button onclick="maintenanceManager.deleteRecord('${record.id}')"
                                class="text-red-600 hover:text-red-900">删除</button>
                        ` : ''}
                    </td>
                </tr>
            `;
        }).join('');
    }

    // 更新分页
    updatePagination() {
        const pagination = document.getElementById('maintenancePagination');
        if (!pagination) return;

        const totalItems = this.records.length;

        pagination.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    共 <span id="maintenanceTotalItems">${totalItems}</span> 条记录，每页 ${this.pageSize} 条
                </div>
                <div class="flex space-x-2">
                    <button id="maintenancePrevPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed" ${this.currentPage <= 1 ? 'disabled' : ''}>上一页</button>
                    <div id="maintenancePageNumbers" class="flex space-x-1"></div>
                    <button id="maintenanceNextPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed" ${this.currentPage >= this.totalPages ? 'disabled' : ''}>下一页</button>
                </div>
            </div>
        `;

        // 设置按钮点击事件
        document.getElementById('maintenancePrevPage').onclick = () => this.goToPage(this.currentPage - 1);
        document.getElementById('maintenanceNextPage').onclick = () => this.goToPage(this.currentPage + 1);

        // 生成页码按钮
        this.generatePageNumbers();
    }

    // 生成页码按钮
    generatePageNumbers() {
        const pageNumbersContainer = document.getElementById('maintenancePageNumbers');
        if (!pageNumbersContainer) return;

        pageNumbersContainer.innerHTML = '';

        // 确定要显示的页码范围
        let startPage = Math.max(1, this.currentPage - 2);
        let endPage = Math.min(this.totalPages, startPage + 4);

        // 调整起始页，确保始终显示5个页码（如果有足够的页数）
        if (endPage - startPage < 4 && this.totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        // 添加第一页按钮（如果不在显示范围内）
        if (startPage > 1) {
            const firstPageBtn = document.createElement('button');
            firstPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            firstPageBtn.textContent = '1';
            firstPageBtn.onclick = () => this.goToPage(1);
            pageNumbersContainer.appendChild(firstPageBtn);

            // 添加省略号（如果第一页和起始页之间有间隔）
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }
        }

        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `px-3 py-1 border rounded-md ${i === this.currentPage ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbersContainer.appendChild(pageBtn);
        }

        // 添加最后一页按钮（如果不在显示范围内）
        if (endPage < this.totalPages) {
            // 添加省略号（如果结束页和最后一页之间有间隔）
            if (endPage < this.totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }

            const lastPageBtn = document.createElement('button');
            lastPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            lastPageBtn.textContent = this.totalPages;
            lastPageBtn.onclick = () => this.goToPage(this.totalPages);
            pageNumbersContainer.appendChild(lastPageBtn);
        }
    }

    // 跳转到指定页面
    goToPage(page) {
        if (page < 1 || page > this.totalPages) return;
        this.currentPage = page;
        this.updateRecordTable();
        this.updatePagination();
    }

    // 切换记录选择状态
    toggleRecordSelection(recordId, checked) {
        if (checked) {
            this.selectedRecords.add(recordId);
        } else {
            this.selectedRecords.delete(recordId);
        }
        this.updateSelectedCount();
        this.updateSelectAllCheckbox();
    }

    // 切换全选状态
    toggleSelectAll(checked) {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageRecords = this.records.slice(startIndex, endIndex);

        pageRecords.forEach(record => {
            if (checked) {
                this.selectedRecords.add(record.id);
            } else {
                this.selectedRecords.delete(record.id);
            }
        });

        // 更新页面上的复选框
        document.querySelectorAll('.record-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });

        this.updateSelectedCount();
    }

    // 更新全选复选框状态
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAllRecords');
        if (!selectAllCheckbox) return;

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageRecords = this.records.slice(startIndex, endIndex);

        const selectedPageRecords = pageRecords.filter(record => this.selectedRecords.has(record.id));

        if (selectedPageRecords.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedPageRecords.length === pageRecords.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    // 更新选中数量显示
    updateSelectedCount() {
        const countElement = document.getElementById('selectedRecordCount');
        if (countElement) {
            const count = this.selectedRecords.size;
            countElement.textContent = count > 0 ? `已选择 ${count} 条记录` : '';
        }

        // 更新批量操作按钮状态
        const batchDeleteBtn = document.getElementById('batchDeleteRecordsBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = this.selectedRecords.size === 0;
            batchDeleteBtn.classList.toggle('opacity-50', this.selectedRecords.size === 0);
        }
    }

    // 显示添加记录模态框
    showAddRecordModal() {
        // 权限检查：只有管理员和机电部用户可以添加记录
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';

        if (!isAdmin && !isElectricalDept) {
            showError('权限不足，只有管理员和机电部用户可以添加维修保养记录');
            return;
        }

        this.showRecordModal();
    }

    // 查看记录详情
    async viewRecord(recordId) {
        try {
            const response = await apiRequest(`/api/maintenance-records/${recordId}`);
            if (response.success) {
                this.showRecordModal(response.record, true);
            } else {
                showError('获取记录详情失败: ' + response.message);
            }
        } catch (error) {
            console.error('获取记录详情失败:', error);
            showError('获取记录详情失败');
        }
    }

    // 编辑记录
    async editRecord(recordId) {
        try {
            const response = await apiRequest(`/api/maintenance-records/${recordId}`);
            if (response.success) {
                const record = response.record;

                // 权限检查：管理员可以编辑所有记录，机电部用户只能编辑自己创建的记录
                const isAdmin = this.currentRole === 'admin';
                const isElectricalDept = this.currentDepartment === '机电部';
                const isOperator = record.operator === this.currentUser;

                if (!isAdmin && (!isElectricalDept || !isOperator)) {
                    if (!isElectricalDept) {
                        showError('权限不足，只有管理员和机电部用户可以编辑维修保养记录');
                    } else {
                        showError('权限不足，您只能编辑自己创建的记录');
                    }
                    return;
                }

                this.showRecordModal(record, false);
            } else {
                showError('获取记录信息失败: ' + response.message);
            }
        } catch (error) {
            console.error('获取记录信息失败:', error);
            showError('获取记录信息失败');
        }
    }

    // 删除记录
    async deleteRecord(recordId) {
        // 先获取记录信息以检查权限
        try {
            const response = await apiRequest(`/api/maintenance-records/${recordId}`);
            if (response.success) {
                const record = response.record;

                // 权限检查：管理员可以删除所有记录，机电部用户只能删除自己创建的记录
                const isAdmin = this.currentRole === 'admin';
                const isElectricalDept = this.currentDepartment === '机电部';
                const isOperator = record.operator === this.currentUser;

                if (!isAdmin && (!isElectricalDept || !isOperator)) {
                    if (!isElectricalDept) {
                        showError('权限不足，只有管理员和机电部用户可以删除维修保养记录');
                    } else {
                        showError('权限不足，您只能删除自己创建的记录');
                    }
                    return;
                }
            } else {
                showError('获取记录信息失败: ' + response.message);
                return;
            }
        } catch (error) {
            console.error('获取记录信息失败:', error);
            showError('获取记录信息失败');
            return;
        }

        if (!confirm('确定要删除这条记录吗？删除后无法恢复。')) {
            return;
        }

        try {
            showLoading('正在删除记录...');
            const response = await apiRequest(`/api/maintenance-records/${recordId}`, {
                method: 'DELETE',
                data: {
                    username: this.currentUser,
                    role: this.currentRole,
                    department: this.currentDepartment
                }
            });

            if (response.success) {
                showSuccess(response.message);
                this.selectedRecords.delete(recordId);
                this.loadRecords();
            } else {
                showError('删除记录失败: ' + response.message);
            }
        } catch (error) {
            console.error('删除记录失败:', error);
            showError('删除记录失败');
        } finally {
            hideLoading();
        }
    }

    // 批量删除记录
    async batchDeleteRecords() {
        // 权限检查：只有管理员和机电部用户可以批量删除记录
        const isAdmin = this.currentRole === 'admin';
        const isElectricalDept = this.currentDepartment === '机电部';

        if (!isAdmin && !isElectricalDept) {
            showError('权限不足，只有管理员和机电部用户可以删除维修保养记录');
            return;
        }

        if (this.selectedRecords.size === 0) {
            showError('请先选择要删除的记录');
            return;
        }

        if (!confirm(`确定要删除选中的 ${this.selectedRecords.size} 条记录吗？删除后无法恢复。`)) {
            return;
        }

        try {
            showLoading('正在批量删除记录...');
            const response = await apiRequest('/api/maintenance-records', {
                method: 'DELETE',
                data: {
                    ids: Array.from(this.selectedRecords),
                    username: this.currentUser,
                    role: this.currentRole,
                    department: this.currentDepartment
                }
            });

            if (response.success) {
                showSuccess(response.message);
                this.selectedRecords.clear();
                this.loadRecords();
            } else {
                showError('批量删除记录失败: ' + response.message);
            }
        } catch (error) {
            console.error('批量删除记录失败:', error);
            showError('批量删除记录失败');
        } finally {
            hideLoading();
        }
    }

    // 导出记录
    async exportRecords() {
        try {
            showLoading('正在导出维修保养记录...');

            const filename = `维修保养记录_${new Date().toISOString().slice(0, 10)}.xlsx`;

            const response = await fetch('/api/export/maintenance', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filters: this.filters,
                    filename: filename
                })
            });

            if (response.ok) {
                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let downloadFilename = filename;
                if (contentDisposition) {
                    const matches = contentDisposition.match(/filename="(.+)"/);
                    if (matches) {
                        downloadFilename = decodeURIComponent(matches[1]);
                    }
                }

                // 下载文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = downloadFilename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                // 记录导出历史（如果导出管理器存在）
                if (window.exportManager) {
                    // 构建范围信息
                    let rangeInfo = '维修保养记录';
                    if (this.filters) {
                        const filterParts = [];
                        if (this.filters.type && this.filters.type !== 'all') {
                            filterParts.push(`类型: ${this.filters.type}`);
                        }
                        if (this.filters.factory) {
                            filterParts.push(`厂区: ${this.filters.factory}`);
                        }
                        if (this.filters.deviceId) {
                            const device = this.devices.find(d => d.id === this.filters.deviceId);
                            if (device) {
                                filterParts.push(`设备: ${device.deviceCode} - ${device.deviceName}`);
                            }
                        }
                        if (this.filters.startDate || this.filters.endDate) {
                            const dateRange = [];
                            if (this.filters.startDate) dateRange.push(`从 ${this.filters.startDate}`);
                            if (this.filters.endDate) dateRange.push(`到 ${this.filters.endDate}`);
                            filterParts.push(dateRange.join(' '));
                        }
                        if (filterParts.length > 0) {
                            rangeInfo += ` (${filterParts.join(', ')})`;
                        }
                    }

                    window.exportManager.addExportHistory('维修保养记录', downloadFilename, rangeInfo, blob.size);
                }

                showSuccess('维修保养记录导出成功！');
            } else {
                const errorData = await response.json();
                showError('导出记录失败: ' + (errorData.message || '未知错误'));
            }
        } catch (error) {
            console.error('导出记录失败:', error);
            showError('导出记录失败');
        } finally {
            hideLoading();
        }
    }

    // 显示记录模态框
    showRecordModal(record = null, readonly = false) {
        const modal = document.getElementById('maintenanceModal');
        const form = document.getElementById('maintenanceForm');
        const title = document.getElementById('maintenanceModalTitle');

        if (!modal || !form || !title) return;

        // 设置标题
        if (readonly) {
            title.textContent = '记录详情';
        } else if (record) {
            title.textContent = '编辑记录';
        } else {
            title.textContent = '添加记录';
        }

        // 重置表单
        form.reset();

        // 填充表单数据
        if (record) {
            // 设置记录编号
            document.getElementById('recordCode').value = record.recordCode || '';

            // 设置厂区、位置和设备选择
            const selectedDevice = this.devices.find(device => device.id === record.deviceId);
            if (selectedDevice) {
                // 设置厂区
                document.getElementById('recordFactory').value = selectedDevice.factory || record.factory || '';

                // 更新位置选择
                this.updateLocationSelect(selectedDevice.factory || record.factory);

                // 设置位置
                setTimeout(() => {
                    document.getElementById('recordLocation').value = selectedDevice.location || '';

                    // 更新设备选择
                    this.updateDeviceSelect(selectedDevice.location);

                    // 设置设备
                    setTimeout(() => {
                        document.getElementById('recordDevice').value = record.deviceId || '';
                    }, 100);
                }, 100);
            } else if (record.factory) {
                // 如果设备信息不存在但有厂区信息，直接设置厂区
                document.getElementById('recordFactory').value = record.factory;
                this.updateLocationSelect(record.factory);
            }

            document.getElementById('recordType').value = record.type || '维修';
            document.getElementById('recordDate').value = record.date || '';
            document.getElementById('recordStartTime').value = record.startTime || '';
            document.getElementById('recordEndTime').value = record.endTime || '';
            // 设置操作人员字段
            const operatorField = document.getElementById('recordOperator');
            if (operatorField) {
                operatorField.value = record.operator || '';
                // 如果是编辑模式且不是管理员，操作人员字段应该只读
                if (!readonly && this.currentRole !== 'admin') {
                    operatorField.readOnly = true;
                    operatorField.style.backgroundColor = '#f3f4f6';
                }
            }
            document.getElementById('recordDescription').value = record.description || '';
            document.getElementById('recordResult').value = record.result || '';
            document.getElementById('recordReviewer').value = record.reviewer || '';

            // 设置故障程度字段
            const faultSeveritySelect = document.getElementById('faultSeverity');
            if (faultSeveritySelect) {
                faultSeveritySelect.value = record.faultSeverity || '';
            }

            // 更新自定义字段和故障程度字段显示
            this.updateCustomFields(record.type || '维修');
            this.toggleFaultSeverityField(record.type || '维修');

            // 填充自定义字段数据
            if (record.customFields) {
                Object.keys(record.customFields).forEach(key => {
                    const field = document.getElementById(`custom_${key}`);
                    if (field) {
                        field.value = record.customFields[key];
                    }
                });
            }
        } else {
            // 重置记录编号
            document.getElementById('recordCode').value = '';
            document.getElementById('recordCode').placeholder = '选择厂区后自动生成';

            // 重置厂区、位置和设备选择
            document.getElementById('recordFactory').value = '';

            const locationSelect = document.getElementById('recordLocation');
            if (locationSelect) {
                locationSelect.innerHTML = '<option value="">请先选择厂区</option>';
                locationSelect.disabled = true;
            }

            const deviceSelect = document.getElementById('recordDevice');
            if (deviceSelect) {
                deviceSelect.innerHTML = '<option value="">请先选择位置</option>';
                deviceSelect.disabled = true;
            }

            // 设置默认值
            document.getElementById('recordDate').value = new Date().toISOString().slice(0, 10);

            // 设置操作人员为当前登录用户，并设为只读
            const operatorField = document.getElementById('recordOperator');
            if (operatorField) {
                operatorField.value = this.currentUser || '';
                operatorField.readOnly = true;
                operatorField.style.backgroundColor = '#f3f4f6'; // 灰色背景表示只读
            }

            // 重置故障程度字段
            const faultSeveritySelect = document.getElementById('faultSeverity');
            if (faultSeveritySelect) {
                faultSeveritySelect.value = '';
            }

            this.updateCustomFields('维修');
            this.toggleFaultSeverityField('维修');
        }

        // 设置只读状态
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.disabled = readonly;
        });



        // 设置按钮状态
        const saveBtn = document.getElementById('saveMaintenanceBtn');
        const cancelBtn = document.getElementById('cancelMaintenanceBtn');

        if (saveBtn) {
            saveBtn.style.display = readonly ? 'none' : 'block';
            saveBtn.textContent = record ? '更新记录' : '添加记录';
        }

        if (cancelBtn) {
            cancelBtn.textContent = readonly ? '关闭' : '取消';
        }

        // 绑定保存事件
        if (!readonly) {
            const newSaveBtn = saveBtn.cloneNode(true);
            saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

            newSaveBtn.addEventListener('click', () => {
                this.saveRecord(record ? record.id : null);
            });
        }

        // 显示模态框
        modal.classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 隐藏记录模态框
    hideRecordModal() {
        const modal = document.getElementById('maintenanceModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.classList.remove('modal-open');
        }
    }

    // 保存记录
    async saveRecord(recordId = null) {
        const form = document.getElementById('maintenanceForm');
        if (!form) return;

        const formData = new FormData(form);

        // 收集自定义字段数据
        const customFields = {};
        const customInputs = form.querySelectorAll('[name^="custom_"]');
        customInputs.forEach(input => {
            const fieldName = input.name.replace('custom_', '');
            customFields[fieldName] = input.value;
        });

        const recordData = {
            recordCode: formData.get('recordCode'),
            factory: formData.get('factory'),
            location: formData.get('location'),
            deviceId: formData.get('deviceId'),
            type: formData.get('type'),
            faultSeverity: formData.get('faultSeverity'), // 添加故障程度字段
            date: formData.get('date'),
            startTime: formData.get('startTime'),
            endTime: formData.get('endTime'),
            operator: formData.get('operator'),
            description: formData.get('description'),
            result: formData.get('result'),
            reviewer: formData.get('reviewer'), // 添加审查人员字段
            customFields: customFields
        };

        // 验证必填字段
        if (!recordData.deviceId || !recordData.type || !recordData.date ||
            !recordData.startTime || !recordData.endTime ||
            !recordData.operator || !recordData.description) {
            showError('请填写所有必填字段');
            return;
        }

        // 验证维修记录的故障程度
        if (recordData.type === '维修' && !recordData.faultSeverity) {
            showError('维修记录必须选择故障程度');
            return;
        }

        // 验证时间逻辑
        if (recordData.startTime && recordData.endTime) {
            const startTime = new Date(recordData.startTime);
            const endTime = new Date(recordData.endTime);
            if (startTime >= endTime) {
                showError('结束时间必须晚于开始时间');
                return;
            }
        }

        try {
            showLoading(recordId ? '正在更新记录...' : '正在添加记录...');

            // 添加用户权限信息
            recordData.username = this.currentUser;
            recordData.role = this.currentRole;
            recordData.department = this.currentDepartment;

            const url = recordId ? `/api/maintenance-records/${recordId}` : '/api/maintenance-records';
            const method = recordId ? 'PUT' : 'POST';

            const response = await apiRequest(url, {
                method: method,
                data: recordData
            });

            if (response.success) {
                showSuccess(response.message);
                this.hideRecordModal();
                this.loadRecords();
            } else {
                showError(response.message);
            }
        } catch (error) {
            console.error('保存记录失败:', error);
            showError('保存记录失败');
        } finally {
            hideLoading();
        }
    }

    // 格式化日期时间
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return '-';
        try {
            const date = new Date(dateTimeString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            console.error('日期格式化失败:', error);
            return dateTimeString;
        }
    }

    // 格式化时间（只显示时分，不显示日期）- 用于维修保养记录明细区
    formatTimeOnly(dateTimeString) {
        if (!dateTimeString) return '-';
        try {
            const date = new Date(dateTimeString);
            return date.toLocaleString('zh-CN', {
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            console.error('时间格式化失败:', error);
            return dateTimeString;
        }
    }

    // 显示筛选器加载状态
    showFilterLoading(selectElement) {
        if (!selectElement) return;

        selectElement.disabled = true;
        selectElement.style.opacity = '0.6';

        // 添加加载指示器
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'filter-loading-indicator';
        loadingIndicator.innerHTML = `
            <div class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600">加载中...</span>
            </div>
        `;
        loadingIndicator.style.cssText = `
            position: absolute;
            top: 50%;
            right: 30px;
            transform: translateY(-50%);
            z-index: 10;
            pointer-events: none;
        `;

        // 将指示器添加到父容器
        const parent = selectElement.parentElement;
        if (parent) {
            parent.style.position = 'relative';
            parent.appendChild(loadingIndicator);
        }
    }

    // 隐藏筛选器加载状态
    hideFilterLoading(selectElement) {
        if (!selectElement) return;

        selectElement.disabled = false;
        selectElement.style.opacity = '1';

        // 移除加载指示器
        const parent = selectElement.parentElement;
        if (parent) {
            const loadingIndicator = parent.querySelector('.filter-loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }
    }

    // 显示按钮加载状态
    showButtonLoading(buttonElement, loadingText = '加载中...') {
        if (!buttonElement) return;

        // 保存原始状态
        buttonElement.dataset.originalText = buttonElement.textContent;
        buttonElement.dataset.originalDisabled = buttonElement.disabled;

        // 设置加载状态
        buttonElement.disabled = true;
        buttonElement.innerHTML = `
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            ${loadingText}
        `;
    }

    // 隐藏按钮加载状态
    hideButtonLoading(buttonElement, originalText = null) {
        if (!buttonElement) return;

        // 恢复原始状态
        const savedText = buttonElement.dataset.originalText;
        const savedDisabled = buttonElement.dataset.originalDisabled === 'true';

        buttonElement.textContent = originalText || savedText || '操作';
        buttonElement.disabled = savedDisabled;

        // 清理数据属性
        delete buttonElement.dataset.originalText;
        delete buttonElement.dataset.originalDisabled;
    }
}

// 全局维修保养管理实例
let maintenanceManager = null;

// 初始化维修保养管理
function initMaintenanceManagement() {
    if (!maintenanceManager) {
        maintenanceManager = new MaintenanceManagement();
    }
    return maintenanceManager;
}
