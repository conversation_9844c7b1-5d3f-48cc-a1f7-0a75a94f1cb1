// 防抖工具函数
// 用于防止用户频繁点击按钮或触发事件

/**
 * 创建一个防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 防抖延迟时间（毫秒）
 * @param {boolean} immediate - 是否立即执行（第一次调用时）
 * @returns {Function} 防抖后的函数
 */
function createDebounce(func, wait, immediate = false) {
    let timeout;
    
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) func.apply(this, args);
        };
        
        const callNow = immediate && !timeout;
        
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        
        if (callNow) func.apply(this, args);
    };
}

/**
 * 为按钮添加防抖机制
 * @param {string|HTMLElement} buttonSelector - 按钮选择器或按钮元素
 * @param {Function} callback - 点击回调函数
 * @param {number} wait - 防抖延迟时间（毫秒），默认1000ms
 * @param {Object} options - 选项
 * @param {string} options.loadingText - 加载时显示的文本
 * @param {boolean} options.disableOnClick - 点击时是否禁用按钮
 */
function addButtonDebounce(buttonSelector, callback, wait = 1000, options = {}) {
    const button = typeof buttonSelector === 'string' 
        ? document.querySelector(buttonSelector) 
        : buttonSelector;
    
    if (!button) {
        console.warn('按钮元素未找到:', buttonSelector);
        return;
    }

    const {
        loadingText = '处理中...',
        disableOnClick = true
    } = options;

    let isProcessing = false;
    let originalText = button.textContent;
    let originalDisabled = button.disabled;

    const debouncedCallback = createDebounce(async (...args) => {
        if (isProcessing) {
            console.log('操作正在进行中，跳过重复请求');
            return;
        }

        try {
            isProcessing = true;
            
            if (disableOnClick) {
                button.disabled = true;
                button.textContent = loadingText;
            }

            await callback.apply(this, args);
        } catch (error) {
            console.error('按钮回调执行失败:', error);
        } finally {
            isProcessing = false;
            
            if (disableOnClick) {
                button.disabled = originalDisabled;
                button.textContent = originalText;
            }
        }
    }, wait);

    // 移除之前的事件监听器（如果存在）
    if (button._debouncedHandler) {
        button.removeEventListener('click', button._debouncedHandler);
    }

    // 添加新的事件监听器
    button._debouncedHandler = debouncedCallback;
    button.addEventListener('click', debouncedCallback);

    return debouncedCallback;
}

/**
 * 为刷新按钮添加防抖机制的便捷函数
 * @param {string|HTMLElement} buttonSelector - 刷新按钮选择器或元素
 * @param {Function} refreshCallback - 刷新回调函数
 * @param {number} wait - 防抖延迟时间（毫秒），默认1000ms
 */
function addRefreshButtonDebounce(buttonSelector, refreshCallback, wait = 1000) {
    return addButtonDebounce(buttonSelector, refreshCallback, wait, {
        loadingText: '刷新中...',
        disableOnClick: true
    });
}

/**
 * 节流函数 - 限制函数在指定时间内只能执行一次
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 节流时间间隔（毫秒）
 * @returns {Function} 节流后的函数
 */
function throttle(func, limit) {
    let inThrottle;
    
    return function(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => inThrottle = false, limit);
        }
    };
}

// 导出函数供其他模块使用
if (typeof module !== 'undefined' && module.exports) {
    module.exports = {
        createDebounce,
        addButtonDebounce,
        addRefreshButtonDebounce,
        throttle
    };
} else {
    // 浏览器环境下挂载到全局对象
    window.DebounceUtils = {
        createDebounce,
        addButtonDebounce,
        addRefreshButtonDebounce,
        throttle
    };
}
