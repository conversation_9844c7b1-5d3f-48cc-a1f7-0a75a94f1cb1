/**
 * 部门管理模块 - 独立JavaScript文件
 * 遵循模块化设计原则，提供完整的部门管理功能
 */

class DepartmentManager {
    constructor() {
        this.departments = [];
        this.isLoading = false;
        this.currentEditingId = null;
        
        // 当前用户信息
        this.currentUser = sessionStorage.getItem('username') || '';
        this.currentRole = sessionStorage.getItem('role') || '';
    }

    /**
     * 初始化部门管理模块
     */
    init() {
        this.bindEvents();
        this.loadDepartments();
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 部门表单提交 - 防止重复绑定
        const departmentForm = document.getElementById('departmentForm');
        if (departmentForm && !departmentForm.hasAttribute('data-bound')) {
            departmentForm.addEventListener('submit', (e) => this.handleAddDepartment(e));
            departmentForm.setAttribute('data-bound', 'true');
        }

        // 编辑部门表单提交 - 防止重复绑定
        const editDepartmentForm = document.getElementById('editDepartmentFormElement');
        if (editDepartmentForm && !editDepartmentForm.hasAttribute('data-bound')) {
            editDepartmentForm.addEventListener('submit', (e) => this.handleEditDepartment(e));
            editDepartmentForm.setAttribute('data-bound', 'true');
        }

        // 搜索功能 - 防止重复绑定
        const searchInput = document.getElementById('departmentSearchInput');
        if (searchInput && !searchInput.hasAttribute('data-bound')) {
            searchInput.addEventListener('input', (e) => this.handleSearch(e.target.value));
            searchInput.setAttribute('data-bound', 'true');
        }
    }

    /**
     * 加载部门数据
     */
    async loadDepartments() {
        if (this.currentRole !== 'admin') {
            console.warn('权限不足，无法加载部门数据');
            return;
        }

        try {
            this.showLoadingState();
            
            const response = await apiRequest('/departments');
            if (response.success) {
                this.departments = response.data || [];
                this.renderDepartmentTable();
            } else {
                this.showErrorMessage(response.message || '加载部门数据失败');
            }
        } catch (error) {
            console.error('加载部门数据失败:', error);
            this.showErrorMessage('加载部门数据失败');
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 渲染部门表格
     */
    renderDepartmentTable() {
        const tbody = document.getElementById('departmentTableBody');
        if (!tbody) return;

        if (this.departments.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="p-4 text-center text-gray-500">暂无部门数据</td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = this.departments.map(dept => `
            <tr class="hover:bg-gray-50">
                <td class="p-2 border-b border-gray-200 whitespace-nowrap">${this.escapeHtml(dept.name)}</td>
                <td class="p-2 border-b border-gray-200 whitespace-nowrap">${this.escapeHtml(dept.code || '-')}</td>
                <td class="p-2 border-b border-gray-200 max-w-xs" title="${this.escapeHtml(dept.description || '-')}">${this.truncateDescription(dept.description)}</td>
                <td class="p-2 text-center border-b border-gray-200 whitespace-nowrap">${dept.userCount || 0}</td>
                <td class="p-2 text-center border-b border-gray-200 whitespace-nowrap">
                    <div class="flex justify-center space-x-1">
                        <button onclick="window.departmentManager?.editDepartment('${dept.id}')"
                                class="text-blue-600 hover:text-blue-800 px-2 py-1 rounded hover:bg-blue-50 transition-colors text-sm whitespace-nowrap">
                            编辑
                        </button>
                        <button onclick="window.departmentManager?.deleteDepartment('${dept.id}', '${this.escapeHtml(dept.name)}')"
                                class="text-red-600 hover:text-red-800 px-2 py-1 rounded hover:bg-red-50 transition-colors text-sm whitespace-nowrap">
                            删除
                        </button>
                    </div>
                </td>
            </tr>
        `).join('');
    }

    /**
     * 截断描述文本
     */
    truncateDescription(description) {
        if (!description) return '-';
        if (description.length <= 20) return this.escapeHtml(description);
        return this.escapeHtml(description.substring(0, 20) + '...');
    }

    /**
     * 处理添加部门
     */
    async handleAddDepartment(event) {
        event.preventDefault();
        
        const formData = new FormData(event.target);
        const departmentData = {
            name: formData.get('name').trim(),
            code: formData.get('code').trim(),
            description: formData.get('description').trim()
        };

        if (!departmentData.name) {
            this.showErrorMessage('部门名称不能为空');
            return;
        }

        try {
            const response = await apiRequest('/departments', {
                method: 'POST',
                data: {
                    ...departmentData,
                    adminUsername: this.currentUser
                }
            });

            if (response.success) {
                this.showSuccessMessage('部门添加成功');
                this.hideAddDepartmentForm();
                this.resetAddForm();
                await this.loadDepartments();

                // 通知用户管理模块更新部门选项
                if (window.userManager && typeof window.userManager.updateDepartmentOptions === 'function') {
                    window.userManager.updateDepartmentOptions();
                }
            } else {
                this.showErrorMessage(response.message || '添加部门失败');
            }
        } catch (error) {
            console.error('添加部门失败:', error);
            this.showErrorMessage('添加部门失败');
        }
    }

    /**
     * 处理编辑部门
     */
    async handleEditDepartment(event) {
        event.preventDefault();
        
        const departmentId = document.getElementById('editDepartmentId').value;
        const formData = new FormData(event.target);
        const departmentData = {
            name: formData.get('name').trim(),
            code: formData.get('code').trim(),
            description: formData.get('description').trim()
        };

        if (!departmentData.name) {
            this.showErrorMessage('部门名称不能为空');
            return;
        }

        try {
            const response = await apiRequest(`/departments/${departmentId}`, {
                method: 'PUT',
                data: {
                    ...departmentData,
                    adminUsername: this.currentUser
                }
            });

            if (response.success) {
                this.showSuccessMessage('部门更新成功');
                this.hideEditDepartmentForm();
                await this.loadDepartments();
                
                // 通知用户管理模块更新部门选项
                if (window.userManager && typeof window.userManager.updateDepartmentOptions === 'function') {
                    window.userManager.updateDepartmentOptions();
                }
            } else {
                this.showErrorMessage(response.message || '更新部门失败');
            }
        } catch (error) {
            console.error('更新部门失败:', error);
            this.showErrorMessage('更新部门失败');
        }
    }

    /**
     * 编辑部门
     */
    editDepartment(departmentId) {
        const department = this.departments.find(d => d.id === departmentId);
        if (!department) {
            this.showErrorMessage('部门不存在');
            return;
        }

        // 填充编辑表单
        document.getElementById('editDepartmentId').value = department.id;
        document.getElementById('editDepartmentName').value = department.name;
        document.getElementById('editDepartmentCode').value = department.code || '';
        document.getElementById('editDepartmentDescription').value = department.description || '';

        // 显示编辑表单
        this.showEditDepartmentForm();
    }

    /**
     * 删除部门
     */
    async deleteDepartment(departmentId, departmentName) {
        const confirmed = await this.showConfirmDialog(
            `确定要删除部门"${departmentName}"吗？\n\n注意：删除部门前请确保没有用户属于该部门。`
        );

        if (!confirmed) return;

        try {
            const response = await apiRequest(`/departments/${departmentId}`, {
                method: 'DELETE',
                data: {
                    adminUsername: this.currentUser
                }
            });

            if (response.success) {
                this.showSuccessMessage('部门删除成功');
                await this.loadDepartments();

                // 通知用户管理模块更新部门选项
                if (window.userManager && typeof window.userManager.updateDepartmentOptions === 'function') {
                    window.userManager.updateDepartmentOptions();
                }
            } else {
                this.showErrorMessage(response.message || '删除部门失败');
            }
        } catch (error) {
            console.error('删除部门失败:', error);
            this.showErrorMessage('删除部门失败');
        }
    }

    /**
     * 处理搜索功能
     */
    handleSearch(searchTerm) {
        const filteredDepartments = this.departments.filter(dept => 
            dept.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
            (dept.code && dept.code.toLowerCase().includes(searchTerm.toLowerCase())) ||
            (dept.description && dept.description.toLowerCase().includes(searchTerm.toLowerCase()))
        );

        // 临时保存原始数据
        const originalDepartments = this.departments;
        this.departments = filteredDepartments;
        this.renderDepartmentTable();
        this.departments = originalDepartments;
    }

    /**
     * 显示添加部门表单
     */
    showAddDepartmentForm() {
        const form = document.getElementById('addDepartmentForm');
        if (form) {
            form.classList.remove('hidden');
            document.getElementById('departmentName').focus();
        }
    }

    /**
     * 隐藏添加部门表单
     */
    hideAddDepartmentForm() {
        const form = document.getElementById('addDepartmentForm');
        if (form) {
            form.classList.add('hidden');
        }
    }

    /**
     * 显示编辑部门表单
     */
    showEditDepartmentForm() {
        const form = document.getElementById('editDepartmentForm');
        if (form) {
            form.classList.remove('hidden');
            document.getElementById('editDepartmentName').focus();
        }
    }

    /**
     * 隐藏编辑部门表单
     */
    hideEditDepartmentForm() {
        const form = document.getElementById('editDepartmentForm');
        if (form) {
            form.classList.add('hidden');
        }
    }

    /**
     * 重置添加表单
     */
    resetAddForm() {
        const form = document.getElementById('departmentForm');
        if (form) {
            form.reset();
        }
    }

    /**
     * 显示加载状态
     */
    showLoadingState() {
        const tbody = document.getElementById('departmentTableBody');
        if (tbody) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="5" class="p-4 text-center text-gray-500">正在加载部门数据...</td>
                </tr>
            `;
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        // 加载状态会在渲染表格时自动隐藏
    }

    /**
     * HTML转义
     */
    escapeHtml(text) {
        if (!text) return '';
        const div = document.createElement('div');
        div.textContent = text;
        return div.innerHTML;
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message) {
        if (typeof showSuccess === 'function') {
            showSuccess(message);
        } else {
            console.log('成功:', message);
            alert(message);
        }
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        if (typeof showError === 'function') {
            showError(message);
        } else {
            console.error('错误:', message);
            alert(message);
        }
    }

    /**
     * 显示确认对话框
     */
    async showConfirmDialog(message) {
        return new Promise((resolve) => {
            if (confirm(message)) {
                resolve(true);
            } else {
                resolve(false);
            }
        });
    }
}

// 全局部门管理器实例
let departmentManager = null;

// 初始化部门管理模块
function initDepartmentManagement() {
    if (!departmentManager) {
        departmentManager = new DepartmentManager();
    }
    return departmentManager;
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { DepartmentManager, initDepartmentManagement };
}
