const crypto = require('crypto');
const bcrypt = require('bcrypt');

/**
 * 密码策略管理器
 * 实施密码复杂度要求和安全策略
 */
class PasswordPolicy {
    constructor(logger) {
        this.logger = logger;
        
        // 简化的密码策略配置
        this.config = {
            // 密码长度要求
            minLength: 6,                // 最小6位
            maxLength: 128,

            // 复杂度要求（全部关闭，只验证长度）
            requireUppercase: false,
            requireLowercase: false,
            requireNumbers: false,
            requireSpecialChars: false,

            // 账户锁定策略
            maxFailedAttempts: 5,        // 最大失败尝试次数
            lockoutDuration: 30,         // 锁定时长（分钟）

            // 密码重置
            resetTokenExpiry: 60,        // 重置令牌有效期（分钟）

            // 弱密码检查（关闭）
            checkCommonPasswords: false,
            checkUserInfo: false
        };
        
        // 常见弱密码列表（简化版）
        this.commonPasswords = new Set([
            'password', '123456', '123456789', 'qwerty', 'abc123',
            'password123', 'admin', 'root', 'user', 'guest',
            '111111', '000000', '123123', 'welcome', 'login'
        ]);
        
        // 用户登录失败记录
        this.failedAttempts = new Map();
        
        // 密码重置令牌
        this.resetTokens = new Map();
    }

    /**
     * 验证密码复杂度（简化版：只验证长度）
     */
    validatePasswordComplexity(password, userInfo = {}) {
        const errors = [];
        const warnings = [];

        // 只检查长度
        if (!password || password.length < this.config.minLength) {
            errors.push(`密码长度至少需要${this.config.minLength}位`);
        }

        if (password && password.length > this.config.maxLength) {
            errors.push(`密码长度不能超过${this.config.maxLength}位`);
        }

        return {
            isValid: errors.length === 0,
            errors,
            warnings,
            strength: {
                score: password && password.length >= this.config.minLength ? 60 : 0,
                level: password && password.length >= this.config.minLength ? 'acceptable' : 'weak',
                description: password && password.length >= this.config.minLength ? '符合基本要求' : '密码太短'
            }
        };
    }

    /**
     * 计算密码强度
     */
    calculatePasswordStrength(password) {
        let score = 0;
        let level = 'weak';
        
        // 长度加分
        score += Math.min(password.length * 2, 20);
        
        // 字符类型加分
        if (/[a-z]/.test(password)) score += 5;
        if (/[A-Z]/.test(password)) score += 5;
        if (/\d/.test(password)) score += 5;
        if (/[^a-zA-Z0-9]/.test(password)) score += 10;
        
        // 字符多样性加分
        const uniqueChars = new Set(password).size;
        score += uniqueChars * 2;
        
        // 模式检查减分
        if (this.hasRepeatingChars(password, 2)) score -= 10;
        if (this.hasKeyboardSequence(password)) score -= 15;
        if (this.commonPasswords.has(password.toLowerCase())) score -= 25;
        
        // 确定强度等级
        if (score >= 80) level = 'very_strong';
        else if (score >= 60) level = 'strong';
        else if (score >= 40) level = 'medium';
        else if (score >= 20) level = 'weak';
        else level = 'very_weak';
        
        return {
            score: Math.max(0, Math.min(100, score)),
            level,
            description: this.getStrengthDescription(level)
        };
    }

    /**
     * 获取强度描述
     */
    getStrengthDescription(level) {
        const descriptions = {
            'very_weak': '非常弱 - 极易被破解',
            'weak': '弱 - 容易被破解',
            'medium': '中等 - 有一定安全性',
            'strong': '强 - 安全性良好',
            'very_strong': '非常强 - 安全性极佳'
        };
        return descriptions[level] || '未知';
    }

    /**
     * 检查重复字符
     */
    hasRepeatingChars(password, maxRepeats) {
        for (let i = 0; i <= password.length - maxRepeats; i++) {
            const char = password[i];
            let count = 1;
            
            for (let j = i + 1; j < password.length && password[j] === char; j++) {
                count++;
            }
            
            if (count >= maxRepeats) {
                return true;
            }
        }
        return false;
    }

    /**
     * 检查键盘序列
     */
    hasKeyboardSequence(password) {
        const sequences = [
            'qwertyuiop', 'asdfghjkl', 'zxcvbnm',
            '1234567890', 'abcdefghijklmnopqrstuvwxyz'
        ];
        
        const lowerPassword = password.toLowerCase();
        
        for (const sequence of sequences) {
            for (let i = 0; i <= sequence.length - 3; i++) {
                const subseq = sequence.substring(i, i + 3);
                if (lowerPassword.includes(subseq) || lowerPassword.includes(subseq.split('').reverse().join(''))) {
                    return true;
                }
            }
        }
        
        return false;
    }

    /**
     * 转义正则表达式特殊字符
     */
    escapeRegex(string) {
        return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
    }

    /**
     * 生成简单密码（6-12位数字和字母组合）
     */
    generateSecurePassword(length = 8) {
        const charset = 'abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        let password = '';

        // 确保长度至少为6位
        const finalLength = Math.max(length, this.config.minLength);

        // 生成随机密码
        for (let i = 0; i < finalLength; i++) {
            password += this.getRandomChar(charset);
        }

        return password;
    }

    /**
     * 获取随机字符
     */
    getRandomChar(charset) {
        const randomIndex = crypto.randomInt(0, charset.length);
        return charset[randomIndex];
    }

    /**
     * 记录登录失败
     */
    recordFailedAttempt(username, ip) {
        const key = `${username}:${ip}`;
        const now = Date.now();
        
        if (!this.failedAttempts.has(key)) {
            this.failedAttempts.set(key, {
                count: 0,
                firstAttempt: now,
                lastAttempt: now,
                lockedUntil: null
            });
        }
        
        const attempts = this.failedAttempts.get(key);
        attempts.count++;
        attempts.lastAttempt = now;
        
        // 检查是否需要锁定账户
        if (attempts.count >= this.config.maxFailedAttempts) {
            attempts.lockedUntil = now + (this.config.lockoutDuration * 60 * 1000);
            
            this.logger.security('Account locked due to failed attempts', {
                username,
                ip,
                attempts: attempts.count,
                lockedUntil: new Date(attempts.lockedUntil).toISOString()
            });
        }
        
        return attempts;
    }

    /**
     * 检查账户是否被锁定
     */
    isAccountLocked(username, ip) {
        const key = `${username}:${ip}`;
        const attempts = this.failedAttempts.get(key);
        
        if (!attempts || !attempts.lockedUntil) {
            return false;
        }
        
        const now = Date.now();
        if (now > attempts.lockedUntil) {
            // 锁定期已过，清除记录
            this.failedAttempts.delete(key);
            return false;
        }
        
        return {
            locked: true,
            unlockTime: new Date(attempts.lockedUntil).toISOString(),
            remainingTime: Math.ceil((attempts.lockedUntil - now) / 1000 / 60) // 分钟
        };
    }

    /**
     * 清除失败尝试记录
     */
    clearFailedAttempts(username, ip) {
        const key = `${username}:${ip}`;
        this.failedAttempts.delete(key);
    }

    /**
     * 生成密码重置令牌
     */
    generateResetToken(username) {
        const token = crypto.randomBytes(32).toString('hex');
        const expiry = Date.now() + (this.config.resetTokenExpiry * 60 * 1000);
        
        this.resetTokens.set(token, {
            username,
            expiry,
            used: false
        });
        
        // 清理过期令牌
        this.cleanupExpiredTokens();
        
        return token;
    }

    /**
     * 验证重置令牌
     */
    validateResetToken(token) {
        const tokenData = this.resetTokens.get(token);
        
        if (!tokenData) {
            return { valid: false, reason: 'invalid_token' };
        }
        
        if (tokenData.used) {
            return { valid: false, reason: 'token_used' };
        }
        
        if (Date.now() > tokenData.expiry) {
            this.resetTokens.delete(token);
            return { valid: false, reason: 'token_expired' };
        }
        
        return { valid: true, username: tokenData.username };
    }

    /**
     * 使用重置令牌
     */
    useResetToken(token) {
        const tokenData = this.resetTokens.get(token);
        if (tokenData) {
            tokenData.used = true;
        }
    }

    /**
     * 清理过期令牌
     */
    cleanupExpiredTokens() {
        const now = Date.now();
        for (const [token, data] of this.resetTokens.entries()) {
            if (now > data.expiry || data.used) {
                this.resetTokens.delete(token);
            }
        }
    }

    /**
     * 获取密码策略配置
     */
    getPolicy() {
        return { ...this.config };
    }

    /**
     * 更新密码策略配置
     */
    updatePolicy(newConfig) {
        this.config = { ...this.config, ...newConfig };
        this.logger.info('Password policy updated', { config: this.config });
    }

    /**
     * 获取安全统计信息
     */
    getSecurityStats() {
        const now = Date.now();
        const stats = {
            failedAttempts: this.failedAttempts.size,
            lockedAccounts: 0,
            activeResetTokens: 0,
            expiredTokens: 0
        };
        
        // 统计锁定账户
        for (const attempts of this.failedAttempts.values()) {
            if (attempts.lockedUntil && now < attempts.lockedUntil) {
                stats.lockedAccounts++;
            }
        }
        
        // 统计重置令牌
        for (const tokenData of this.resetTokens.values()) {
            if (now > tokenData.expiry || tokenData.used) {
                stats.expiredTokens++;
            } else {
                stats.activeResetTokens++;
            }
        }
        
        return stats;
    }
}

module.exports = PasswordPolicy;
