# 设备管理系统代码实现

## 目录
1. [后端API接口](#后端api接口)
2. [数据库操作](#数据库操作)
3. [前端页面结构](#前端页面结构)
4. [前端JavaScript功能](#前端javascript功能)
5. [CSS样式](#css样式)
6. [权限控制](#权限控制)

## 后端API接口

### 设备管理业务逻辑 (backend/modules/business/device-management.js)

```javascript
const fs = require('fs');
const path = require('path');
const DataAccess = require('../../data-access');

// 设备管理模块
class DeviceManagement {
    constructor() {
        this.dataAccess = new DataAccess();
        // 保留文件路径用于向后兼容，但不再使用
        this.devicesFile = path.join(__dirname, '..', 'data', 'devices.json');
        this.factoriesFile = path.join(__dirname, '..', 'data', 'factories.json');
    }

    // 读取设备数据
    getDevices() {
        try {
            return this.dataAccess.getDevices();
        } catch (error) {
            console.error('读取设备数据失败:', error);
            return [];
        }
    }

    // 读取厂区数据
    getFactories() {
        try {
            return this.dataAccess.getFactories();
        } catch (error) {
            console.error('读取厂区数据失败:', error);
            return [];
        }
    }

    // 根据ID获取设备
    getDeviceById(id) {
        const devices = this.getDevices();
        return devices.find(device => device.id === id);
    }

    // 添加设备
    addDevice(deviceData) {
        try {
            console.log('开始添加设备，接收到的数据:', deviceData);
            const devices = this.getDevices();
            console.log('当前设备数量:', devices.length);

            // 验证必填字段
            if (!deviceData.deviceCode || !deviceData.deviceName || !deviceData.location || !deviceData.responsible || !deviceData.factory || !deviceData.entryDate) {
                console.log('验证失败：缺少必填字段');
                return { success: false, message: '设备编号、设备名称、厂区、设备位置、负责人和设备进厂日期为必填项' };
            }

            // 验证进厂日期格式和合理性
            const entryDate = new Date(deviceData.entryDate);
            if (isNaN(entryDate.getTime())) {
                console.log('验证失败：进厂日期格式无效');
                return { success: false, message: '设备进厂日期格式无效' };
            }

            // 验证进厂日期不能是未来日期
            const today = new Date();
            today.setHours(23, 59, 59, 999);
            if (entryDate > today) {
                console.log('验证失败：进厂日期不能是未来日期');
                return { success: false, message: '设备进厂日期不能是未来日期' };
            }

            // 检查设备编号是否已存在
            if (devices.some(device => device.deviceCode === deviceData.deviceCode)) {
                console.log('验证失败：设备编号已存在');
                return { success: false, message: '设备编号已存在' };
            }

            const newDevice = {
                id: Date.now().toString(),
                deviceCode: deviceData.deviceCode,
                deviceName: deviceData.deviceName,
                factory: deviceData.factory,
                location: deviceData.location,
                responsible: deviceData.responsible,
                entryDate: deviceData.entryDate,
                status: deviceData.status || '启用',
                createTime: new Date().toISOString(),
                updateTime: new Date().toISOString()
            };

            console.log('准备添加的设备对象:', newDevice);

            // 使用数据访问层添加设备
            const result = this.dataAccess.addDevice(newDevice);
            console.log('数据库操作结果:', result);

            if (result.changes > 0) {
                console.log('设备添加成功');
                return { success: true, message: '设备添加成功', device: newDevice };
            } else {
                console.log('设备添加失败：数据库操作未生效');
                return { success: false, message: '设备添加失败' };
            }
        } catch (error) {
            console.error('添加设备失败:', error);
            return { success: false, message: '添加设备失败: ' + error.message };
        }
    }

    // 更新设备
    updateDevice(id, deviceData) {
        try {
            const devices = this.getDevices();
            const deviceIndex = devices.findIndex(device => device.id === id);

            if (deviceIndex === -1) {
                return { success: false, message: '设备不存在' };
            }

            // 验证必填字段
            if (!deviceData.deviceCode || !deviceData.deviceName || !deviceData.location || !deviceData.responsible || !deviceData.factory || !deviceData.entryDate) {
                return { success: false, message: '设备编号、设备名称、厂区、设备位置、负责人和设备进厂日期为必填项' };
            }

            // 验证进厂日期格式和合理性
            const entryDate = new Date(deviceData.entryDate);
            if (isNaN(entryDate.getTime())) {
                return { success: false, message: '设备进厂日期格式无效' };
            }

            // 验证进厂日期不能是未来日期
            const today = new Date();
            today.setHours(23, 59, 59, 999);
            if (entryDate > today) {
                return { success: false, message: '设备进厂日期不能是未来日期' };
            }

            // 检查设备编号是否与其他设备重复（排除当前设备）
            if (devices.some(device => device.deviceCode === deviceData.deviceCode && device.id !== id)) {
                return { success: false, message: '设备编号已存在' };
            }

            // 使用数据访问层更新设备
            const result = this.dataAccess.updateDevice(id, deviceData);

            if (result.changes > 0) {
                return { success: true, message: '设备更新成功' };
            } else {
                return { success: false, message: '设备更新失败' };
            }
        } catch (error) {
            console.error('更新设备失败:', error);
            return { success: false, message: '更新设备失败: ' + error.message };
        }
    }

    // 删除设备
    deleteDevice(id) {
        try {
            const result = this.dataAccess.deleteDevice(id);
            
            if (result.changes > 0) {
                return { success: true, message: '设备删除成功' };
            } else {
                return { success: false, message: '设备不存在或删除失败' };
            }
        } catch (error) {
            console.error('删除设备失败:', error);
            return { success: false, message: '删除设备失败: ' + error.message };
        }
    }

    // 批量删除设备
    batchDeleteDevices(deviceIds) {
        try {
            let deletedCount = 0;
            const errors = [];

            for (const id of deviceIds) {
                try {
                    const result = this.dataAccess.deleteDevice(id);
                    if (result.changes > 0) {
                        deletedCount++;
                    }
                } catch (error) {
                    errors.push(`删除设备 ${id} 失败: ${error.message}`);
                }
            }

            if (deletedCount === deviceIds.length) {
                return { success: true, message: `成功删除 ${deletedCount} 个设备` };
            } else if (deletedCount > 0) {
                return { 
                    success: true, 
                    message: `成功删除 ${deletedCount} 个设备，${deviceIds.length - deletedCount} 个失败`,
                    errors: errors
                };
            } else {
                return { success: false, message: '删除失败', errors: errors };
            }
        } catch (error) {
            console.error('批量删除设备失败:', error);
            return { success: false, message: '批量删除设备失败: ' + error.message };
        }
    }
}

module.exports = DeviceManagement;
```

### 服务器路由 (backend/server.js - 设备相关路由)

```javascript
// 获取设备列表
app.get('/api/devices', (req, res) => {
    try {
        const devices = deviceManager.getDevices();
        const factories = deviceManager.getFactories();
        
        // 应用筛选和搜索
        let filteredDevices = devices;
        
        // 搜索功能
        if (req.query.search) {
            const searchTerm = req.query.search.toLowerCase();
            filteredDevices = filteredDevices.filter(device => 
                device.deviceCode.toLowerCase().includes(searchTerm) ||
                device.deviceName.toLowerCase().includes(searchTerm) ||
                device.location.toLowerCase().includes(searchTerm) ||
                device.responsible.toLowerCase().includes(searchTerm)
            );
        }
        
        // 厂区筛选
        if (req.query.factory) {
            filteredDevices = filteredDevices.filter(device => device.factory === req.query.factory);
        }
        
        // 状态筛选
        if (req.query.status) {
            filteredDevices = filteredDevices.filter(device => device.status === req.query.status);
        }
        
        // 位置筛选
        if (req.query.location) {
            filteredDevices = filteredDevices.filter(device => device.location === req.query.location);
        }
        
        // 负责人筛选
        if (req.query.responsible) {
            filteredDevices = filteredDevices.filter(device => device.responsible === req.query.responsible);
        }
        
        res.json({
            success: true,
            devices: filteredDevices,
            factories: factories,
            total: filteredDevices.length
        });
    } catch (error) {
        console.error('获取设备列表失败:', error);
        res.status(500).json({ success: false, message: '获取设备列表失败' });
    }
});

// 添加设备
app.post('/api/devices', (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员可以添加设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以添加设备' });
        }

        const result = deviceManager.addDevice(req.body);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('添加设备失败:', error);
        res.status(500).json({ success: false, message: '添加设备失败' });
    }
});

// 更新设备
app.put('/api/devices/:id', (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员可以更新设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以修改设备信息' });
        }

        const result = deviceManager.updateDevice(req.params.id, req.body);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('更新设备失败:', error);
        res.status(500).json({ success: false, message: '更新设备失败' });
    }
});

// 删除设备
app.delete('/api/devices/:id', (req, res) => {
    try {
        const { username, role, department } = req.body;

        // 权限检查：只有管理员可以删除设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以删除设备' });
        }

        const result = deviceManager.deleteDevice(req.params.id);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('删除设备失败:', error);
        res.status(500).json({ success: false, message: '删除设备失败' });
    }
});

// 批量删除设备
app.post('/api/devices/batch-delete', (req, res) => {
    try {
        const { deviceIds, username, role, department } = req.body;

        // 权限检查：只有管理员可以批量删除设备
        if (role !== 'admin') {
            return res.status(403).json({ success: false, message: '权限不足，只有管理员可以删除设备' });
        }

        if (!deviceIds || !Array.isArray(deviceIds) || deviceIds.length === 0) {
            return res.status(400).json({ success: false, message: '请选择要删除的设备' });
        }

        const result = deviceManager.batchDeleteDevices(deviceIds);
        if (result.success) {
            res.json(result);
        } else {
            res.status(400).json(result);
        }
    } catch (error) {
        console.error('批量删除设备失败:', error);
        res.status(500).json({ success: false, message: '批量删除设备失败' });
    }
});
```

## 数据库操作

### 数据访问层 (backend/data-access.js - 设备相关操作)

```javascript
// 获取所有设备
getDevices() {
    const stmt = this.db.prepare('SELECT * FROM devices ORDER BY createTime DESC');
    return stmt.all();
}

// 添加设备
addDevice(deviceData) {
    const stmt = this.db.prepare(`
        INSERT INTO devices (id, deviceCode, deviceName, factory, location, responsible, entryDate, status, createTime, updateTime)
        VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
    `);

    const now = new Date().toISOString();
    return stmt.run(
        deviceData.id,
        deviceData.deviceCode,
        deviceData.deviceName,
        deviceData.factory,
        deviceData.location || null,
        deviceData.responsible || null,
        deviceData.entryDate || null,
        deviceData.status || '启用',
        now,
        now
    );
}

// 更新设备
updateDevice(id, deviceData) {
    const stmt = this.db.prepare(`
        UPDATE devices
        SET deviceCode = ?, deviceName = ?, factory = ?, location = ?, responsible = ?,
            entryDate = ?, status = ?, updateTime = ?
        WHERE id = ?
    `);

    return stmt.run(
        deviceData.deviceCode,
        deviceData.deviceName,
        deviceData.factory,
        deviceData.location || null,
        deviceData.responsible || null,
        deviceData.entryDate || null,
        deviceData.status || '启用',
        new Date().toISOString(),
        id
    );
}

// 删除设备
deleteDevice(id) {
    const stmt = this.db.prepare('DELETE FROM devices WHERE id = ?');
    return stmt.run(id);
}

// 获取所有厂区
getFactories() {
    const stmt = this.db.prepare('SELECT * FROM factories ORDER BY id');
    return stmt.all();
}
```

## 前端页面结构

### 设备信息页面HTML (frontend/index.html - deviceInfoSection)

```html
<!-- 设备信息页面 -->
<section id="deviceInfoSection" class="hidden bg-white p-6 rounded-lg shadow-md">
    <div class="device-management-container">
        <!-- 设备管理头部 -->
        <div class="device-management-header">
            <h2 class="device-management-title">设备信息管理</h2>
            <div class="device-management-actions">
                <button id="manageFactoriesBtn" class="btn btn-secondary mr-2">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16m14 0h2m-2 0h-5m-9 0H3m2 0h5M9 7h1m-1 4h1m4-4h1m-1 4h1m-5 10v-5a1 1 0 011-1h2a1 1 0 011 1v5m-4 0h4" />
                    </svg>
                    厂区管理
                </button>
                <button id="addDeviceBtn" class="btn btn-primary">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                    </svg>
                    添加设备
                </button>
                <button id="batchDeleteDevicesBtn" class="btn btn-danger" disabled>
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16" />
                    </svg>
                    批量删除
                </button>
                <button id="importDevicesBtn" class="btn btn-success">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 16a4 4 0 01-.88-7.903A5 5 0 1115.9 6L16 6a5 5 0 011 9.9M9 19l3 3m0 0l3-3m-3 3V10" />
                    </svg>
                    导入设备
                </button>
                <button id="exportDevicesBtn" class="btn btn-success">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3m2 8H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    导出设备
                </button>
                <button id="refreshDevicesBtn" class="btn btn-outline">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15" />
                    </svg>
                    刷新
                </button>
            </div>
        </div>

        <!-- 设备统计概览 -->
        <div class="device-stats-section mb-6">
            <!-- 统计卡片 -->
            <div class="stats-grid grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 mb-6">
                <div class="stat-card bg-gradient-to-r from-blue-500 to-blue-600 text-white p-6 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-100 text-sm font-medium">设备总数</p>
                            <p id="totalDevicesCount" class="text-3xl font-bold">0</p>
                        </div>
                        <div class="bg-blue-400 bg-opacity-30 p-3 rounded-full">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zM3 10a1 1 0 011-1h6a1 1 0 011 1v6a1 1 0 01-1 1H4a1 1 0 01-1-1v-6zM14 9a1 1 0 00-1 1v6a1 1 0 001 1h2a1 1 0 001-1v-6a1 1 0 00-1-1h-2z"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="stat-card bg-gradient-to-r from-green-500 to-green-600 text-white p-6 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-100 text-sm font-medium">启用设备</p>
                            <p id="activeDevicesCount" class="text-3xl font-bold">0</p>
                            <p id="activeDevicesPercentage" class="text-green-100 text-sm">0%</p>
                        </div>
                        <div class="bg-green-400 bg-opacity-30 p-3 rounded-full">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="stat-card bg-gradient-to-r from-red-500 to-red-600 text-white p-6 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-red-100 text-sm font-medium">停用设备</p>
                            <p id="inactiveDevicesCount" class="text-3xl font-bold">0</p>
                            <p id="inactiveDevicesPercentage" class="text-red-100 text-sm">0%</p>
                        </div>
                        <div class="bg-red-400 bg-opacity-30 p-3 rounded-full">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zM8.707 7.293a1 1 0 00-1.414 1.414L8.586 10l-1.293 1.293a1 1 0 101.414 1.414L10 11.414l1.293 1.293a1 1 0 001.414-1.414L11.414 10l1.293-1.293a1 1 0 00-1.414-1.414L10 8.586 8.707 7.293z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                </div>

                <div class="stat-card bg-gradient-to-r from-purple-500 to-purple-600 text-white p-6 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-purple-100 text-sm font-medium">平均使用年限</p>
                            <p id="averageDeviceAge" class="text-3xl font-bold">0</p>
                            <p class="text-purple-100 text-sm">年</p>
                        </div>
                        <div class="bg-purple-400 bg-opacity-30 p-3 rounded-full">
                            <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M6 2a1 1 0 00-1 1v1H4a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2h-1V3a1 1 0 10-2 0v1H7V3a1 1 0 00-1-1zm0 5a1 1 0 000 2h8a1 1 0 100-2H6z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 图表区域 -->
            <div class="charts-grid mb-6">
                <!-- 设备状态分布图 -->
                <div class="chart-card doughnut-chart">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="chart-title">设备状态分布</h3>
                        <div class="chart-legend">
                            <div class="legend-item">
                                <div class="legend-dot bg-green-500"></div>
                                <span>启用</span>
                            </div>
                            <div class="legend-item">
                                <div class="legend-dot bg-red-500"></div>
                                <span>停用</span>
                            </div>
                        </div>
                    </div>
                    <div class="chart-container">
                        <canvas id="deviceStatusChart"></canvas>
                    </div>
                </div>

                <!-- 厂区设备分布图 -->
                <div class="chart-card bar-chart">
                    <div class="flex items-center justify-between mb-3">
                        <h3 class="chart-title">厂区设备分布</h3>
                        <button id="factoryChartRefresh" class="text-blue-600 hover:text-blue-800 text-xs flex items-center gap-1 px-2 py-1 rounded hover:bg-blue-50 transition-colors">
                            <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 2a1 1 0 011 1v2.101a7.002 7.002 0 0111.601 2.566 1 1 0 11-1.885.666A5.002 5.002 0 005.999 7H9a1 1 0 010 2H4a1 1 0 01-1-1V3a1 1 0 011-1zm.008 9.057a1 1 0 011.276.61A5.002 5.002 0 0014.001 13H11a1 1 0 110-2h5a1 1 0 011 1v5a1 1 0 11-2 0v-2.101a7.002 7.002 0 01-11.601-2.566 1 1 0 01.61-1.276z" clip-rule="evenodd"/>
                            </svg>
                            <span>刷新</span>
                        </button>
                    </div>
                    <div class="chart-container">
                        <canvas id="factoryDistributionChart"></canvas>
                    </div>
                </div>
            </div>
        </div>

        <!-- 选择计数器 -->
        <div id="selectedDeviceCount" class="selection-counter hidden"></div>

        <!-- 设备筛选器 -->
        <div class="device-filters">
            <div class="filter-grid">
                <div class="filter-group">
                    <label class="filter-label">搜索设备</label>
                    <input type="text" id="deviceSearchInput" class="filter-input" placeholder="搜索设备编号、名称、位置或负责人...">
                </div>
                <div class="filter-group">
                    <label class="filter-label">厂区</label>
                    <select id="deviceFactoryFilter" class="filter-input">
                        <option value="">全部厂区</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">设备状态</label>
                    <select id="deviceStatusFilter" class="filter-input">
                        <option value="">全部状态</option>
                        <option value="启用">启用</option>
                        <option value="停用">停用</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">设备位置</label>
                    <select id="deviceLocationFilter" class="filter-input">
                        <option value="">全部位置</option>
                        <!-- 位置选项将通过JavaScript动态加载 -->
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">负责人</label>
                    <select id="deviceResponsibleFilter" class="filter-input">
                        <option value="">全部负责人</option>
                        <!-- 负责人选项将通过JavaScript动态加载 -->
                    </select>
                </div>
            </div>
        </div>

        <!-- 滚动提示 -->
        <div class="scroll-hint"></div>

        <!-- 设备表格 -->
        <div class="device-table-container">
            <table class="device-table">
                <thead>
                    <tr>
                        <th>
                            <input type="checkbox" id="selectAllDevices" class="rounded border-gray-300 text-blue-600 focus:ring-blue-500">
                        </th>
                        <th class="sortable cursor-pointer hover:bg-gray-50" data-sort="deviceCode">
                            设备编号
                            <svg class="w-4 h-4 inline ml-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 12l5-5 5 5H5z"/>
                            </svg>
                        </th>
                        <th class="sortable cursor-pointer hover:bg-gray-50" data-sort="deviceName">
                            设备名称
                            <svg class="w-4 h-4 inline ml-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 12l5-5 5 5H5z"/>
                            </svg>
                        </th>
                        <th class="sortable cursor-pointer hover:bg-gray-50" data-sort="factory">
                            厂区
                            <svg class="w-4 h-4 inline ml-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 12l5-5 5 5H5z"/>
                            </svg>
                        </th>
                        <th>设备位置</th>
                        <th>负责人</th>
                        <th class="sortable cursor-pointer hover:bg-gray-50" data-sort="entryDate">
                            进厂日期
                            <svg class="w-4 h-4 inline ml-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 12l5-5 5 5H5z"/>
                            </svg>
                        </th>
                        <th class="sortable cursor-pointer hover:bg-gray-50" data-sort="status">
                            状态
                            <svg class="w-4 h-4 inline ml-1 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M5 12l5-5 5 5H5z"/>
                            </svg>
                        </th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody id="deviceTableBody">
                    <tr>
                        <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                            正在加载设备数据...
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>

        <!-- 分页 -->
        <div id="devicePagination" class="pagination-container">
            <div class="text-sm text-gray-700">
                正在加载...
            </div>
        </div>
    </div>
</section>
```

### 设备模态框HTML

```html
<!-- 设备管理模态框 -->
<div id="deviceModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="device-modal-content">
        <div class="device-modal-header">
            <h2 id="deviceModalTitle" class="device-modal-title">添加设备</h2>
        </div>
        <div class="device-modal-body">
            <form id="deviceForm" class="space-y-4">
                <div class="form-group">
                    <label class="form-label">设备编号 *</label>
                    <input type="text" id="deviceCode" name="deviceCode" class="form-input" placeholder="请输入设备编号" required>
                </div>
                <div class="form-group">
                    <label class="form-label">设备名称 *</label>
                    <input type="text" id="deviceName" name="deviceName" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">厂区 *</label>
                    <select id="deviceFactory" name="factory" class="form-select" required>
                        <option value="">请选择厂区</option>
                    </select>
                </div>
                <div class="form-group">
                    <label class="form-label">设备位置 *</label>
                    <input type="text" id="deviceLocation" name="location" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">负责人 *</label>
                    <input type="text" id="deviceResponsible" name="responsible" class="form-input" required>
                </div>
                <div class="form-group">
                    <label class="form-label">设备进厂日期 *</label>
                    <input type="date" id="deviceEntryDate" name="entryDate" class="form-input" required>
                    <p class="text-sm text-gray-500 mt-1">用于准确计算设备年龄和健康度评估</p>
                </div>
                <div class="form-group">
                    <label class="form-label">设备状态</label>
                    <select id="deviceStatus" name="status" class="form-select">
                        <option value="启用">启用</option>
                        <option value="停用">停用</option>
                    </select>
                </div>
            </form>
        </div>
        <div class="device-modal-footer">
            <button type="button" id="cancelDeviceBtn" onclick="window.deviceManager.hideDeviceModal()" class="btn btn-secondary">取消</button>
            <button type="button" id="saveDeviceBtn" class="btn btn-primary">保存设备</button>
        </div>
    </div>
</div>

<!-- 设备导入模态框 -->
<div id="deviceImportModal" class="hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center">
    <div class="device-modal-content" style="max-width: 600px;">
        <div class="device-modal-header">
            <h2 class="device-modal-title">导入设备数据</h2>
        </div>
        <div class="device-modal-body">
            <div class="space-y-4">
                <!-- 导入说明 -->
                <div class="bg-blue-50 border border-blue-200 rounded-lg p-4">
                    <h3 class="text-sm font-medium text-blue-800 mb-2">导入说明</h3>
                    <ul class="text-sm text-blue-700 space-y-1">
                        <li>• 支持Excel格式文件(.xlsx)</li>
                        <li>• 请使用系统导出的格式作为模板</li>
                        <li>• 必填字段：设备编号、设备名称、负责人、进厂日期</li>
                        <li>• 厂区信息：必须提供"厂区"列或"设备位置"列（推荐直接使用厂区列）</li>
                        <li>• 设备编号不能重复</li>
                        <li>• 进厂日期格式：YYYY-MM-DD</li>
                    </ul>
                </div>

                <!-- 文件选择 -->
                <div class="form-group">
                    <label class="form-label">选择Excel文件 *</label>
                    <input type="file" id="deviceImportFile" accept=".xlsx,.xls" class="form-input">
                    <p class="text-sm text-gray-500 mt-1">请选择要导入的Excel文件</p>
                </div>

                <!-- 导入选项 -->
                <div class="form-group">
                    <label class="form-label">导入选项</label>
                    <div class="space-y-2">
                        <label class="flex items-center">
                            <input type="checkbox" id="skipDuplicates" checked class="mr-2">
                            <span class="text-sm">跳过重复的设备编号</span>
                        </label>
                        <label class="flex items-center">
                            <input type="checkbox" id="updateExisting" class="mr-2">
                            <span class="text-sm">更新已存在的设备信息</span>
                        </label>
                    </div>
                </div>

                <!-- 导入进度 -->
                <div id="importProgress" class="hidden">
                    <div class="bg-gray-200 rounded-full h-2">
                        <div id="importProgressBar" class="bg-blue-600 h-2 rounded-full transition-all duration-300" style="width: 0%"></div>
                    </div>
                    <p id="importProgressText" class="text-sm text-gray-600 mt-1">准备导入...</p>
                </div>

                <!-- 导入结果 -->
                <div id="importResult" class="hidden">
                    <div id="importResultContent" class="p-4 rounded-lg"></div>
                </div>
            </div>
        </div>
        <div class="device-modal-footer">
            <button type="button" id="cancelImportBtn" class="btn btn-secondary">取消</button>
            <button type="button" id="startImportBtn" class="btn btn-primary">开始导入</button>
        </div>
    </div>
</div>
```

## 前端JavaScript功能

### 设备管理主类 (frontend/js/device-management.js)

```javascript
// 设备管理前端模块
class DeviceManagement {
    constructor() {
        this.devices = [];
        this.factories = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.filters = {};
        this.selectedDevices = new Set();
        this.searchTimeout = null;
        this.isInitialized = false;
        this.sortField = null;
        this.sortDirection = 'asc';
        this.isLoading = false; // 防止重复加载
        this.refreshDebounceTimer = null; // 防抖定时器
        this.init();
    }

    // 防抖工具函数
    debounce(func, wait) {
        return (...args) => {
            clearTimeout(this.refreshDebounceTimer);
            this.refreshDebounceTimer = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // 初始化
    init() {
        if (this.isInitialized) {
            return;
        }

        this.currentUser = localStorage.getItem('currentUser') || '';
        this.currentRole = localStorage.getItem('currentRole') || '';
        this.currentDepartment = localStorage.getItem('currentDepartment') || '';

        this.bindEvents();
        this.loadFactories();
        this.loadDevices();
        this.initPermissions();
        this.initDeviceStats();
        this.isInitialized = true;
    }

    // 初始化权限
    initPermissions() {
        const isAdmin = this.currentRole === 'admin';

        // 控制按钮显示
        const addDeviceBtn = document.getElementById('addDeviceBtn');
        const batchDeleteBtn = document.getElementById('batchDeleteDevicesBtn');
        const exportDevicesBtn = document.getElementById('exportDevicesBtn');

        if (addDeviceBtn) {
            if (isAdmin) {
                addDeviceBtn.style.display = '';
                addDeviceBtn.style.visibility = 'visible';
            } else {
                addDeviceBtn.style.display = 'none';
            }
        }
        if (batchDeleteBtn) {
            if (isAdmin) {
                batchDeleteBtn.style.display = '';
                batchDeleteBtn.style.visibility = 'visible';
            } else {
                batchDeleteBtn.style.display = 'none';
            }
        }
        if (exportDevicesBtn) {
            // 所有用户都可以导出设备数据
            exportDevicesBtn.style.display = '';
            exportDevicesBtn.style.visibility = 'visible';
        }
    }

    // 绑定事件
    bindEvents() {
        // 搜索功能 - 添加防抖机制
        const searchInput = document.getElementById('deviceSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.currentPage = 1;

                // 清除之前的搜索超时
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }

                // 延迟搜索，避免频繁请求
                this.searchTimeout = setTimeout(() => {
                    this.loadDevices(false); // 搜索时不显示loading
                }, 300);
            });
        }

        // 筛选器事件
        const factoryFilter = document.getElementById('deviceFactoryFilter');
        const statusFilter = document.getElementById('deviceStatusFilter');
        const locationFilter = document.getElementById('deviceLocationFilter');
        const responsibleFilter = document.getElementById('deviceResponsibleFilter');

        if (factoryFilter) {
            factoryFilter.addEventListener('change', (e) => {
                this.filters.factory = e.target.value;
                this.currentPage = 1;
                this.loadDevices(false);
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.currentPage = 1;
                this.loadDevices(false);
            });
        }

        if (locationFilter) {
            locationFilter.addEventListener('change', (e) => {
                this.filters.location = e.target.value;
                this.currentPage = 1;
                this.loadDevices(false);
            });
        }

        if (responsibleFilter) {
            responsibleFilter.addEventListener('change', (e) => {
                this.filters.responsible = e.target.value;
                this.currentPage = 1;
                this.loadDevices(false);
            });
        }

        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAllDevices');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.toggleAllDevices(e.target.checked);
            });
        }

        // 按钮事件
        const addDeviceBtn = document.getElementById('addDeviceBtn');
        const batchDeleteBtn = document.getElementById('batchDeleteDevicesBtn');
        const importDevicesBtn = document.getElementById('importDevicesBtn');
        const exportDevicesBtn = document.getElementById('exportDevicesBtn');
        const refreshDevicesBtn = document.getElementById('refreshDevicesBtn');
        const manageFactoriesBtn = document.getElementById('manageFactoriesBtn');

        if (addDeviceBtn) {
            addDeviceBtn.addEventListener('click', () => this.showDeviceModal());
        }

        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', () => this.batchDeleteDevices());
        }

        if (importDevicesBtn) {
            importDevicesBtn.addEventListener('click', () => this.showImportModal());
        }

        if (exportDevicesBtn) {
            exportDevicesBtn.addEventListener('click', () => this.exportDevices());
        }

        if (refreshDevicesBtn) {
            refreshDevicesBtn.addEventListener('click', this.debounce(() => this.loadDevices(true), 1000));
        }

        if (manageFactoriesBtn) {
            manageFactoriesBtn.addEventListener('click', () => {
                if (window.factoryManager) {
                    window.factoryManager.showFactoryModal();
                }
            });
        }

        // 表格排序事件
        const sortableHeaders = document.querySelectorAll('.device-table th.sortable');
        sortableHeaders.forEach(header => {
            header.addEventListener('click', () => {
                const sortField = header.getAttribute('data-sort');
                this.sortDevices(sortField);
            });
        });
    }

    // 加载设备数据
    async loadDevices(showLoadingIndicator = true) {
        if (this.isLoading) {
            console.log('设备数据正在加载中，跳过重复请求');
            return;
        }

        this.isLoading = true;

        try {
            if (showLoadingIndicator) {
                showLoading('正在加载设备数据...');
            }

            // 构建查询参数
            const queryParams = new URLSearchParams();

            if (this.filters.search) queryParams.append('search', this.filters.search);
            if (this.filters.factory) queryParams.append('factory', this.filters.factory);
            if (this.filters.status) queryParams.append('status', this.filters.status);
            if (this.filters.location) queryParams.append('location', this.filters.location);
            if (this.filters.responsible) queryParams.append('responsible', this.filters.responsible);

            const response = await apiRequest(`/api/devices?${queryParams.toString()}`);

            if (response.success) {
                this.devices = response.devices;

                // 如果有排序状态，重新应用排序
                if (this.sortField) {
                    this.applySorting();
                } else {
                    this.updateDeviceTable();
                    this.updatePagination();
                }

                this.updateSelectedCount();
                this.updateLocationAndResponsibleSelects();

                // 更新统计数据和图表
                if (this.deviceStats) {
                    this.deviceStats.updateStats(this.devices, this.factories);
                }

                console.log('设备列表数据加载成功');
            } else {
                showError('加载设备列表失败: ' + response.message);
            }
        } catch (error) {
            console.error('加载设备列表失败:', error);
            showError('加载设备列表失败，请检查网络连接');
        } finally {
            this.isLoading = false;
            if (showLoadingIndicator) {
                hideLoading();
            }
        }
    }

    // 更新设备表格
    updateDeviceTable() {
        const tbody = document.getElementById('deviceTableBody');
        if (!tbody) return;

        if (this.devices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                        暂无设备数据
                    </td>
                </tr>
            `;
            return;
        }

        // 计算分页
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageDevices = this.devices.slice(startIndex, endIndex);
        this.totalPages = Math.ceil(this.devices.length / this.pageSize);

        tbody.innerHTML = pageDevices.map(device => {
            // 格式化进厂日期
            let entryDateDisplay = '未设置';
            if (device.entryDate) {
                try {
                    const date = new Date(device.entryDate);
                    entryDateDisplay = date.toLocaleDateString('zh-CN');
                } catch (e) {
                    entryDateDisplay = device.entryDate;
                }
            }

            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox"
                               class="device-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                               value="${device.id}"
                               ${this.selectedDevices.has(device.id) ? 'checked' : ''}
                               onchange="deviceManager.toggleDeviceSelection('${device.id}', this.checked)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${device.deviceCode}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${device.deviceName}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${this.getFactoryName(device.factory)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${device.location}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${device.responsible}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${entryDateDisplay}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            device.status === '启用'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                        }">
                            ${device.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="deviceManager.viewDevice('${device.id}')"
                                class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                        ${this.currentRole === 'admin' ? `
                        <button onclick="deviceManager.editDevice('${device.id}')"
                                class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                        <button onclick="deviceManager.deleteDevice('${device.id}')"
                                class="text-red-600 hover:text-red-900">删除</button>
                        ` : ''}
                    </td>
                </tr>
            `;
        }).join('');
    }

    // 显示设备模态框
    showDeviceModal(device = null, readonly = false) {
        const modal = document.getElementById('deviceModal');
        const form = document.getElementById('deviceForm');
        const title = document.getElementById('deviceModalTitle');

        if (!modal || !form || !title) return;

        // 权限检查：非管理员用户只能查看
        const isAdmin = this.currentRole === 'admin';
        if (!isAdmin && !readonly) {
            this.showDeviceModal(device, true); // 强制只读模式
            return;
        }

        // 设置标题
        if (readonly) {
            title.textContent = '设备详情';
        } else if (device) {
            title.textContent = '编辑设备';
        } else {
            title.textContent = '添加设备';
        }

        // 填充厂区选项
        this.populateFactorySelect('deviceFactory');

        // 填充表单数据
        if (device) {
            document.getElementById('deviceCode').value = device.deviceCode || '';
            document.getElementById('deviceName').value = device.deviceName || '';
            document.getElementById('deviceFactory').value = device.factory || '';
            document.getElementById('deviceLocation').value = device.location || '';
            document.getElementById('deviceResponsible').value = device.responsible || '';
            document.getElementById('deviceEntryDate').value = device.entryDate || '';
            document.getElementById('deviceStatus').value = device.status || '启用';
        } else {
            form.reset();
            document.getElementById('deviceStatus').value = '启用';
        }

        // 设置只读状态
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.disabled = readonly;
        });

        // 设置按钮状态
        const saveBtn = document.getElementById('saveDeviceBtn');
        const cancelBtn = document.getElementById('cancelDeviceBtn');

        if (saveBtn) {
            saveBtn.style.display = readonly ? 'none' : 'block';
            saveBtn.textContent = device ? '更新设备' : '添加设备';
        }

        if (cancelBtn) {
            cancelBtn.textContent = readonly ? '关闭' : '取消';
        }

        // 绑定保存事件
        if (!readonly) {
            const newSaveBtn = saveBtn.cloneNode(true);
            saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

            newSaveBtn.addEventListener('click', () => {
                this.saveDevice(device ? device.id : null);
            });
        }

        // 显示模态框
        modal.classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 隐藏设备模态框
    hideDeviceModal() {
        const modal = document.getElementById('deviceModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.classList.remove('modal-open');
        }
    }

    // 保存设备
    async saveDevice(deviceId = null) {
        const form = document.getElementById('deviceForm');
        if (!form) return;

        // 获取表单数据
        const formData = new FormData(form);
        const deviceData = {};
        for (let [key, value] of formData.entries()) {
            deviceData[key] = value;
        }

        // 验证必填字段
        if (!deviceData.deviceCode || !deviceData.deviceName || !deviceData.factory ||
            !deviceData.location || !deviceData.responsible || !deviceData.entryDate) {
            showError('请填写所有必填字段');
            return;
        }

        try {
            // 获取保存按钮并显示加载状态
            const saveButton = document.querySelector('#deviceModal .bg-blue-500');
            if (saveButton) {
                this.showButtonLoading(saveButton, deviceId ? '更新中...' : '添加中...');
            } else {
                showLoading(deviceId ? '正在更新设备...' : '正在添加设备...');
            }

            // 添加用户权限信息
            deviceData.username = this.currentUser;
            deviceData.role = this.currentRole;
            deviceData.department = this.currentDepartment;

            const url = deviceId ? `/api/devices/${deviceId}` : '/api/devices';
            const method = deviceId ? 'PUT' : 'POST';

            const response = await apiRequest(url, {
                method: method,
                data: deviceData
            });

            if (response.success) {
                showSuccess(response.message);
                this.hideDeviceModal();
                this.loadDevices(false); // 不显示全屏加载器
            } else {
                showError(response.message);
            }
        } catch (error) {
            console.error('保存设备失败:', error);
            showError('保存设备失败');
        } finally {
            const saveButton = document.querySelector('#deviceModal .bg-blue-500');
            if (saveButton) {
                this.hideButtonLoading(saveButton, deviceId ? '更新设备' : '添加设备');
            } else {
                hideLoading();
            }
        }
    }
}

// 初始化设备管理
function initDeviceManagement() {
    return new DeviceManagement();
}

// 导出到全局
window.initDeviceManagement = initDeviceManagement;
```

## CSS样式

### 设备管理样式 (frontend/css/device-management.css)

```css
/* 设备管理模块样式 */

/* 设备统计概览样式 */
.device-stats-section {
    margin-bottom: 1.5rem;
}

/* 统计数字样式优化 */
.stat-card .text-3xl {
    font-size: 2rem;
    line-height: 1.2;
}

.stat-card .text-sm {
    font-size: 0.8rem;
    opacity: 0.9;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--from-color), var(--to-color));
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

/* 图表区域样式 */
.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.5rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    border: 1px solid #e5e7eb;
}

.chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin: 0;
}

.chart-container {
    position: relative;
    height: 200px;
    margin-top: 1rem;
}

.chart-legend {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
    color: #6b7280;
}

.legend-dot {
    width: 0.75rem;
    height: 0.75rem;
    border-radius: 50%;
}

/* 设备管理页面布局 */
.device-management-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1rem;
}

/* 设备管理头部 */
.device-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.device-management-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.device-management-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* 筛选器样式 */
.device-filters {
    background: white;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.filter-input {
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 设备表格样式 */
.device-table-container {
    background: white;
    border-radius: 0.5rem;
    overflow-x: auto; /* 启用横向滚动 */
    overflow-y: visible;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    /* 确保滚动条可见 */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.device-table {
    width: 100%;
    min-width: 1000px; /* 设置最小宽度，确保所有列都能显示 */
    border-collapse: collapse;
    table-layout: fixed; /* 固定表格布局 */
}

/* 设置各列的固定宽度 */
.device-table th:nth-child(1),
.device-table td:nth-child(1) {
    width: 50px; /* 复选框列 */
    min-width: 50px;
}

.device-table th:nth-child(2),
.device-table td:nth-child(2) {
    width: 120px; /* 设备编号列 */
    min-width: 120px;
}

.device-table th:nth-child(3),
.device-table td:nth-child(3) {
    width: 150px; /* 设备名称列 */
    min-width: 150px;
}

.device-table th:nth-child(4),
.device-table td:nth-child(4) {
    width: 120px; /* 厂区列 */
    min-width: 120px;
}

.device-table th:nth-child(5),
.device-table td:nth-child(5) {
    width: 120px; /* 设备位置列 */
    min-width: 120px;
}

.device-table th:nth-child(6),
.device-table td:nth-child(6) {
    width: 100px; /* 负责人列 */
    min-width: 100px;
}

.device-table th:nth-child(7),
.device-table td:nth-child(7) {
    width: 120px; /* 进厂日期列 */
    min-width: 120px;
}

.device-table th:nth-child(8),
.device-table td:nth-child(8) {
    width: 80px; /* 状态列 */
    min-width: 80px;
}

.device-table th:nth-child(9),
.device-table td:nth-child(9) {
    width: 150px; /* 操作列 */
    min-width: 150px;
}

.device-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1.5rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: #6b7280;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e5e7eb;
    position: sticky;
    top: 0;
    z-index: 10;
}

.device-table td {
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
    vertical-align: middle;
    /* 文本溢出处理 */
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
}

.device-table tr:hover {
    background-color: #f9fafb;
}

/* 模态框样式 */
.device-modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.device-modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.device-modal-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.device-modal-body {
    padding: 1.5rem;
}

.device-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.form-input,
.form-select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus,
.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: 1px solid transparent;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4b5563;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
}

.btn-outline {
    background-color: transparent;
    color: #6b7280;
    border-color: #d1d5db;
}

.btn-outline:hover:not(:disabled) {
    background-color: #f9fafb;
    color: #374151;
}

/* 分页样式 */
.pagination-container {
    display: flex;
    justify-content: between;
    align-items: center;
    padding: 1rem;
    background: white;
    border-top: 1px solid #e5e7eb;
}

/* 选择计数器样式 */
.selection-counter {
    background: #dbeafe;
    color: #1e40af;
    padding: 0.5rem 1rem;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    margin-bottom: 1rem;
    border: 1px solid #93c5fd;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .device-management-header {
        flex-direction: column;
        align-items: stretch;
    }

    .device-management-actions {
        justify-content: center;
    }

    .filter-grid {
        grid-template-columns: 1fr;
    }

    .device-table {
        min-width: 800px; /* 在小屏幕上进一步减少最小宽度 */
    }

    .device-modal-content {
        margin: 0.5rem;
        max-width: none;
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }
}
```

## 权限控制

### 访问控制配置 (backend/modules/auth/access-control.js)

```javascript
// API权限映射
const API_PERMISSIONS = {
    // 设备管理API
    'GET:/api/devices': ['devices.view'],
    'POST:/api/devices': ['devices.create'],
    'PUT:/api/devices/:id': ['devices.edit'],
    'DELETE:/api/devices/:id': ['devices.delete'],
    'POST:/api/devices/:id/maintenance': ['devices.maintenance'],

    // 维护记录API
    'GET:/api/maintenance': ['maintenance.view'],
    'POST:/api/maintenance': ['maintenance.create'],
    'PUT:/api/maintenance/:id': ['maintenance.edit'],
    'DELETE:/api/maintenance/:id': ['maintenance.delete'],

    // 厂区管理API
    'GET:/api/factories': ['factories.view'],
    'POST:/api/factories': ['factories.create'],
    'PUT:/api/factories/:id': ['factories.edit'],
    'DELETE:/api/factories/:id': ['factories.delete'],
};

// 角色权限映射
const ROLE_PERMISSIONS = {
    'admin': [
        // 设备管理权限
        'devices.view', 'devices.create', 'devices.edit', 'devices.delete', 'devices.maintenance',
        // 维护记录权限
        'maintenance.view', 'maintenance.create', 'maintenance.edit', 'maintenance.delete',
        // 厂区管理权限
        'factories.view', 'factories.create', 'factories.edit', 'factories.delete',
        // 用户管理权限
        'users.view', 'users.create', 'users.edit', 'users.delete',
        // 系统设置权限
        'system.settings', 'system.backup', 'system.restore'
    ],
    'manager': [
        // 设备查看权限
        'devices.view',
        // 维护记录权限
        'maintenance.view', 'maintenance.create', 'maintenance.edit',
        // 厂区查看权限
        'factories.view'
    ],
    'user': [
        // 基本查看权限
        'devices.view',
        'maintenance.view',
        'factories.view'
    ]
};

// 检查用户权限
function hasPermission(userRole, permission) {
    const rolePermissions = ROLE_PERMISSIONS[userRole] || [];
    return rolePermissions.includes(permission);
}

// 检查API访问权限
function checkApiPermission(userRole, method, path) {
    const apiKey = `${method}:${path}`;
    const requiredPermissions = API_PERMISSIONS[apiKey] || [];

    if (requiredPermissions.length === 0) {
        return true; // 无需特殊权限的API
    }

    return requiredPermissions.some(permission => hasPermission(userRole, permission));
}

module.exports = {
    API_PERMISSIONS,
    ROLE_PERMISSIONS,
    hasPermission,
    checkApiPermission
};
```

## API文档

### 设备管理接口文档

```markdown
## 设备管理接口

### 获取设备列表
```http
GET /api/devices
```

**查询参数**:
- `page`: 页码
- `limit`: 每页数量
- `search`: 搜索关键词
- `status`: 设备状态筛选
- `factory`: 厂区筛选
- `location`: 位置筛选
- `responsible`: 负责人筛选

**响应**:
```json
{
  "success": true,
  "devices": [
    {
      "id": "1",
      "deviceCode": "DEV001",
      "deviceName": "设备名称",
      "factory": "SR",
      "location": "A区1号线",
      "responsible": "张三",
      "entryDate": "2023-01-01",
      "status": "启用",
      "createTime": "2023-01-01T00:00:00.000Z",
      "updateTime": "2023-01-01T00:00:00.000Z"
    }
  ],
  "factories": [
    {
      "id": "SR",
      "name": "深圳工厂",
      "description": "深圳生产基地"
    }
  ],
  "total": 1
}
```

### 创建设备
```http
POST /api/devices
```

**请求体**:
```json
{
  "deviceCode": "DEV001",
  "deviceName": "设备名称",
  "factory": "SR",
  "location": "A区1号线",
  "responsible": "张三",
  "entryDate": "2023-01-01",
  "status": "启用"
}
```

### 更新设备
```http
PUT /api/devices/:id
```

### 删除设备
```http
DELETE /api/devices/:id
```

### 批量删除设备
```http
POST /api/devices/batch-delete
```

**请求体**:
```json
{
  "deviceIds": ["1", "2", "3"]
}
```

## 功能特性总结

### 核心功能
1. **设备信息管理**
   - 设备的增删改查操作
   - 设备信息包括：编号、名称、厂区、位置、负责人、进厂日期、状态
   - 支持批量操作（批量删除、批量导入、批量导出）

2. **数据筛选与搜索**
   - 多维度筛选：厂区、状态、位置、负责人
   - 全文搜索：支持设备编号、名称、位置、负责人的模糊搜索
   - 实时搜索：输入防抖，提升用户体验

3. **数据可视化**
   - 设备统计概览：总数、启用数、停用数、平均使用年限
   - 设备状态分布图（饼图）
   - 厂区设备分布图（柱状图）

4. **权限控制**
   - 基于角色的权限管理（admin、manager、user）
   - API级别的权限控制
   - 前端界面权限控制

5. **数据导入导出**
   - Excel格式的设备数据导入
   - 支持数据验证和错误处理
   - 设备数据导出功能

### 技术架构
1. **后端技术栈**
   - Node.js + Express.js
   - SQLite数据库
   - 模块化业务逻辑设计

2. **前端技术栈**
   - 原生JavaScript（ES6+）
   - Tailwind CSS框架
   - Chart.js图表库
   - 响应式设计

3. **数据库设计**
   - devices表：存储设备信息
   - factories表：存储厂区信息
   - 支持关联查询和数据完整性

### 用户体验优化
1. **性能优化**
   - 防抖搜索，减少API请求
   - 分页加载，提升大数据量处理能力
   - 缓存机制，减少重复请求

2. **界面交互**
   - 模态框操作，无需页面跳转
   - 实时数据更新
   - 加载状态提示
   - 错误信息友好提示

3. **响应式设计**
   - 支持PC端和移动端
   - 表格横向滚动适配
   - 移动端优化的操作界面

### 安全性考虑
1. **数据验证**
   - 前后端双重数据验证
   - SQL注入防护
   - XSS攻击防护

2. **权限控制**
   - 基于JWT的身份认证
   - 细粒度的权限控制
   - API访问权限验证

3. **数据完整性**
   - 必填字段验证
   - 数据格式验证
   - 业务逻辑验证

这个设备管理系统提供了完整的设备信息管理功能，具有良好的用户体验、安全性和可扩展性。代码结构清晰，便于维护和二次开发。
```
