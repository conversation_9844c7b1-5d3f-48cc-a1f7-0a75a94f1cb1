/* 维修保养记录管理页面样式 */

/* 维修保养记录表格容器 - 添加横向滚动 */
#maintenanceRecordsSection .device-table-container {
    background: white;
    border-radius: 0.5rem;
    overflow-x: auto; /* 启用横向滚动 */
    overflow-y: visible;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    /* 确保滚动条可见 */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

/* 维修保养记录表格 */
#maintenanceRecordsSection .device-table {
    width: 100%;
    min-width: 1200px; /* 设置最小宽度，确保所有列都能显示 */
    border-collapse: collapse;
    table-layout: fixed; /* 固定表格布局 */
}

/* 设置各列的固定宽度 */
#maintenanceRecordsSection .device-table th:nth-child(1),
#maintenanceRecordsSection .device-table td:nth-child(1) {
    width: 50px; /* 复选框列 */
    min-width: 50px;
}

#maintenanceRecordsSection .device-table th:nth-child(2),
#maintenanceRecordsSection .device-table td:nth-child(2) {
    width: 120px; /* 记录编号列 */
    min-width: 120px;
}

#maintenanceRecordsSection .device-table th:nth-child(3),
#maintenanceRecordsSection .device-table td:nth-child(3) {
    width: 180px; /* 关联设备列 */
    min-width: 180px;
}

#maintenanceRecordsSection .device-table th:nth-child(4),
#maintenanceRecordsSection .device-table td:nth-child(4) {
    width: 80px; /* 类型列 */
    min-width: 80px;
}

#maintenanceRecordsSection .device-table th:nth-child(5),
#maintenanceRecordsSection .device-table td:nth-child(5) {
    width: 100px; /* 日期列 */
    min-width: 100px;
}

#maintenanceRecordsSection .device-table th:nth-child(6),
#maintenanceRecordsSection .device-table td:nth-child(6) {
    width: 120px; /* 开始时间列 */
    min-width: 120px;
}

#maintenanceRecordsSection .device-table th:nth-child(7),
#maintenanceRecordsSection .device-table td:nth-child(7) {
    width: 120px; /* 结束时间列 */
    min-width: 120px;
}

#maintenanceRecordsSection .device-table th:nth-child(8),
#maintenanceRecordsSection .device-table td:nth-child(8) {
    width: 100px; /* 操作人列 */
    min-width: 100px;
}

#maintenanceRecordsSection .device-table th:nth-child(9),
#maintenanceRecordsSection .device-table td:nth-child(9) {
    width: 200px; /* 描述列 */
    min-width: 200px;
}

#maintenanceRecordsSection .device-table th:nth-child(10),
#maintenanceRecordsSection .device-table td:nth-child(10) {
    width: 150px; /* 操作列 */
    min-width: 150px;
}

/* 表格单元格样式优化 */
#maintenanceRecordsSection .device-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e5e7eb;
    white-space: nowrap; /* 防止表头换行 */
    overflow: hidden;
    text-overflow: ellipsis;
}

#maintenanceRecordsSection .device-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.875rem;
    color: #1f2937;
    vertical-align: middle;
    text-align: left;
}

/* 特定列的文本处理 */
/* 记录编号、日期、时间、操作人列 - 不换行且完整显示，左对齐 */
#maintenanceRecordsSection .device-table td:nth-child(2),
#maintenanceRecordsSection .device-table td:nth-child(5),
#maintenanceRecordsSection .device-table td:nth-child(6),
#maintenanceRecordsSection .device-table td:nth-child(7),
#maintenanceRecordsSection .device-table td:nth-child(8) {
    white-space: nowrap;
    overflow: visible; /* 改为visible，确保内容完整显示 */
    text-overflow: clip; /* 改为clip，不使用省略号 */
    text-align: left;
}

/* 关联设备和描述列 - 允许换行但限制高度，左对齐 */
#maintenanceRecordsSection .device-table td:nth-child(3),
#maintenanceRecordsSection .device-table td:nth-child(9) {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    max-height: 60px;
    overflow: hidden;
    line-height: 1.4;
    text-align: left;
}

/* 类型列 - 左对齐显示 */
#maintenanceRecordsSection .device-table td:nth-child(4) {
    text-align: left;
    white-space: nowrap;
}

/* 操作列 - 确保按钮不换行且左对齐 */
#maintenanceRecordsSection .device-table td:nth-child(10) {
    white-space: nowrap;
    overflow: visible; /* 允许下拉菜单等元素溢出 */
    text-align: left;
}

/* 操作按钮样式优化 */
#maintenanceRecordsSection .device-table td:nth-child(10) button {
    margin-right: 0.25rem;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    border-radius: 0.25rem;
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

/* 自定义滚动条样式 (Webkit浏览器) */
#maintenanceRecordsSection .device-table-container::-webkit-scrollbar {
    height: 8px;
}

#maintenanceRecordsSection .device-table-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

#maintenanceRecordsSection .device-table-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

#maintenanceRecordsSection .device-table-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 表格行悬停效果 */
#maintenanceRecordsSection .device-table tbody tr:hover {
    background-color: #f9fafb;
}

/* 复选框列 - 居中对齐 */
#maintenanceRecordsSection .device-table th:nth-child(1),
#maintenanceRecordsSection .device-table td:nth-child(1) {
    text-align: center;
}

/* 复选框样式 */
#maintenanceRecordsSection .device-table input[type="checkbox"] {
    width: 16px;
    height: 16px;
    margin: 0;
}

/* 状态标签样式 */
#maintenanceRecordsSection .device-table .status-badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1.25;
    white-space: nowrap;
}

/* 响应式优化 */
@media (max-width: 1024px) {
    #maintenanceRecordsSection .device-table {
        min-width: 1000px; /* 在中等屏幕上稍微减少最小宽度 */
    }
}

@media (max-width: 768px) {
    #maintenanceRecordsSection .device-table {
        min-width: 900px; /* 在小屏幕上进一步减少最小宽度 */
    }

    /* 在移动设备上调整列宽 */
    #maintenanceRecordsSection .device-table th:nth-child(3),
    #maintenanceRecordsSection .device-table td:nth-child(3) {
        width: 150px;
        min-width: 150px;
    }

    #maintenanceRecordsSection .device-table th:nth-child(9),
    #maintenanceRecordsSection .device-table td:nth-child(9) {
        width: 150px;
        min-width: 150px;
    }

    #maintenanceRecordsSection .device-table th:nth-child(10),
    #maintenanceRecordsSection .device-table td:nth-child(10) {
        width: 120px;
        min-width: 120px;
    }
}

/* 滚动提示样式 */
.scroll-hint {
    position: relative;
    margin-bottom: 0.5rem;
}

.scroll-hint::after {
    content: "← 左右滑动查看更多列 →";
    display: block;
    text-align: center;
    font-size: 0.75rem;
    color: #6b7280;
    padding: 0.25rem;
    background-color: #f9fafb;
    border-radius: 0.25rem;
    border: 1px solid #e5e7eb;
}

/* 当表格宽度超出容器时显示滚动提示 */
@media (max-width: 1200px) {
    .scroll-hint::after {
        display: block;
    }
}

@media (min-width: 1201px) {
    .scroll-hint::after {
        display: none;
    }
}
