/**
 * 备份调度器
 * 管理自动化备份任务的调度和执行
 */
class BackupScheduler {
    constructor(backupManager, logger) {
        this.backupManager = backupManager;
        this.logger = logger;
        this.scheduledTasks = new Map();
        this.isRunning = false;
        
        // 默认调度配置
        this.config = {
            // 月末备份时间（每月最后一天17:00）
            monthlyBackupTime: '17:00',
            // 是否启用自动备份
            autoBackup: true,
            // 备份保留策略
            retention: {
                monthly: 24  // 保留24个月的月备份
            }
        };
    }

    /**
     * 启动备份调度器
     */
    start(config = {}) {
        if (this.isRunning) {
            this.logger.warn('备份调度器已在运行');
            return;
        }

        // 合并配置
        this.config = { ...this.config, ...config };
        this.isRunning = true;

        if (this.config.autoBackup) {
            // 启动月末备份调度
            this.startMonthlyBackups();

            // 启动时检查是否需要首次备份
            this.checkAndPerformBackup();
        }

        this.logger.info('备份调度器已启动', {
            monthlyBackupTime: this.config.monthlyBackupTime,
            autoBackup: this.config.autoBackup,
            retentionMonths: this.config.retention.monthly
        });
    }

    /**
     * 停止备份调度器
     */
    stop() {
        if (!this.isRunning) {
            return;
        }

        // 清除所有定时任务
        for (const [name, taskId] of this.scheduledTasks) {
            clearInterval(taskId);
            clearTimeout(taskId);
        }
        
        this.scheduledTasks.clear();
        this.isRunning = false;
        
        this.logger.info('备份调度器已停止');
    }

    /**
     * 启动月末备份调度
     */
    startMonthlyBackups() {
        // 每小时检查一次是否需要执行月末备份
        const checkInterval = 60 * 60 * 1000; // 1小时

        const checkAndScheduleBackup = () => {
            const now = new Date();
            const [hour, minute] = this.config.monthlyBackupTime.split(':').map(Number);

            // 计算当月最后一天
            const lastDayOfMonth = new Date(now.getFullYear(), now.getMonth() + 1, 0);
            const scheduledTime = new Date(lastDayOfMonth);
            scheduledTime.setHours(hour, minute, 0, 0);

            // 检查是否是备份时间（允许1小时的误差范围）
            const timeDiff = Math.abs(now.getTime() - scheduledTime.getTime());
            const oneHour = 60 * 60 * 1000;

            if (timeDiff <= oneHour && now.getDate() === lastDayOfMonth.getDate()) {
                // 检查今天是否已经执行过备份
                if (!this.hasBackupToday()) {
                    this.logger.info('检测到月末备份时间，开始执行备份');
                    this.performScheduledBackup('monthly');
                }
            }

            // 计算下次备份时间用于日志显示
            let nextBackupTime = new Date(scheduledTime);
            if (scheduledTime <= now) {
                const nextMonth = new Date(now.getFullYear(), now.getMonth() + 2, 0);
                nextBackupTime = new Date(nextMonth);
                nextBackupTime.setHours(hour, minute, 0, 0);
            }

            this.nextBackupTime = nextBackupTime;
        };

        // 立即检查一次
        checkAndScheduleBackup();

        // 设置定期检查
        const intervalId = setInterval(checkAndScheduleBackup, checkInterval);
        this.scheduledTasks.set('monthlyBackup', intervalId);

        this.logger.info(`月末备份调度器已启动，每小时检查一次`, {
            nextBackupTime: this.nextBackupTime ? this.nextBackupTime.toISOString() : 'calculating...'
        });
    }

    /**
     * 检查今天是否已经执行过备份
     */
    hasBackupToday() {
        try {
            const stats = this.backupManager.getStats();
            if (!stats.lastBackupTime) {
                return false;
            }

            const lastBackup = new Date(stats.lastBackupTime);
            const today = new Date();

            return lastBackup.toDateString() === today.toDateString();
        } catch (error) {
            this.logger.error('检查今日备份状态失败', { error: error.message });
            return false;
        }
    }

    /**
     * 执行调度的备份
     */
    async performScheduledBackup(type) {
        try {
            this.logger.info(`开始执行月末备份`);

            const result = await this.backupManager.performFullBackup({
                type: type,
                automated: true
            });

            if (result.success) {
                this.logger.info(`月末备份完成`, {
                    backupName: result.backup.name,
                    size: result.backup.size,
                    duration: result.backup.duration
                });

                // 执行备份保留策略
                await this.applyRetentionPolicy();
            } else {
                this.logger.error(`月末备份失败`, {
                    error: result.error
                });
            }

        } catch (error) {
            this.logger.error(`执行月末备份时发生异常`, {
                error: error.message
            });
        }
    }

    /**
     * 检查并执行备份（仅在从未备份时执行）
     */
    async checkAndPerformBackup() {
        try {
            const stats = this.backupManager.getStats();

            // 如果从未备份过，执行首次备份
            if (!stats.lastBackupTime) {
                this.logger.info('检测到从未执行过备份，开始首次备份');
                await this.performScheduledBackup('initial');
            } else {
                this.logger.info('系统已有备份记录，等待下次月末自动备份');
            }
        } catch (error) {
            this.logger.error('检查备份状态时发生错误', { error: error.message });
        }
    }

    /**
     * 应用备份保留策略
     */
    async applyRetentionPolicy() {
        try {
            const backups = await this.backupManager.listBackups();

            // 只保留月备份，按月份分组
            const monthlyBackups = [];

            // 按月份分组备份
            for (const backup of backups) {
                const backupDate = new Date(backup.created);
                const monthKey = `${backupDate.getFullYear()}-${backupDate.getMonth()}`;

                // 检查该月是否已有备份
                const existingBackup = monthlyBackups.find(b => {
                    const bDate = new Date(b.created);
                    return `${bDate.getFullYear()}-${bDate.getMonth()}` === monthKey;
                });

                // 如果该月没有备份，或者当前备份更新，则保留当前备份
                if (!existingBackup || new Date(backup.created) > new Date(existingBackup.created)) {
                    if (existingBackup) {
                        // 移除旧的备份
                        const index = monthlyBackups.indexOf(existingBackup);
                        monthlyBackups.splice(index, 1);
                    }
                    monthlyBackups.push(backup);
                }
            }

            // 应用保留策略 - 只保留最近的月备份
            const toKeep = new Set();

            // 保留最近的月备份（按配置的数量）
            monthlyBackups
                .sort((a, b) => new Date(b.created) - new Date(a.created))
                .slice(0, this.config.retention.monthly)
                .forEach(backup => toKeep.add(backup.name));

            // 删除不需要保留的备份
            const toDelete = backups.filter(backup => !toKeep.has(backup.name));
            
            for (const backup of toDelete) {
                try {
                    const backupPath = require('path').join(this.backupManager.backupDir, backup.name);
                    require('fs').rmSync(backupPath, { recursive: true, force: true });
                    this.logger.info('根据保留策略删除备份', { backup: backup.name });
                } catch (error) {
                    this.logger.error('删除备份失败', { 
                        backup: backup.name, 
                        error: error.message 
                    });
                }
            }
            
            if (toDelete.length > 0) {
                this.logger.info('备份保留策略执行完成', {
                    deleted: toDelete.length,
                    kept: toKeep.size
                });
            }
            
        } catch (error) {
            this.logger.error('应用备份保留策略失败', { error: error.message });
        }
    }

    /**
     * 手动触发备份
     */
    async triggerManualBackup(type = 'manual') {
        return await this.performScheduledBackup(type);
    }

    /**
     * 获取调度器状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            config: this.config,
            nextBackupTime: this.nextBackupTime ? this.nextBackupTime.toISOString() : null,
            scheduledTasks: Array.from(this.scheduledTasks.keys()),
            backupStats: this.backupManager.getStats()
        };
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        const oldConfig = { ...this.config };
        this.config = { ...this.config, ...newConfig };
        
        // 如果调度相关配置改变，重启调度器
        if (
            oldConfig.monthlyBackupTime !== this.config.monthlyBackupTime ||
            oldConfig.autoBackup !== this.config.autoBackup
        ) {
            if (this.isRunning) {
                this.stop();
                this.start();
            }
        }
        
        this.logger.info('备份调度器配置已更新', { 
            oldConfig, 
            newConfig: this.config 
        });
    }
}

module.exports = BackupScheduler;
