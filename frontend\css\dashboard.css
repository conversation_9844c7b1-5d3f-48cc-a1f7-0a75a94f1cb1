/* Dashboard首页样式 */

.dashboard-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1.5rem;
    background: linear-gradient(135deg, #f8fafc 0%, #e2e8f0 100%);
    min-height: 100vh;
}

/* 欢迎区域 */
.welcome-section {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-radius: 1rem;
    padding: 2rem;
    margin-bottom: 2rem;
    color: white;
    box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1);
}

.welcome-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex-wrap: wrap;
    gap: 1rem;
}

.welcome-text h1 {
    font-size: 2rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
}

.welcome-text p {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

.welcome-info {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    gap: 0.5rem;
}

.current-time {
    font-size: 1.1rem;
    font-weight: 600;
}

.user-role {
    background: rgba(255, 255, 255, 0.2);
    padding: 0.5rem 1rem;
    border-radius: 0.5rem;
    font-size: 0.9rem;
    backdrop-filter: blur(10px);
}

/* 核心数据概览区域 */
.overview-section {
    margin-bottom: 2rem;
}

.section-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin-bottom: 1rem;
}



.overview-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.overview-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
    border: 2px solid transparent;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.overview-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #3b82f6, #8b5cf6);
}

.overview-card:hover {
    transform: translateY(-4px);
    box-shadow: 0 12px 25px -5px rgba(0, 0, 0, 0.15);
    border-color: #3b82f6;
}

.card-header {
    display: flex;
    justify-content: between;
    align-items: center;
    margin-bottom: 1rem;
}

.card-icon {
    width: 3rem;
    height: 3rem;
    border-radius: 0.75rem;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    margin-right: 1rem;
}

.card-title {
    font-size: 1rem;
    font-weight: 600;
    color: #6b7280;
    margin: 0;
}

.card-value {
    font-size: 2.5rem;
    font-weight: 700;
    color: #1f2937;
    margin-bottom: 0.5rem;
}

.card-trend {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.875rem;
}

.trend-up {
    color: #10b981;
}

.trend-down {
    color: #ef4444;
}

.trend-neutral {
    color: #6b7280;
}

/* 申请管理卡片 */
.application-card .card-icon {
    background: linear-gradient(135deg, #3b82f6, #1d4ed8);
    color: white;
}

/* 设备管理卡片 */
.device-card .card-icon {
    background: linear-gradient(135deg, #10b981, #059669);
    color: white;
}

/* 审批效率卡片 */
.efficiency-card .card-icon {
    background: linear-gradient(135deg, #f59e0b, #d97706);
    color: white;
}



/* 快速操作区域 */
.quick-actions-section {
    margin-bottom: 2rem;
}

.quick-actions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
}

.quick-action-btn {
    background: white;
    border: 2px solid #e5e7eb;
    border-radius: 0.75rem;
    padding: 1.5rem;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s ease;
    text-decoration: none;
    color: #374151;
}

.quick-action-btn:hover {
    border-color: #3b82f6;
    background: #f8fafc;
    transform: translateY(-2px);
    box-shadow: 0 8px 15px -3px rgba(0, 0, 0, 0.1);
}

.quick-action-icon {
    font-size: 2rem;
    margin-bottom: 0.5rem;
    display: block;
}

.quick-action-title {
    font-weight: 600;
    margin-bottom: 0.25rem;
}

.quick-action-desc {
    font-size: 0.875rem;
    color: #6b7280;
}

/* 数据可视化区域 */
.charts-section {
    margin-bottom: 2rem;
}

.charts-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.chart-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.chart-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.chart-container {
    position: relative;
    height: 300px;
}

/* 最新动态区域 */
.activity-section {
    margin-bottom: 2rem;
}

.activity-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 1.5rem;
}

.activity-card {
    background: white;
    border-radius: 1rem;
    padding: 1.5rem;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.activity-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e5e7eb;
}

.activity-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.activity-refresh-btn {
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 0.5rem;
    transition: all 0.2s ease;
    color: #6b7280;
}

.activity-refresh-btn:hover {
    background: #f3f4f6;
    color: #3b82f6;
}

.activity-refresh-btn:active {
    transform: scale(0.95);
}

.refresh-icon {
    display: inline-block;
    transition: transform 0.3s ease;
}

.activity-refresh-btn:hover .refresh-icon {
    transform: rotate(180deg);
}

.activity-refresh-btn.refreshing .refresh-icon {
    animation: spin 1s linear infinite;
}

@keyframes spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
}

.activity-list {
    max-height: 300px;
    overflow-y: auto;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 0.75rem 0;
    border-bottom: 1px solid #f3f4f6;
}

.activity-item:last-child {
    border-bottom: none;
}

.activity-icon {
    width: 2rem;
    height: 2rem;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 0.875rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-text {
    font-size: 0.875rem;
    color: #374151;
    margin-bottom: 0.25rem;
}

.activity-time {
    font-size: 0.75rem;
    color: #9ca3af;
}

/* 活动类型样式 */
.activity-application {
    border-left: 3px solid #3b82f6;
    padding-left: 0.5rem;
}

.activity-maintenance {
    border-left: 3px solid #8b5cf6;
    padding-left: 0.5rem;
}

.activity-device {
    border-left: 3px solid #10b981;
    padding-left: 0.5rem;
}

.activity-default {
    border-left: 3px solid #e5e7eb;
    padding-left: 0.5rem;
}

/* 活动图标增强 */
.activity-icon {
    border-radius: 50%;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s ease;
}

.activity-item:hover .activity-icon {
    transform: scale(1.1);
}

/* 活动文本样式 */
.activity-text {
    line-height: 1.4;
    word-break: break-word;
}

/* 滚动条美化 */
.activity-list::-webkit-scrollbar {
    width: 6px;
}

.activity-list::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 3px;
}

.activity-list::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 响应式设计 */
@media (max-width: 768px) {
    .dashboard-container {
        padding: 1rem;
    }
    
    .welcome-content {
        flex-direction: column;
        text-align: center;
    }
    
    .welcome-info {
        align-items: center;
    }
    
    .overview-grid {
        grid-template-columns: 1fr;
    }
    
    .charts-grid {
        grid-template-columns: 1fr;
    }
    
    .chart-container {
        height: 250px;
    }
    
    .activity-grid {
        grid-template-columns: 1fr;
    }
    
    .quick-actions-grid {
        grid-template-columns: repeat(2, 1fr);
    }
}

@media (max-width: 480px) {
    .welcome-text h1 {
        font-size: 1.5rem;
    }
    
    .card-value {
        font-size: 2rem;
    }
    
    .quick-actions-grid {
        grid-template-columns: 1fr;
    }
}

/* 加载状态 */
.loading-skeleton {
    background: linear-gradient(90deg, #f0f0f0 25%, #e0e0e0 50%, #f0f0f0 75%);
    background-size: 200% 100%;
    animation: loading 1.5s infinite;
}

@keyframes loading {
    0% {
        background-position: 200% 0;
    }
    100% {
        background-position: -200% 0;
    }
}

/* 空状态 */
.empty-state {
    text-align: center;
    padding: 2rem;
    color: #6b7280;
}

.empty-state-icon {
    font-size: 3rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

.empty-state-text {
    font-size: 1rem;
    margin-bottom: 0.5rem;
}

.empty-state-desc {
    font-size: 0.875rem;
    opacity: 0.7;
}
