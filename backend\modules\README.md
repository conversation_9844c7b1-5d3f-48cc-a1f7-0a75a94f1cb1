# 系统模块架构说明

本文档说明了管理系统的模块组织结构和功能分类。

## 📁 文件夹结构

```
backend/modules/
├── auth/                    # 认证和授权模块
│   ├── auth-service.js      # JWT认证服务
│   ├── session-manager.js   # 会话管理器
│   ├── access-control.js    # 访问控制和权限管理
│   └── password-policy.js   # 密码策略管理
├── security/                # 安全防护模块
│   ├── file-security.js     # 文件安全验证和恶意软件扫描
│   └── validation-middleware.js # 输入验证中间件
├── database/                # 数据库相关模块
│   ├── database-optimizer.js      # 数据库性能优化
│   ├── data-consistency-checker.js # 数据一致性检查
│   └── consistency-scheduler.js    # 一致性检查调度器
├── backup/                  # 备份恢复模块
│   ├── backup-manager.js    # 备份管理器
│   └── backup-scheduler.js  # 备份调度器
├── monitoring/              # 监控和日志模块
│   ├── performance-monitor.js # 实时性能监控
│   ├── logger.js           # 结构化日志系统
│   └── error-handler.js    # 统一错误处理
├── business/                # 业务逻辑模块
│   ├── device-health.js     # 设备健康监控
│   ├── device-management.js # 设备管理
│   ├── maintenance-management.js # 维修保养管理
│   └── export-service.js    # 数据导出服务
├── infrastructure/          # 基础设施模块
│   ├── cache-manager.js     # 缓存管理器
│   └── system-integration.js # 系统集成和统一入口
└── index.js                # 模块统一导出文件
```

## 🔧 模块功能说明

### 🔐 认证和授权 (auth/)
- **JWT认证**: 双token机制，自动刷新，黑名单管理
- **会话管理**: 安全会话验证，异常检测
- **权限控制**: 基于角色的细粒度权限管理
- **密码策略**: 简化的6位密码验证策略

### 🛡️ 安全防护 (security/)
- **文件安全**: 恶意软件扫描，文件类型验证，隔离机制
- **输入验证**: 防SQL注入，防XSS攻击，统一验证规则

### 🗄️ 数据库管理 (database/)
- **性能优化**: 50个索引优化，慢查询监控
- **一致性检查**: 自动数据完整性验证和修复
- **调度管理**: 定期维护任务调度

### 💾 备份恢复 (backup/)
- **自动备份**: 每日数据库备份，压缩存储
- **备份调度**: 灵活的备份策略配置
- **恢复验证**: 备份完整性检查

### 📊 监控日志 (monitoring/)
- **性能监控**: CPU、内存、磁盘实时监控
- **结构化日志**: JSON格式，自动轮转，分类存储
- **错误处理**: 统一错误格式，详细错误追踪

### 💼 业务逻辑 (business/)
- **设备管理**: 设备健康状态监控和管理
- **维修保养**: 维修记录和保养计划管理
- **数据导出**: 多格式数据导出服务

### 🏗️ 基础设施 (infrastructure/)
- **缓存管理**: LRU算法，内存限制，标签分组
- **系统集成**: 统一模块集成和配置管理

## 📦 使用方式

### 方式一：分组导入
```javascript
const { auth, security, monitoring } = require('./modules');

// 使用认证模块
const authService = new auth.AuthService();
const sessionManager = new auth.SessionManager();

// 使用安全模块
const fileSecurity = new security.FileSecurity();
```

### 方式二：直接导入（向后兼容）
```javascript
const { AuthService, FileSecurity, Logger } = require('./modules');

const authService = new AuthService();
const fileSecurity = new FileSecurity();
const logger = new Logger();
```

### 方式三：单独导入
```javascript
const AuthService = require('./modules/auth/auth-service');
const FileSecurity = require('./modules/security/file-security');
```

## 🔗 模块依赖关系

```
system-integration.js (核心集成模块)
├── auth/
│   ├── auth-service.js
│   ├── session-manager.js
│   ├── access-control.js
│   └── password-policy.js
├── security/
│   ├── file-security.js
│   └── validation-middleware.js
├── database/
│   ├── database-optimizer.js
│   ├── data-consistency-checker.js
│   └── consistency-scheduler.js
├── backup/
│   ├── backup-manager.js
│   └── backup-scheduler.js
├── monitoring/
│   ├── performance-monitor.js
│   ├── logger.js
│   └── error-handler.js
└── infrastructure/
    └── cache-manager.js
```

## 🎯 设计原则

1. **功能分离**: 按功能领域组织模块，避免功能混杂
2. **高内聚低耦合**: 模块内部功能紧密相关，模块间依赖最小化
3. **向后兼容**: 保持原有导入方式可用，平滑迁移
4. **统一管理**: 通过system-integration.js统一集成和配置
5. **可扩展性**: 新功能可以轻松添加到对应分类中

## 🚀 优势

- **代码组织清晰**: 按功能分类，易于查找和维护
- **团队协作友好**: 不同团队可以专注于不同模块
- **测试更容易**: 模块独立，单元测试更简单
- **部署灵活**: 可以选择性部署某些模块
- **文档维护**: 每个模块职责明确，文档更容易维护
