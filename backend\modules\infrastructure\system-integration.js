/**
 * 系统集成模块
 * 安全地集成所有新的优化模块到现有系统中
 * 确保向后兼容性和系统稳定性
 */

// 导入新的优化模块
const AuthService = require('../auth/auth-service');
const ValidationMiddleware = require('../security/validation-middleware');
const FileSecurity = require('../security/file-security');
const DatabaseOptimizer = require('../database/database-optimizer');
const CacheManager = require('./cache-manager');
const ErrorHandler = require('../monitoring/error-handler');
const Logger = require('../monitoring/logger');
const DataConsistencyChecker = require('../database/data-consistency-checker');
const ConsistencyScheduler = require('../database/consistency-scheduler');
const BackupManager = require('../backup/backup-manager');
const BackupScheduler = require('../backup/backup-scheduler');
const PasswordPolicy = require('../auth/password-policy');
const SessionManager = require('../auth/session-manager');
const AccessControl = require('../auth/access-control');
const PerformanceMonitor = require('../monitoring/performance-monitor');

// 导入统一日志工具
const consoleLogger = require('../../utils/console-logger');

class SystemIntegration {
    constructor(app, database, dataAccess) {
        this.app = app;
        this.database = database;
        this.dataAccess = dataAccess;
        
        // 初始化新模块
        this.initializeModules();
        
        // 集成中间件
        this.integrateMiddleware();
        
        // 集成路由增强
        this.integrateRouteEnhancements();
    }

    /**
     * 初始化所有新模块
     */
    initializeModules() {
        try {
            // 初始化日志系统
            this.logger = new Logger({
                level: process.env.LOG_LEVEL || 'info',
                enableConsole: true,
                enableFile: true,
                format: 'json',
                silentMode: true // 启用静默模式，只记录到文件
            });

            // 初始化错误处理器
            this.errorHandler = new ErrorHandler(this.logger);

            // 初始化密码策略管理器
            this.passwordPolicy = new PasswordPolicy(this.logger);

            // 初始化访问控制
            this.accessControl = new AccessControl(this.logger);

            // 初始化性能监控器
            this.performanceMonitor = new PerformanceMonitor(this.logger);

            // 初始化会话管理器
            this.sessionManager = new SessionManager(this.logger);

            // 初始化认证服务
            this.authService = new AuthService(this.passwordPolicy);

            // 初始化验证中间件
            this.validationMiddleware = new ValidationMiddleware();

            // 初始化文件安全
            this.fileSecurity = new FileSecurity();

            // 初始化数据库优化器
            this.dbOptimizer = new DatabaseOptimizer(this.database);

            // 初始化缓存管理器
            this.cacheManager = new CacheManager({
                maxSize: 1000,
                defaultTTL: 300000, // 5分钟
                maxMemoryMB: 100
            });

            // 初始化数据一致性检查器
            this.dataConsistencyChecker = new DataConsistencyChecker(this.dataAccess, this.logger);

            // 初始化数据一致性检查调度器
            this.consistencyScheduler = new ConsistencyScheduler(this.dataConsistencyChecker, this.logger);

            // 启动数据一致性检查调度器
            this.consistencyScheduler.start({
                quickCheckInterval: 2 * 60 * 60 * 1000, // 2小时检查一次
                autoRepair: true,
                repairOptions: {
                    removeOrphanedFiles: false, // 不自动删除孤立文件，需要手动确认
                    fixMissingFileRecords: true // 自动修复缺失文件记录
                }
            });

            // 初始化备份管理器
            this.backupManager = new BackupManager(this.dataAccess, this.logger);

            // 初始化备份调度器
            this.backupScheduler = new BackupScheduler(this.backupManager, this.logger);

            // 启动备份调度器
            this.backupScheduler.start({
                dailyBackupTime: '03:00', // 凌晨3点执行每日备份
                incrementalInterval: 6 * 60 * 60 * 1000, // 6小时执行增量备份
                autoBackup: true
            });

            this.logger.system('System modules initialized successfully');
            consoleLogger.systemSuccess('系统优化模块初始化成功');
        } catch (error) {
            consoleLogger.systemError('Failed to initialize system modules:', error);
            throw error;
        }
    }

    /**
     * 集成中间件
     */
    integrateMiddleware() {
        try {
            // 1. 日志中间件（最早）
            this.app.use(this.logger.expressMiddleware());

            // 2. 全局错误处理初始化
            this.errorHandler.initializeGlobalHandlers();

            // 3. JWT认证中间件
            this.app.use('/api', this.createJWTMiddleware());

            this.logger.system('Middleware integration completed');
            consoleLogger.systemSuccess('中间件集成完成');
        } catch (error) {
            this.logger.error('Failed to integrate middleware', { error: error.message });
            throw error;
        }
    }

    /**
     * 创建JWT认证中间件
     */
    createJWTMiddleware() {
        return (req, res, next) => {
            // 跳过不需要认证的路由
            const publicRoutes = [
                '/api/login',
                '/api/register',
                '/api/health',
                '/api/status',
                '/api/auth/login',
                '/api/auth/refresh'
            ];

            // 暂时跳过dashboard路由，使用原有认证机制
            const legacyRoutes = [
                '/api/dashboard',
                '/api/applications',
                '/api/devices',
                '/api/maintenance',
                '/api/users',
                '/api/departments',
                '/api/factories',
                '/api/export',
                '/api/upload',
                '/api/device-health'
            ];

            const requestPath = req.originalUrl || req.url;
            const pathOnly = requestPath.split('?')[0]; // 移除查询参数

            if (publicRoutes.includes(pathOnly)) {
                return next();
            }

            // 检查是否为legacy路由，如果是则跳过JWT验证
            const isLegacyRoute = legacyRoutes.some(route => pathOnly.startsWith(route));
            if (isLegacyRoute) {
                return next();
            }

            // 检查Authorization头
            const authHeader = req.headers.authorization;
            if (!authHeader || !authHeader.startsWith('Bearer ')) {
                // 如果没有JWT token，检查session（向后兼容）
                if (req.session && req.session.user) {
                    req.user = req.session.user;
                    return next();
                }

                // 对于原有的API路由，如果有username参数且session存在，允许通过
                if (req.query.username && req.session && req.session.username) {
                    req.user = {
                        id: req.session.userId || req.session.user?.id,
                        username: req.session.username,
                        role: req.session.role || req.query.role,
                        department: req.session.department
                    };
                    return next();
                }

                return res.status(401).json({
                    success: false,
                    message: '未提供认证令牌',
                    code: 'NO_TOKEN'
                });
            }

            const token = authHeader.substring(7);
            const decoded = this.authService.verifyAccessToken(token);

            if (!decoded) {
                return res.status(401).json({
                    success: false,
                    message: '无效的认证令牌',
                    code: 'INVALID_TOKEN'
                });
            }

            // 将用户信息添加到请求对象
            req.user = decoded;
            next();
        };
    }

    /**
     * 集成路由增强
     */
    integrateRouteEnhancements() {
        try {
            // 添加新的认证路由
            this.addAuthRoutes();

            // 添加系统监控路由
            this.addMonitoringRoutes();

            // 添加文件上传增强
            this.enhanceFileUpload();

            // 添加缓存管理路由
            this.addCacheRoutes();

            // 注意：错误处理中间件应该在所有路由之后添加
            // 这里只是注册，实际添加在server.js的最后

            this.logger.system('Route enhancements completed');
            consoleLogger.systemSuccess('路由增强完成');
        } catch (error) {
            this.logger.error('Failed to integrate route enhancements', { error: error.message });
            throw error;
        }
    }

    /**
     * 添加认证路由
     */
    addAuthRoutes() {
        // JWT登录路由
        this.app.post('/api/auth/login', 
            this.validationMiddleware.validate({
                body: {
                    username: { required: true, type: 'string', minLength: 1 },
                    password: { required: true, type: 'string', minLength: 1 }
                }
            }),
            this.errorHandler.asyncWrapper(async (req, res) => {
                const { username, password } = req.body;
                
                const result = await this.authService.login(
                    username, 
                    password, 
                    (username) => this.dataAccess.getUserByUsername(username)
                );

                if (result.success) {
                    this.logger.security('User login successful', { 
                        userId: result.user.id, 
                        username: result.user.username 
                    });
                } else {
                    this.logger.security('User login failed', { 
                        username, 
                        reason: result.message 
                    });
                }

                res.json(result);
            })
        );

        // Token刷新路由
        this.app.post('/api/auth/refresh',
            this.validationMiddleware.validate({
                body: {
                    refreshToken: { required: true, type: 'string' }
                }
            }),
            this.errorHandler.asyncWrapper(async (req, res) => {
                const { refreshToken } = req.body;
                
                const result = await this.authService.refreshAccessToken(
                    refreshToken,
                    (userId) => this.dataAccess.getUserById(userId)
                );

                if (result) {
                    res.json({ success: true, ...result });
                } else {
                    res.status(401).json({ 
                        success: false, 
                        message: '刷新令牌无效或已过期' 
                    });
                }
            })
        );

        // 登出路由
        this.app.post('/api/auth/logout', (req, res) => {
            const authHeader = req.headers.authorization;
            const refreshToken = req.body.refreshToken;
            
            if (authHeader && authHeader.startsWith('Bearer ')) {
                const accessToken = authHeader.substring(7);
                this.authService.logout(accessToken, refreshToken);
            }

            // 清除session（向后兼容）
            if (req.session) {
                req.session.destroy();
            }

            this.logger.security('User logout', { userId: req.user?.userId });
            res.json({ success: true, message: '登出成功' });
        });
    }

    /**
     * 添加系统监控路由
     */
    addMonitoringRoutes() {
        // 系统健康检查
        this.app.get('/api/health', (req, res) => {
            const health = {
                status: 'healthy',
                timestamp: new Date().toISOString(),
                uptime: process.uptime(),
                memory: process.memoryUsage(),
                version: process.env.npm_package_version || '1.0.0'
            };

            res.json(health);
        });

        // 系统统计信息
        this.app.get('/api/system/stats', (req, res) => {
            const stats = {
                cache: this.cacheManager.getStats(),
                database: this.dbOptimizer.getDatabasePerformanceInfo(),
                errors: this.errorHandler.getErrorStats(),
                auth: this.authService.getTokenStats(),
                logs: this.logger.getLogStats()
            };

            res.json({ success: true, data: stats });
        });

        // 慢查询报告
        this.app.get('/api/system/slow-queries', (req, res) => {
            const slowQueries = this.dbOptimizer.getSlowQueries();
            res.json({ success: true, data: slowQueries });
        });

        // 数据库性能调优API
        // 分析查询计划
        this.app.post('/api/system/database/analyze-query', this.errorHandler.asyncWrapper(async (req, res) => {
            const { sql, params = [] } = req.body;
            const result = this.dbOptimizer.analyzeQueryPlan(sql, params);
            res.json(result);
        }));

        // 获取表统计信息
        this.app.get('/api/system/database/table-stats', (req, res) => {
            const stats = this.dbOptimizer.getTableStatistics();
            res.json({ success: true, data: stats });
        });

        // 获取优化建议
        this.app.get('/api/system/database/optimization-suggestions', (req, res) => {
            const suggestions = this.dbOptimizer.getOptimizationSuggestions();
            res.json({ success: true, data: suggestions });
        });

        // 执行性能基准测试
        this.app.post('/api/system/database/benchmark', this.errorHandler.asyncWrapper(async (req, res) => {
            const result = await this.dbOptimizer.runPerformanceBenchmark();
            res.json(result);
        }));

        // 手动触发数据库维护
        this.app.post('/api/system/database/maintenance', (req, res) => {
            this.dbOptimizer.performMaintenance();
            res.json({ success: true, message: '数据库维护已执行' });
        });

        // 数据一致性检查
        this.app.get('/api/system/consistency-check', this.errorHandler.asyncWrapper(async (req, res) => {
            const result = await this.dataConsistencyChecker.performFullConsistencyCheck();
            res.json(result);
        }));

        // 数据一致性统计
        this.app.get('/api/system/consistency-stats', (req, res) => {
            const stats = this.dataConsistencyChecker.getStats();
            res.json({ success: true, data: stats });
        });

        // 修复数据一致性问题
        this.app.post('/api/system/repair-consistency', this.errorHandler.asyncWrapper(async (req, res) => {
            const { removeOrphanedFiles = false, fixMissingFileRecords = false } = req.body;
            const result = await this.dataConsistencyChecker.repairInconsistencies({
                removeOrphanedFiles,
                fixMissingFileRecords
            });
            res.json(result);
        }));

        // 数据一致性调度器状态
        this.app.get('/api/system/consistency-scheduler/status', (req, res) => {
            const status = this.consistencyScheduler.getStatus();
            res.json({ success: true, data: status });
        });

        // 手动触发数据一致性检查
        this.app.post('/api/system/consistency-scheduler/trigger', this.errorHandler.asyncWrapper(async (req, res) => {
            const { type = 'full' } = req.body;
            await this.consistencyScheduler.triggerManualCheck(type);
            res.json({ success: true, message: `${type === 'quick' ? '快速' : '完整'}检查已触发` });
        }));

        // 备份管理API
        // 执行手动备份
        this.app.post('/api/system/backup/create', this.errorHandler.asyncWrapper(async (req, res) => {
            const result = await this.backupManager.performFullBackup();
            res.json(result);
        }));

        // 列出所有备份
        this.app.get('/api/system/backup/list', this.errorHandler.asyncWrapper(async (req, res) => {
            const backups = await this.backupManager.listBackups();
            res.json({ success: true, data: backups });
        }));

        // 恢复备份
        this.app.post('/api/system/backup/restore', this.errorHandler.asyncWrapper(async (req, res) => {
            const { backupName, options = {} } = req.body;
            const result = await this.backupManager.restoreBackup(backupName, options);
            res.json(result);
        }));

        // 备份统计信息
        this.app.get('/api/system/backup/stats', (req, res) => {
            const stats = this.backupManager.getStats();
            res.json({ success: true, data: stats });
        });

        // 备份调度器状态
        this.app.get('/api/system/backup-scheduler/status', (req, res) => {
            const status = this.backupScheduler.getStatus();
            res.json({ success: true, data: status });
        });

        // 手动触发备份
        this.app.post('/api/system/backup-scheduler/trigger', this.errorHandler.asyncWrapper(async (req, res) => {
            const { type = 'manual' } = req.body;
            await this.backupScheduler.triggerManualBackup(type);
            res.json({ success: true, message: '备份已触发' });
        }));

        // 密码策略API
        // 验证密码复杂度
        this.app.post('/api/system/password/validate', (req, res) => {
            const { password, userInfo = {} } = req.body;
            const result = this.passwordPolicy.validatePasswordComplexity(password, userInfo);
            res.json({ success: true, data: result });
        });

        // 生成安全密码
        this.app.post('/api/system/password/generate', (req, res) => {
            const { length = 12 } = req.body;
            const password = this.passwordPolicy.generateSecurePassword(length);
            res.json({ success: true, data: { password } });
        });

        // 获取密码策略配置
        this.app.get('/api/system/password/policy', (req, res) => {
            const policy = this.passwordPolicy.getPolicy();
            res.json({ success: true, data: policy });
        });

        // 更新密码策略配置
        this.app.put('/api/system/password/policy', (req, res) => {
            const newConfig = req.body;
            this.passwordPolicy.updatePolicy(newConfig);
            res.json({ success: true, message: '密码策略已更新' });
        });

        // 检查账户锁定状态
        this.app.post('/api/system/password/check-lockout', (req, res) => {
            const { username, ip } = req.body;
            const lockStatus = this.passwordPolicy.isAccountLocked(username, ip);
            res.json({ success: true, data: lockStatus });
        });

        // 会话管理API
        // 获取会话统计信息
        this.app.get('/api/system/sessions/stats', (req, res) => {
            const stats = this.sessionManager.getSessionStats();
            res.json({ success: true, data: stats });
        });

        // 获取会话详情
        this.app.get('/api/system/sessions/:sessionId', (req, res) => {
            const { sessionId } = req.params;
            const session = this.sessionManager.getSessionDetails(sessionId);
            if (session) {
                res.json({ success: true, data: session });
            } else {
                res.status(404).json({ success: false, message: '会话不存在' });
            }
        });

        // 终止会话
        this.app.delete('/api/system/sessions/:sessionId', (req, res) => {
            const { sessionId } = req.params;
            const terminated = this.sessionManager.terminateSession(sessionId, 'admin_action');
            if (terminated) {
                res.json({ success: true, message: '会话已终止' });
            } else {
                res.status(404).json({ success: false, message: '会话不存在' });
            }
        });

        // 终止用户的所有会话
        this.app.delete('/api/system/sessions/user/:userId', (req, res) => {
            const { userId } = req.params;
            const count = this.sessionManager.terminateUserSessions(userId, 'admin_action');
            res.json({ success: true, message: `已终止${count}个会话` });
        });

        // 获取会话管理器配置
        this.app.get('/api/system/sessions/config', (req, res) => {
            const config = this.sessionManager.getConfig();
            res.json({ success: true, data: config });
        });

        // 更新会话管理器配置
        this.app.put('/api/system/sessions/config', (req, res) => {
            const newConfig = req.body;
            this.sessionManager.updateConfig(newConfig);
            res.json({ success: true, message: '会话管理配置已更新' });
        });

        // 访问控制API
        // 获取角色权限矩阵
        this.app.get('/api/system/access-control/roles', (req, res) => {
            const matrix = this.accessControl.getRolePermissionMatrix();
            res.json({ success: true, data: matrix });
        });

        // 检查用户权限
        this.app.post('/api/system/access-control/check-permission', (req, res) => {
            const { userRole, permission } = req.body;
            const hasPermission = this.accessControl.hasPermission(userRole, permission);
            res.json({ success: true, data: { hasPermission } });
        });

        // 检查API访问权限
        this.app.post('/api/system/access-control/check-api', (req, res) => {
            const { userRole, method, path } = req.body;
            const result = this.accessControl.canAccessAPI(userRole, method, path);
            res.json({ success: true, data: result });
        });

        // 获取访问控制统计信息
        this.app.get('/api/system/access-control/stats', (req, res) => {
            const stats = this.accessControl.getAccessControlStats();
            res.json({ success: true, data: stats });
        });

        // 为角色添加权限
        this.app.post('/api/system/access-control/roles/:role/permissions', (req, res) => {
            const { role } = req.params;
            const { permission } = req.body;
            this.accessControl.addPermissionToRole(role, permission);
            res.json({ success: true, message: '权限已添加到角色' });
        });

        // 从角色移除权限
        this.app.delete('/api/system/access-control/roles/:role/permissions/:permission', (req, res) => {
            const { role, permission } = req.params;
            this.accessControl.removePermissionFromRole(role, permission);
            res.json({ success: true, message: '权限已从角色移除' });
        });

        // 文件安全增强API
        // 获取扫描统计信息
        this.app.get('/api/system/file-security/scan-stats', (req, res) => {
            const stats = this.fileSecurity.getScanStats();
            res.json({ success: true, data: stats });
        });

        // 获取隔离文件列表
        this.app.get('/api/system/file-security/quarantine', (req, res) => {
            const files = this.fileSecurity.getQuarantinedFiles();
            res.json({ success: true, data: files });
        });

        // 删除隔离文件
        this.app.delete('/api/system/file-security/quarantine/:fileName', (req, res) => {
            const { fileName } = req.params;
            const result = this.fileSecurity.deleteQuarantinedFile(fileName);
            res.json(result);
        });

        // 恢复隔离文件
        this.app.post('/api/system/file-security/quarantine/:fileName/restore', (req, res) => {
            const { fileName } = req.params;
            const { targetPath } = req.body;
            const result = this.fileSecurity.restoreQuarantinedFile(fileName, targetPath);
            res.json(result);
        });

        // 清理隔离区
        this.app.post('/api/system/file-security/quarantine/cleanup', (req, res) => {
            const { daysOld = 30 } = req.body;
            const result = this.fileSecurity.cleanupQuarantine(daysOld);
            res.json(result);
        });

        // 获取安全报告
        this.app.get('/api/system/file-security/report', (req, res) => {
            const report = this.fileSecurity.getSecurityReport();
            res.json({ success: true, data: report });
        });

        // 更新扫描配置
        this.app.put('/api/system/file-security/config', (req, res) => {
            const newConfig = req.body;
            this.fileSecurity.updateScanConfig(newConfig);
            res.json({ success: true, message: '扫描配置已更新' });
        });

        // 获取扫描配置
        this.app.get('/api/system/file-security/config', (req, res) => {
            const config = this.fileSecurity.getScanConfig();
            res.json({ success: true, data: config });
        });

        // 性能监控API
        // 获取当前性能指标
        this.app.get('/api/system/performance/current', (req, res) => {
            const metrics = this.performanceMonitor.getCurrentMetrics();
            res.json({ success: true, data: metrics });
        });

        // 获取历史性能数据
        this.app.get('/api/system/performance/history/:type', (req, res) => {
            const { type } = req.params;
            const { limit = 50 } = req.query;
            const history = this.performanceMonitor.getHistoryData(type, parseInt(limit));
            res.json({ success: true, data: history });
        });

        // 获取性能报告
        this.app.get('/api/system/performance/report', (req, res) => {
            const report = this.performanceMonitor.getPerformanceReport();
            res.json({ success: true, data: report });
        });

        // 获取性能监控配置
        this.app.get('/api/system/performance/config', (req, res) => {
            const config = this.performanceMonitor.getConfig();
            res.json({ success: true, data: config });
        });

        // 更新性能监控配置
        this.app.put('/api/system/performance/config', (req, res) => {
            const newConfig = req.body;
            this.performanceMonitor.updateConfig(newConfig);
            res.json({ success: true, message: '性能监控配置已更新' });
        });

        // 启动/停止性能监控
        this.app.post('/api/system/performance/control', (req, res) => {
            const { action } = req.body;

            if (action === 'start') {
                this.performanceMonitor.startMonitoring();
                res.json({ success: true, message: '性能监控已启动' });
            } else if (action === 'stop') {
                this.performanceMonitor.stopMonitoring();
                res.json({ success: true, message: '性能监控已停止' });
            } else {
                res.status(400).json({ success: false, message: '无效的操作' });
            }
        });
    }

    /**
     * 增强文件上传
     */
    enhanceFileUpload() {
        // 创建增强的multer配置
        const multer = require('multer');
        
        const storage = multer.diskStorage({
            destination: (req, file, cb) => {
                const uploadDir = path.join(__dirname, '../../uploads');
                if (!require('fs').existsSync(uploadDir)) {
                    require('fs').mkdirSync(uploadDir, { recursive: true });
                }
                cb(null, uploadDir);
            },
            filename: (req, file, cb) => {
                const safeName = this.fileSecurity.generateSafeFileName(file.originalname);
                cb(null, safeName);
            }
        });

        const upload = multer({
            storage: storage,
            limits: {
                fileSize: 15 * 1024 * 1024 // 15MB
            },
            fileFilter: (req, file, cb) => {
                if (this.fileSecurity.isAllowedType(file.mimetype)) {
                    cb(null, true);
                } else {
                    cb(new Error(`不支持的文件类型: ${file.mimetype}`), false);
                }
            }
        });

        // 增强的文件上传路由
        this.app.post('/api/upload/secure', 
            upload.single('file'),
            this.errorHandler.asyncWrapper(async (req, res) => {
                if (!req.file) {
                    throw this.errorHandler.createError(
                        this.errorHandler.errorTypes.VALIDATION_ERROR,
                        '没有上传文件',
                        {},
                        400
                    );
                }

                // 安全验证
                const validation = await this.fileSecurity.validateFile(req.file);
                
                if (!validation.isValid) {
                    // 删除不安全的文件
                    require('fs').unlinkSync(req.file.path);
                    
                    throw this.errorHandler.createError(
                        this.errorHandler.errorTypes.FILE_ERROR,
                        '文件安全验证失败',
                        { errors: validation.errors },
                        400
                    );
                }

                this.logger.userAction(req.user?.userId, 'file_upload', {
                    filename: req.file.filename,
                    originalName: req.file.originalname,
                    size: req.file.size,
                    hash: validation.fileInfo.hash
                });

                res.json({
                    success: true,
                    message: '文件上传成功',
                    file: {
                        filename: req.file.filename,
                        originalName: req.file.originalname,
                        size: req.file.size,
                        hash: validation.fileInfo.hash
                    }
                });
            })
        );
    }

    /**
     * 添加缓存管理路由
     */
    addCacheRoutes() {
        // 缓存统计
        this.app.get('/api/cache/stats', (req, res) => {
            const stats = this.cacheManager.getStats();
            res.json({ success: true, data: stats });
        });

        // 清理缓存
        this.app.delete('/api/cache/clear', (req, res) => {
            const { tags } = req.query;
            
            if (tags) {
                const cleared = this.cacheManager.deleteByTags(tags.split(','));
                this.logger.userAction(req.user?.userId, 'cache_clear_by_tags', { tags, cleared });
                res.json({ success: true, message: `清理了 ${cleared} 个缓存项` });
            } else {
                this.cacheManager.clear();
                this.logger.userAction(req.user?.userId, 'cache_clear_all');
                res.json({ success: true, message: '所有缓存已清理' });
            }
        });
    }

    /**
     * 获取集成的模块实例
     */
    getModules() {
        return {
            logger: this.logger,
            errorHandler: this.errorHandler,
            authService: this.authService,
            validationMiddleware: this.validationMiddleware,
            fileSecurity: this.fileSecurity,
            dbOptimizer: this.dbOptimizer,
            cacheManager: this.cacheManager,
            dataConsistencyChecker: this.dataConsistencyChecker,
            consistencyScheduler: this.consistencyScheduler,
            backupManager: this.backupManager,
            backupScheduler: this.backupScheduler,
            passwordPolicy: this.passwordPolicy,
            sessionManager: this.sessionManager,
            accessControl: this.accessControl,
            performanceMonitor: this.performanceMonitor
        };
    }

    /**
     * 创建优化的数据访问包装器
     */
    createOptimizedDataAccess() {
        const originalDataAccess = this.dataAccess;
        
        return {
            // 包装用户查询
            getUserByUsername: (username) => {
                const cacheKey = `user:username:${username}`;
                let user = this.cacheManager.get(cacheKey);
                
                if (!user) {
                    user = originalDataAccess.getUserByUsername(username);
                    if (user) {
                        this.cacheManager.set(cacheKey, user, { 
                            ttl: 300000, // 5分钟
                            tags: ['users', `user:${user.id}`] 
                        });
                    }
                }
                
                return user;
            },

            // 包装用户ID查询
            getUserById: (id) => {
                const cacheKey = `user:id:${id}`;
                let user = this.cacheManager.get(cacheKey);
                
                if (!user) {
                    user = originalDataAccess.getUserById(id);
                    if (user) {
                        this.cacheManager.set(cacheKey, user, { 
                            ttl: 300000,
                            tags: ['users', `user:${user.id}`] 
                        });
                    }
                }
                
                return user;
            },

            // 其他方法保持原样
            ...originalDataAccess
        };
    }
}

module.exports = SystemIntegration;
