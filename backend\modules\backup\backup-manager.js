const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { spawn } = require('child_process');

/**
 * 备份恢复管理器
 * 提供自动化数据备份和恢复功能
 */
class BackupManager {
    constructor(dataAccess, logger) {
        this.dataAccess = dataAccess;
        this.logger = logger;
        
        // 备份目录配置
        this.backupDir = path.join(__dirname, '../../backups');
        this.dbPath = path.join(__dirname, '../data/application_system.db');
        this.uploadsDir = path.join(__dirname, '../../uploads');
        this.archiveDir = path.join(__dirname, '../../archive');
        
        // 确保备份目录存在
        this.ensureBackupDirectory();
        
        // 备份配置
        this.config = {
            // 保留备份数量
            maxBackups: 30,
            // 备份压缩
            enableCompression: true,
            // 备份验证
            enableVerification: true,
            // 自动清理旧备份
            autoCleanup: true,
            // 备份文件命名格式
            backupNameFormat: 'backup_{timestamp}',
            // 增量备份间隔（小时）
            incrementalInterval: 6,
            // 完整备份间隔（天）
            fullBackupInterval: 7
        };
        
        // 备份统计
        this.stats = {
            totalBackups: 0,
            lastBackupTime: null,
            lastBackupSize: 0,
            totalBackupSize: 0,
            successfulBackups: 0,
            failedBackups: 0
        };
    }

    /**
     * 确保备份目录存在
     */
    ensureBackupDirectory() {
        if (!fs.existsSync(this.backupDir)) {
            fs.mkdirSync(this.backupDir, { recursive: true });
            this.logger.info('创建备份目录', { path: this.backupDir });
        }
    }

    /**
     * 执行完整备份
     */
    async performFullBackup(options = {}) {
        const startTime = Date.now();
        const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
        const backupName = `full_backup_${timestamp}`;
        const backupPath = path.join(this.backupDir, backupName);
        
        try {
            this.logger.info('开始执行完整备份', { backupName });
            
            // 创建备份目录
            fs.mkdirSync(backupPath, { recursive: true });
            
            const backupResult = {
                name: backupName,
                type: 'full',
                timestamp: new Date().toISOString(),
                startTime,
                components: {}
            };
            
            // 1. 备份数据库
            backupResult.components.database = await this.backupDatabase(backupPath);
            
            // 2. 备份上传文件
            backupResult.components.uploads = await this.backupUploads(backupPath);
            
            // 3. 备份归档文件
            backupResult.components.archive = await this.backupArchive(backupPath);
            
            // 4. 备份配置文件
            backupResult.components.config = await this.backupConfig(backupPath);
            
            // 5. 生成备份清单
            const manifest = await this.generateBackupManifest(backupPath, backupResult);
            
            // 6. 验证备份完整性
            if (this.config.enableVerification) {
                backupResult.verification = await this.verifyBackup(backupPath, manifest);
            }
            
            // 7. 压缩备份（如果启用）
            if (this.config.enableCompression) {
                backupResult.compression = await this.compressBackup(backupPath);
            }
            
            // 计算总耗时和大小
            backupResult.duration = Date.now() - startTime;
            backupResult.size = await this.calculateBackupSize(backupPath);
            backupResult.success = true;
            
            // 更新统计信息
            this.updateBackupStats(backupResult);
            
            // 自动清理旧备份
            if (this.config.autoCleanup) {
                await this.cleanupOldBackups();
            }
            
            this.logger.info('完整备份完成', {
                backupName,
                duration: backupResult.duration,
                size: backupResult.size
            });
            
            return {
                success: true,
                backup: backupResult
            };
            
        } catch (error) {
            this.logger.error('完整备份失败', {
                backupName,
                error: error.message,
                duration: Date.now() - startTime
            });
            
            // 清理失败的备份
            try {
                if (fs.existsSync(backupPath)) {
                    fs.rmSync(backupPath, { recursive: true, force: true });
                }
            } catch (cleanupError) {
                this.logger.error('清理失败备份时出错', { error: cleanupError.message });
            }
            
            this.stats.failedBackups++;
            
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 备份数据库
     */
    async backupDatabase(backupPath) {
        try {
            const dbBackupPath = path.join(backupPath, 'database');
            fs.mkdirSync(dbBackupPath, { recursive: true });
            
            // 复制主数据库文件
            const dbFileName = path.basename(this.dbPath);
            const targetDbPath = path.join(dbBackupPath, dbFileName);
            
            // 使用SQLite的备份API进行热备份
            const db = this.dataAccess.getDatabase();
            
            // 执行VACUUM INTO命令进行备份
            const backupDbPath = path.join(dbBackupPath, `${dbFileName}.backup`);
            db.exec(`VACUUM INTO '${backupDbPath.replace(/\\/g, '/')}'`);
            
            // 计算文件哈希
            const hash = await this.calculateFileHash(backupDbPath);
            
            return {
                success: true,
                files: [dbFileName],
                size: fs.statSync(backupDbPath).size,
                hash
            };
        } catch (error) {
            this.logger.error('数据库备份失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 备份上传文件
     */
    async backupUploads(backupPath) {
        try {
            const uploadsBackupPath = path.join(backupPath, 'uploads');
            
            if (!fs.existsSync(this.uploadsDir)) {
                return {
                    success: true,
                    files: [],
                    size: 0,
                    message: '上传目录不存在，跳过备份'
                };
            }
            
            // 复制整个uploads目录
            await this.copyDirectory(this.uploadsDir, uploadsBackupPath);
            
            const files = await this.getDirectoryFiles(uploadsBackupPath);
            const size = await this.calculateDirectorySize(uploadsBackupPath);
            
            return {
                success: true,
                files: files.length,
                size
            };
        } catch (error) {
            this.logger.error('上传文件备份失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 备份归档文件
     */
    async backupArchive(backupPath) {
        try {
            const archiveBackupPath = path.join(backupPath, 'archive');
            
            if (!fs.existsSync(this.archiveDir)) {
                return {
                    success: true,
                    files: [],
                    size: 0,
                    message: '归档目录不存在，跳过备份'
                };
            }
            
            // 复制整个archive目录
            await this.copyDirectory(this.archiveDir, archiveBackupPath);
            
            const files = await this.getDirectoryFiles(archiveBackupPath);
            const size = await this.calculateDirectorySize(archiveBackupPath);
            
            return {
                success: true,
                files: files.length,
                size
            };
        } catch (error) {
            this.logger.error('归档文件备份失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 备份配置文件
     */
    async backupConfig(backupPath) {
        try {
            const configBackupPath = path.join(backupPath, 'config');
            fs.mkdirSync(configBackupPath, { recursive: true });
            
            const configFiles = [
                'package.json',
                '.env.example'
            ];
            
            const backedUpFiles = [];
            
            for (const configFile of configFiles) {
                const sourcePath = path.join(__dirname, '../../', configFile);
                if (fs.existsSync(sourcePath)) {
                    const targetPath = path.join(configBackupPath, configFile);
                    fs.copyFileSync(sourcePath, targetPath);
                    backedUpFiles.push(configFile);
                }
            }
            
            const size = await this.calculateDirectorySize(configBackupPath);
            
            return {
                success: true,
                files: backedUpFiles,
                size
            };
        } catch (error) {
            this.logger.error('配置文件备份失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 生成备份清单
     */
    async generateBackupManifest(backupPath, backupResult) {
        const manifest = {
            version: '1.0',
            backup: backupResult,
            created: new Date().toISOString(),
            system: {
                platform: process.platform,
                nodeVersion: process.version,
                hostname: require('os').hostname()
            }
        };
        
        const manifestPath = path.join(backupPath, 'manifest.json');
        fs.writeFileSync(manifestPath, JSON.stringify(manifest, null, 2));
        
        return manifest;
    }

    /**
     * 验证备份完整性
     */
    async verifyBackup(backupPath, manifest) {
        try {
            const verification = {
                passed: true,
                checks: [],
                errors: []
            };
            
            // 检查必要文件是否存在
            const requiredFiles = [
                'database',
                'manifest.json'
            ];
            
            for (const file of requiredFiles) {
                const filePath = path.join(backupPath, file);
                if (fs.existsSync(filePath)) {
                    verification.checks.push(`${file}: 存在`);
                } else {
                    verification.passed = false;
                    verification.errors.push(`${file}: 缺失`);
                }
            }
            
            return verification;
        } catch (error) {
            return {
                passed: false,
                errors: [error.message]
            };
        }
    }

    /**
     * 压缩备份
     */
    async compressBackup(backupPath) {
        // 这里可以实现压缩逻辑，比如使用tar或zip
        // 为了简化，这里只是返回压缩信息
        return {
            enabled: false,
            message: '压缩功能待实现'
        };
    }

    /**
     * 计算备份大小
     */
    async calculateBackupSize(backupPath) {
        return await this.calculateDirectorySize(backupPath);
    }

    /**
     * 更新备份统计
     */
    updateBackupStats(backupResult) {
        this.stats.totalBackups++;
        this.stats.lastBackupTime = backupResult.timestamp;
        this.stats.lastBackupSize = backupResult.size;
        this.stats.totalBackupSize += backupResult.size;
        
        if (backupResult.success) {
            this.stats.successfulBackups++;
        } else {
            this.stats.failedBackups++;
        }
    }

    /**
     * 清理旧备份
     */
    async cleanupOldBackups() {
        try {
            const backups = await this.listBackups();
            
            if (backups.length > this.config.maxBackups) {
                // 按时间排序，删除最旧的备份
                backups.sort((a, b) => new Date(a.created) - new Date(b.created));
                
                const toDelete = backups.slice(0, backups.length - this.config.maxBackups);
                
                for (const backup of toDelete) {
                    const backupPath = path.join(this.backupDir, backup.name);
                    if (fs.existsSync(backupPath)) {
                        fs.rmSync(backupPath, { recursive: true, force: true });
                        this.logger.info('删除旧备份', { backup: backup.name });
                    }
                }
            }
        } catch (error) {
            this.logger.error('清理旧备份失败', { error: error.message });
        }
    }

    /**
     * 列出所有备份
     */
    async listBackups() {
        try {
            const backups = [];
            const items = fs.readdirSync(this.backupDir);
            
            for (const item of items) {
                const itemPath = path.join(this.backupDir, item);
                const stats = fs.statSync(itemPath);
                
                if (stats.isDirectory()) {
                    const manifestPath = path.join(itemPath, 'manifest.json');
                    
                    if (fs.existsSync(manifestPath)) {
                        try {
                            const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));
                            backups.push({
                                name: item,
                                created: manifest.created,
                                size: await this.calculateDirectorySize(itemPath),
                                type: manifest.backup?.type || 'unknown',
                                success: manifest.backup?.success || false
                            });
                        } catch (error) {
                            // 忽略损坏的清单文件
                        }
                    }
                }
            }
            
            return backups.sort((a, b) => new Date(b.created) - new Date(a.created));
        } catch (error) {
            this.logger.error('列出备份失败', { error: error.message });
            return [];
        }
    }

    /**
     * 工具方法：复制目录
     */
    async copyDirectory(source, target) {
        if (!fs.existsSync(target)) {
            fs.mkdirSync(target, { recursive: true });
        }
        
        const items = fs.readdirSync(source);
        
        for (const item of items) {
            const sourcePath = path.join(source, item);
            const targetPath = path.join(target, item);
            const stats = fs.statSync(sourcePath);
            
            if (stats.isDirectory()) {
                await this.copyDirectory(sourcePath, targetPath);
            } else {
                fs.copyFileSync(sourcePath, targetPath);
            }
        }
    }

    /**
     * 工具方法：计算目录大小
     */
    async calculateDirectorySize(dirPath) {
        let totalSize = 0;
        
        const traverse = (currentPath) => {
            const items = fs.readdirSync(currentPath);
            
            for (const item of items) {
                const itemPath = path.join(currentPath, item);
                const stats = fs.statSync(itemPath);
                
                if (stats.isDirectory()) {
                    traverse(itemPath);
                } else {
                    totalSize += stats.size;
                }
            }
        };
        
        if (fs.existsSync(dirPath)) {
            traverse(dirPath);
        }
        
        return totalSize;
    }

    /**
     * 工具方法：获取目录文件列表
     */
    async getDirectoryFiles(dirPath) {
        const files = [];
        
        const traverse = (currentPath) => {
            const items = fs.readdirSync(currentPath);
            
            for (const item of items) {
                const itemPath = path.join(currentPath, item);
                const stats = fs.statSync(itemPath);
                
                if (stats.isDirectory()) {
                    traverse(itemPath);
                } else {
                    files.push(itemPath);
                }
            }
        };
        
        if (fs.existsSync(dirPath)) {
            traverse(dirPath);
        }
        
        return files;
    }

    /**
     * 工具方法：计算文件哈希
     */
    async calculateFileHash(filePath) {
        return new Promise((resolve, reject) => {
            const hash = crypto.createHash('sha256');
            const stream = fs.createReadStream(filePath);
            
            stream.on('data', data => hash.update(data));
            stream.on('end', () => resolve(hash.digest('hex')));
            stream.on('error', reject);
        });
    }

    /**
     * 恢复备份
     */
    async restoreBackup(backupName, options = {}) {
        const startTime = Date.now();
        const backupPath = path.join(this.backupDir, backupName);

        try {
            this.logger.info('开始恢复备份', { backupName });

            // 检查备份是否存在
            if (!fs.existsSync(backupPath)) {
                throw new Error(`备份不存在: ${backupName}`);
            }

            // 读取备份清单
            const manifestPath = path.join(backupPath, 'manifest.json');
            if (!fs.existsSync(manifestPath)) {
                throw new Error('备份清单文件不存在');
            }

            const manifest = JSON.parse(fs.readFileSync(manifestPath, 'utf8'));

            const restoreResult = {
                backup: backupName,
                timestamp: new Date().toISOString(),
                startTime,
                components: {}
            };

            // 创建恢复前的备份
            if (options.createPreRestoreBackup !== false) {
                this.logger.info('创建恢复前备份');
                const preRestoreBackup = await this.performFullBackup();
                restoreResult.preRestoreBackup = preRestoreBackup.backup?.name;
            }

            // 1. 恢复数据库
            if (options.restoreDatabase !== false) {
                restoreResult.components.database = await this.restoreDatabase(backupPath);
            }

            // 2. 恢复上传文件
            if (options.restoreUploads !== false) {
                restoreResult.components.uploads = await this.restoreUploads(backupPath);
            }

            // 3. 恢复归档文件
            if (options.restoreArchive !== false) {
                restoreResult.components.archive = await this.restoreArchive(backupPath);
            }

            restoreResult.duration = Date.now() - startTime;
            restoreResult.success = true;

            this.logger.info('备份恢复完成', {
                backupName,
                duration: restoreResult.duration
            });

            return {
                success: true,
                restore: restoreResult
            };

        } catch (error) {
            this.logger.error('备份恢复失败', {
                backupName,
                error: error.message,
                duration: Date.now() - startTime
            });

            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 恢复数据库
     */
    async restoreDatabase(backupPath) {
        try {
            const dbBackupPath = path.join(backupPath, 'database');
            const backupDbFile = fs.readdirSync(dbBackupPath).find(file => file.endsWith('.backup'));

            if (!backupDbFile) {
                throw new Error('备份数据库文件不存在');
            }

            const sourceDbPath = path.join(dbBackupPath, backupDbFile);

            // 关闭当前数据库连接
            this.dataAccess.close();

            // 备份当前数据库
            const currentDbBackup = `${this.dbPath}.restore-backup-${Date.now()}`;
            if (fs.existsSync(this.dbPath)) {
                fs.copyFileSync(this.dbPath, currentDbBackup);
            }

            // 恢复数据库
            fs.copyFileSync(sourceDbPath, this.dbPath);

            // 重新初始化数据库连接
            this.dataAccess = new (require('../data-access'))();

            return {
                success: true,
                message: '数据库恢复成功',
                backupFile: currentDbBackup
            };
        } catch (error) {
            this.logger.error('数据库恢复失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 恢复上传文件
     */
    async restoreUploads(backupPath) {
        try {
            const uploadsBackupPath = path.join(backupPath, 'uploads');

            if (!fs.existsSync(uploadsBackupPath)) {
                return {
                    success: true,
                    message: '备份中无上传文件，跳过恢复'
                };
            }

            // 备份当前上传目录
            if (fs.existsSync(this.uploadsDir)) {
                const currentUploadsBackup = `${this.uploadsDir}.restore-backup-${Date.now()}`;
                await this.copyDirectory(this.uploadsDir, currentUploadsBackup);

                // 清空当前上传目录
                fs.rmSync(this.uploadsDir, { recursive: true, force: true });
            }

            // 恢复上传文件
            await this.copyDirectory(uploadsBackupPath, this.uploadsDir);

            const files = await this.getDirectoryFiles(this.uploadsDir);

            return {
                success: true,
                files: files.length,
                message: '上传文件恢复成功'
            };
        } catch (error) {
            this.logger.error('上传文件恢复失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 恢复归档文件
     */
    async restoreArchive(backupPath) {
        try {
            const archiveBackupPath = path.join(backupPath, 'archive');

            if (!fs.existsSync(archiveBackupPath)) {
                return {
                    success: true,
                    message: '备份中无归档文件，跳过恢复'
                };
            }

            // 备份当前归档目录
            if (fs.existsSync(this.archiveDir)) {
                const currentArchiveBackup = `${this.archiveDir}.restore-backup-${Date.now()}`;
                await this.copyDirectory(this.archiveDir, currentArchiveBackup);

                // 清空当前归档目录
                fs.rmSync(this.archiveDir, { recursive: true, force: true });
            }

            // 恢复归档文件
            await this.copyDirectory(archiveBackupPath, this.archiveDir);

            const files = await this.getDirectoryFiles(this.archiveDir);

            return {
                success: true,
                files: files.length,
                message: '归档文件恢复成功'
            };
        } catch (error) {
            this.logger.error('归档文件恢复失败', { error: error.message });
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 获取备份统计信息
     */
    getStats() {
        return { ...this.stats };
    }
}

module.exports = BackupManager;
