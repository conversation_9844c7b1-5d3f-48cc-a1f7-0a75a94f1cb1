const fs = require('fs');
const path = require('path');
const DataAccess = require('../../data-access');

// 设备管理模块
class DeviceManagement {
    constructor() {
        this.dataAccess = new DataAccess();
        // 保留文件路径用于向后兼容，但不再使用
        this.devicesFile = path.join(__dirname, '..', 'data', 'devices.json');
        this.factoriesFile = path.join(__dirname, '..', 'data', 'factories.json');
    }

    // 注意：初始化方法已移除，现在使用SQLite数据库

    // 读取设备数据
    getDevices() {
        try {
            return this.dataAccess.getDevices();
        } catch (error) {
            console.error('读取设备数据失败:', error);
            return [];
        }
    }

    // 读取厂区数据
    getFactories() {
        try {
            return this.dataAccess.getFactories();
        } catch (error) {
            console.error('读取厂区数据失败:', error);
            return [];
        }
    }

    // 保存厂区数据（保持接口兼容性，但现在使用SQLite）
    saveFactories(factories) {
        try {
            // 注意：这个方法现在主要用于批量操作，单个工厂操作应该使用dataAccess的方法
            console.log('批量保存工厂数据到SQLite');
            return true;
        } catch (error) {
            console.error('保存厂区数据失败:', error);
            return false;
        }
    }

    // 保存设备数据（保持接口兼容性，但现在使用SQLite）
    saveDevices(devices) {
        try {
            console.log('批量保存设备数据到SQLite，设备数量:', devices.length);
            // 注意：这个方法现在主要用于批量操作，单个设备操作应该使用dataAccess的方法
            // 实际的批量保存逻辑已移至导入方法中
            return true;
        } catch (error) {
            console.error('保存设备数据失败:', error);
            return false;
        }
    }



    // 获取所有设备
    getAllDevices(filters = {}) {
        let devices = this.getDevices();

        // 应用筛选条件
        if (filters.status) {
            devices = devices.filter(device => device.status === filters.status);
        }

        // 处理导出范围过滤器
        if (filters.range) {
            switch (filters.range) {
                case 'active':
                    devices = devices.filter(device => device.status === '启用');
                    break;
                case 'inactive':
                    devices = devices.filter(device => device.status === '停用');
                    break;
                case 'all':
                default:
                    // 不过滤，返回所有设备
                    break;
            }
        }

        if (filters.factory) {
            devices = devices.filter(device => device.factory === filters.factory);
        }

        if (filters.location) {
            devices = devices.filter(device =>
                device.location && device.location.toLowerCase().includes(filters.location.toLowerCase())
            );
        }

        if (filters.responsible) {
            devices = devices.filter(device =>
                device.responsible && device.responsible.toLowerCase().includes(filters.responsible.toLowerCase())
            );
        }

        if (filters.search) {
            const searchTerm = filters.search.toLowerCase();
            devices = devices.filter(device =>
                (device.deviceCode && device.deviceCode.toLowerCase().includes(searchTerm)) ||
                (device.deviceName && device.deviceName.toLowerCase().includes(searchTerm)) ||
                (device.factory && device.factory.toLowerCase().includes(searchTerm)) ||
                (device.location && device.location.toLowerCase().includes(searchTerm)) ||
                (device.responsible && device.responsible.toLowerCase().includes(searchTerm))
            );
        }

        return devices;
    }

    // 根据ID获取设备
    getDeviceById(id) {
        const devices = this.getDevices();
        return devices.find(device => device.id === id);
    }

    // 添加设备
    addDevice(deviceData) {
        try {
            console.log('开始添加设备，接收到的数据:', deviceData);
            const devices = this.getDevices();
            console.log('当前设备数量:', devices.length);

            // 验证必填字段
            if (!deviceData.deviceCode || !deviceData.deviceName || !deviceData.location || !deviceData.responsible || !deviceData.factory || !deviceData.entryDate) {
                console.log('验证失败：缺少必填字段');
                return { success: false, message: '设备编号、设备名称、厂区、设备位置、负责人和设备进厂日期为必填项' };
            }

            // 验证进厂日期格式和合理性
            const entryDate = new Date(deviceData.entryDate);
            if (isNaN(entryDate.getTime())) {
                console.log('验证失败：进厂日期格式无效');
                return { success: false, message: '设备进厂日期格式无效' };
            }

            // 验证进厂日期不能是未来日期
            const today = new Date();
            today.setHours(23, 59, 59, 999);
            if (entryDate > today) {
                console.log('验证失败：进厂日期不能是未来日期');
                return { success: false, message: '设备进厂日期不能是未来日期' };
            }

            // 检查设备编号是否已存在
            if (devices.some(device => device.deviceCode === deviceData.deviceCode)) {
                console.log('验证失败：设备编号已存在');
                return { success: false, message: '设备编号已存在' };
            }

            const newDevice = {
                id: Date.now().toString(),
                deviceCode: deviceData.deviceCode,
                deviceName: deviceData.deviceName,
                factory: deviceData.factory,
                location: deviceData.location,
                responsible: deviceData.responsible,
                entryDate: deviceData.entryDate,
                status: deviceData.status || '启用',
                createTime: new Date().toISOString(),
                updateTime: new Date().toISOString()
            };

            console.log('准备添加的新设备:', newDevice);

            console.log('开始保存设备数据到SQLite...');
            try {
                this.dataAccess.addDevice(newDevice);
                console.log('设备添加成功');
                return { success: true, message: '设备添加成功', device: newDevice };
            } catch (error) {
                console.log('保存设备数据失败:', error);
                return { success: false, message: '保存设备数据失败' };
            }
        } catch (error) {
            console.error('添加设备失败:', error);
            return { success: false, message: '添加设备失败: ' + error.message };
        }
    }

    // 更新设备
    updateDevice(id, deviceData) {
        try {
            const devices = this.getDevices();
            const deviceIndex = devices.findIndex(device => device.id === id);

            if (deviceIndex === -1) {
                return { success: false, message: '设备不存在' };
            }

            // 验证必填字段
            if (!deviceData.deviceCode || !deviceData.deviceName || !deviceData.location || !deviceData.responsible || !deviceData.factory || !deviceData.entryDate) {
                return { success: false, message: '设备编号、设备名称、厂区、设备位置、负责人和设备进厂日期为必填项' };
            }

            // 验证进厂日期格式和合理性
            const entryDate = new Date(deviceData.entryDate);
            if (isNaN(entryDate.getTime())) {
                return { success: false, message: '设备进厂日期格式无效' };
            }

            // 验证进厂日期不能是未来日期
            const today = new Date();
            today.setHours(23, 59, 59, 999);
            if (entryDate > today) {
                return { success: false, message: '设备进厂日期不能是未来日期' };
            }

            // 检查设备编号是否与其他设备冲突
            if (devices.some((device, index) =>
                index !== deviceIndex && device.deviceCode === deviceData.deviceCode)) {
                return { success: false, message: '设备编号已存在' };
            }

            // 更新设备信息
            const updatedDevice = {
                ...devices[deviceIndex],
                deviceCode: deviceData.deviceCode,
                deviceName: deviceData.deviceName,
                factory: deviceData.factory,
                location: deviceData.location,
                responsible: deviceData.responsible,
                entryDate: deviceData.entryDate,
                status: deviceData.status || devices[deviceIndex].status,
                updateTime: new Date().toISOString()
            };

            try {
                this.dataAccess.updateDevice(id, updatedDevice);
                return { success: true, message: '设备更新成功', device: updatedDevice };
            } catch (error) {
                return { success: false, message: '保存设备数据失败' };
            }
        } catch (error) {
            console.error('更新设备失败:', error);
            return { success: false, message: '更新设备失败' };
        }
    }

    // 删除设备
    deleteDevice(id) {
        try {
            const device = this.dataAccess.getDeviceById(id);

            if (!device) {
                return { success: false, message: '设备不存在' };
            }

            this.dataAccess.deleteDevice(id);
            return { success: true, message: '设备删除成功' };
        } catch (error) {
            console.error('删除设备失败:', error);
            return { success: false, message: '删除设备失败' };
        }
    }

    // 批量删除设备
    deleteDevices(ids) {
        try {
            let deletedCount = 0;
            for (const id of ids) {
                try {
                    this.dataAccess.deleteDevice(id);
                    deletedCount++;
                } catch (error) {
                    console.error(`删除设备 ${id} 失败:`, error);
                }
            }

            return { success: true, message: `成功删除 ${deletedCount} 个设备` };
        } catch (error) {
            console.error('批量删除设备失败:', error);
            return { success: false, message: '批量删除设备失败' };
        }
    }

    // 获取设备统计信息
    getDeviceStats() {
        const devices = this.getDevices();
        const total = devices.length;
        const active = devices.filter(device => device.status === '启用').length;
        const inactive = devices.filter(device => device.status === '停用').length;

        return {
            total,
            active,
            inactive
        };
    }

    // ==================== 厂区管理方法 ====================

    // 添加厂区
    addFactory(factoryData) {
        try {
            const factories = this.getFactories();

            // 验证必填字段
            if (!factoryData.id || !factoryData.name) {
                return { success: false, message: '厂区ID和厂区名称为必填项' };
            }

            // 检查厂区ID是否已存在
            if (factories.some(factory => factory.id === factoryData.id)) {
                return { success: false, message: '厂区ID已存在' };
            }

            // 检查厂区名称是否已存在
            if (factories.some(factory => factory.name === factoryData.name)) {
                return { success: false, message: '厂区名称已存在' };
            }

            const newFactory = {
                id: factoryData.id.trim(),
                name: factoryData.name.trim(),
                description: factoryData.description ? factoryData.description.trim() : '',
                createTime: new Date().toISOString(),
                updateTime: new Date().toISOString()
            };

            try {
                this.dataAccess.addFactory(newFactory);
                return { success: true, message: '厂区添加成功', factory: newFactory };
            } catch (error) {
                return { success: false, message: '保存厂区数据失败' };
            }
        } catch (error) {
            console.error('添加厂区失败:', error);
            return { success: false, message: '添加厂区失败: ' + error.message };
        }
    }

    // 更新厂区
    updateFactory(id, factoryData) {
        try {
            const factories = this.getFactories();
            const factoryIndex = factories.findIndex(factory => factory.id === id);

            if (factoryIndex === -1) {
                return { success: false, message: '厂区不存在' };
            }

            // 验证必填字段
            if (!factoryData.name) {
                return { success: false, message: '厂区名称为必填项' };
            }

            // 检查厂区名称是否与其他厂区冲突
            if (factories.some((factory, index) =>
                index !== factoryIndex && factory.name === factoryData.name)) {
                return { success: false, message: '厂区名称已存在' };
            }

            // 更新厂区信息（不允许修改ID）
            const updatedFactory = {
                ...factories[factoryIndex],
                name: factoryData.name.trim(),
                description: factoryData.description ? factoryData.description.trim() : '',
                updateTime: new Date().toISOString()
            };

            try {
                this.dataAccess.updateFactory(id, updatedFactory);
                return { success: true, message: '厂区更新成功', factory: updatedFactory };
            } catch (error) {
                return { success: false, message: '保存厂区数据失败' };
            }
        } catch (error) {
            console.error('更新厂区失败:', error);
            return { success: false, message: '更新厂区失败: ' + error.message };
        }
    }

    // 删除厂区
    deleteFactory(id) {
        try {
            const factories = this.getFactories();
            const factoryIndex = factories.findIndex(factory => factory.id === id);

            if (factoryIndex === -1) {
                return { success: false, message: '厂区不存在' };
            }

            // 检查是否有设备使用该厂区
            const devices = this.getDevices();
            const relatedDevices = devices.filter(device => device.factory === id);

            if (relatedDevices.length > 0) {
                return {
                    success: false,
                    message: `无法删除厂区，还有 ${relatedDevices.length} 个设备正在使用该厂区`
                };
            }

            try {
                this.dataAccess.deleteFactory(id);
                return { success: true, message: '厂区删除成功' };
            } catch (error) {
                return { success: false, message: '保存厂区数据失败' };
            }
        } catch (error) {
            console.error('删除厂区失败:', error);
            return { success: false, message: '删除厂区失败: ' + error.message };
        }
    }

    // 根据ID获取厂区
    getFactoryById(id) {
        const factories = this.getFactories();
        return factories.find(factory => factory.id === id);
    }

    // 从Excel文件导入设备数据
    async importDevicesFromExcel(filePath, options = {}) {
        const ExcelJS = require('exceljs');

        try {
            const {
                skipDuplicates = true,
                updateExisting = false,
                username = 'system'
            } = options;

            const workbook = new ExcelJS.Workbook();
            await workbook.xlsx.readFile(filePath);

            // 获取第一个工作表
            const worksheet = workbook.getWorksheet(1);
            if (!worksheet) {
                return { success: false, message: 'Excel文件中没有找到工作表' };
            }

            const devices = this.getDevices();
            const factories = this.getFactories();
            const factoryIds = factories.map(f => f.id);

            const importedDevices = [];
            const updatedDevices = [];
            const skippedDevices = [];
            const errors = [];
            const newDevicesToAdd = [];
            const devicesToUpdate = [];

            // 获取表头行（第一行）
            const headerRow = worksheet.getRow(1);
            const headers = [];
            headerRow.eachCell((cell, colNumber) => {
                // ExcelJS的colNumber从1开始，转换为0基索引
                headers[colNumber - 1] = cell.value ? cell.value.toString().trim() : '';
            });

            // 验证必要的列是否存在
            const requiredColumns = ['设备编号', '设备名称', '负责人', '进厂日期'];
            const columnMapping = {};

            for (const required of requiredColumns) {
                const colIndex = headers.findIndex(h => h === required);
                if (colIndex === -1) {
                    return {
                        success: false,
                        message: `Excel文件缺少必要的列: ${required}`
                    };
                }
                columnMapping[required] = colIndex;
            }

            // 查找可选列（厂区和设备位置至少要有一个）
            const optionalColumns = ['厂区', '设备位置', '状态', '创建时间', '更新时间'];
            for (const optional of optionalColumns) {
                const colIndex = headers.findIndex(h => h === optional);
                if (colIndex !== -1) {
                    columnMapping[optional] = colIndex;
                }
            }

            // 验证厂区和设备位置至少有一个
            if (columnMapping['厂区'] === undefined && columnMapping['设备位置'] === undefined) {
                return {
                    success: false,
                    message: 'Excel文件必须包含"厂区"列或"设备位置"列中的至少一个'
                };
            }

            // 添加调试信息
            console.log('Excel表头:', headers);
            console.log('列映射:', columnMapping);

            // 处理数据行（从第二行开始）
            let rowNumber = 1;
            worksheet.eachRow((row, rowIndex) => {
                if (rowIndex === 1) return; // 跳过表头行

                rowNumber++;

                try {
                    const deviceData = {};

                    // 提取必填字段
                    deviceData.deviceCode = this.getCellValue(row, columnMapping['设备编号']);
                    deviceData.deviceName = this.getCellValue(row, columnMapping['设备名称']);
                    deviceData.responsible = this.getCellValue(row, columnMapping['负责人']);
                    deviceData.entryDate = this.getCellValue(row, columnMapping['进厂日期']);

                    // 提取厂区和位置信息
                    deviceData.factory = this.getCellValue(row, columnMapping['厂区']);
                    deviceData.location = this.getCellValue(row, columnMapping['设备位置']);

                    // 提取可选字段
                    deviceData.status = this.getCellValue(row, columnMapping['状态']) || '启用';

                    // 验证必填字段
                    if (!deviceData.deviceCode || !deviceData.deviceName ||
                        !deviceData.responsible || !deviceData.entryDate) {
                        errors.push(`第${rowNumber}行: 缺少必填字段（设备编号、设备名称、负责人、进厂日期）`);
                        return;
                    }

                    // 验证进厂日期格式
                    console.log(`第${rowNumber}行: 原始进厂日期值:`, deviceData.entryDate, '类型:', typeof deviceData.entryDate);
                    const entryDate = this.parseDate(deviceData.entryDate);
                    console.log(`第${rowNumber}行: 解析后的进厂日期:`, entryDate);
                    if (!entryDate) {
                        errors.push(`第${rowNumber}行: 进厂日期格式无效 (原始值: "${deviceData.entryDate}", 类型: ${typeof deviceData.entryDate})`);
                        return;
                    }
                    deviceData.entryDate = entryDate;

                    // 验证进厂日期不能是未来日期
                    const today = new Date();
                    today.setHours(23, 59, 59, 999);
                    if (new Date(entryDate) > today) {
                        errors.push(`第${rowNumber}行: 进厂日期不能是未来日期`);
                        return;
                    }

                    // 验证状态值
                    if (!['启用', '停用'].includes(deviceData.status)) {
                        errors.push(`第${rowNumber}行: 状态值无效 (${deviceData.status})，应为"启用"或"停用"`);
                        return;
                    }

                    // 处理厂区信息
                    let finalFactory = null;

                    if (deviceData.factory) {
                        // 如果直接提供了厂区，验证是否有效
                        if (factoryIds.includes(deviceData.factory)) {
                            finalFactory = deviceData.factory;
                        } else {
                            errors.push(`第${rowNumber}行: 厂区"${deviceData.factory}"不存在，有效的厂区有: ${factoryIds.join(', ')}`);
                            return;
                        }
                    } else if (deviceData.location) {
                        // 如果没有直接提供厂区，尝试从位置中提取
                        finalFactory = this.extractFactoryFromLocation(deviceData.location, factoryIds);
                        if (!finalFactory) {
                            errors.push(`第${rowNumber}行: 无法从位置"${deviceData.location}"中识别厂区，请在厂区列中明确指定或确保位置包含有效的厂区标识`);
                            return;
                        }
                    } else {
                        errors.push(`第${rowNumber}行: 必须提供厂区信息或设备位置信息`);
                        return;
                    }

                    deviceData.factory = finalFactory;

                    // 检查设备编号是否已存在
                    const existingDeviceIndex = devices.findIndex(d => d.deviceCode === deviceData.deviceCode);

                    if (existingDeviceIndex !== -1) {
                        if (updateExisting) {
                            // 更新现有设备
                            const updatedDevice = {
                                ...devices[existingDeviceIndex],
                                deviceName: deviceData.deviceName,
                                factory: deviceData.factory,
                                location: deviceData.location,
                                responsible: deviceData.responsible,
                                entryDate: deviceData.entryDate,
                                status: deviceData.status,
                                updateTime: new Date().toISOString()
                            };
                            devicesToUpdate.push(updatedDevice);
                            updatedDevices.push(deviceData.deviceCode);
                        } else if (skipDuplicates) {
                            // 跳过重复设备
                            skippedDevices.push(deviceData.deviceCode);
                        } else {
                            errors.push(`第${rowNumber}行: 设备编号"${deviceData.deviceCode}"已存在`);
                        }
                        return;
                    }

                    // 添加新设备
                    const newDevice = {
                        id: Date.now().toString() + Math.random().toString(36).substr(2, 9),
                        deviceCode: deviceData.deviceCode,
                        deviceName: deviceData.deviceName,
                        factory: deviceData.factory,
                        location: deviceData.location,
                        responsible: deviceData.responsible,
                        entryDate: deviceData.entryDate,
                        status: deviceData.status,
                        createTime: new Date().toISOString(),
                        updateTime: new Date().toISOString()
                    };

                    newDevicesToAdd.push(newDevice);
                    importedDevices.push(deviceData.deviceCode);

                } catch (rowError) {
                    console.error(`处理第${rowNumber}行时出错:`, rowError);
                    errors.push(`第${rowNumber}行: 处理数据时出错 - ${rowError.message}`);
                }
            });

            // 保存设备数据
            if (newDevicesToAdd.length > 0 || devicesToUpdate.length > 0) {
                try {
                    // 使用事务批量保存设备数据
                    const transaction = this.dataAccess.transaction(() => {
                        // 添加新设备
                        for (const device of newDevicesToAdd) {
                            this.dataAccess.addDevice(device);
                            console.log(`添加新设备: ${device.deviceCode}`);
                        }

                        // 更新现有设备
                        for (const device of devicesToUpdate) {
                            this.dataAccess.updateDevice(device.id, device);
                            console.log(`更新设备: ${device.deviceCode}`);
                        }
                    });

                    transaction();
                    console.log(`批量导入完成: 新增${newDevicesToAdd.length}个，更新${devicesToUpdate.length}个设备`);
                } catch (error) {
                    console.error('保存设备数据失败:', error);
                    return { success: false, message: '保存设备数据失败: ' + error.message };
                }
            }

            return {
                success: true,
                message: '设备数据导入完成',
                imported: importedDevices.length,
                updated: updatedDevices.length,
                skipped: skippedDevices.length,
                errors: errors.length > 0 ? errors : undefined
            };

        } catch (error) {
            console.error('导入Excel文件失败:', error);
            return {
                success: false,
                message: '导入Excel文件失败: ' + error.message
            };
        }
    }

    // 获取单元格值的辅助方法
    getCellValue(row, columnIndex) {
        if (columnIndex === -1 || columnIndex === undefined) return '';
        const cell = row.getCell(columnIndex + 1); // ExcelJS使用1基索引，所以需要+1
        if (!cell || cell.value === null || cell.value === undefined) return '';

        // 处理日期类型
        if (cell.value instanceof Date) {
            // 确保日期有效
            if (!isNaN(cell.value.getTime())) {
                return cell.value.toISOString().split('T')[0];
            }
        }

        // 处理数字类型（Excel有时将日期存储为数字）
        if (typeof cell.value === 'number' && cell.numFmt && cell.numFmt.includes('d')) {
            // 这可能是Excel日期序列号
            try {
                const excelDate = new Date((cell.value - 25569) * 86400 * 1000);
                if (!isNaN(excelDate.getTime())) {
                    return excelDate.toISOString().split('T')[0];
                }
            } catch (e) {
                // 如果转换失败，继续使用原值
            }
        }

        return cell.value.toString().trim();
    }

    // 解析日期的辅助方法
    parseDate(dateValue) {
        if (!dateValue) return null;

        // 转换为字符串进行处理
        const dateStr = dateValue.toString().trim();

        // 如果已经是标准格式 YYYY-MM-DD
        if (/^\d{4}-\d{2}-\d{2}$/.test(dateStr)) {
            // 验证日期是否有效
            const testDate = new Date(dateStr + 'T00:00:00.000Z');
            if (!isNaN(testDate.getTime())) {
                return dateStr;
            }
        }

        // 处理其他常见格式
        const formats = [
            /^(\d{4})[-\/](\d{1,2})[-\/](\d{1,2})$/, // YYYY-MM-DD 或 YYYY/MM/DD
            /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{4})$/, // MM-DD-YYYY 或 MM/DD/YYYY
            /^(\d{1,2})[-\/](\d{1,2})[-\/](\d{2})$/   // MM-DD-YY 或 MM/DD/YY
        ];

        for (const format of formats) {
            const match = dateStr.match(format);
            if (match) {
                let year, month, day;

                if (format === formats[0]) { // YYYY-MM-DD
                    [, year, month, day] = match;
                } else if (format === formats[1]) { // MM-DD-YYYY
                    [, month, day, year] = match;
                } else if (format === formats[2]) { // MM-DD-YY
                    [, month, day, year] = match;
                    year = parseInt(year) < 50 ? `20${year}` : `19${year}`;
                }

                // 补零
                month = month.padStart(2, '0');
                day = day.padStart(2, '0');

                const formattedDate = `${year}-${month}-${day}`;

                // 验证日期是否有效
                const testDate = new Date(formattedDate + 'T00:00:00.000Z');
                if (!isNaN(testDate.getTime())) {
                    return formattedDate;
                }
            }
        }

        // 尝试直接解析为Date对象
        try {
            const date = new Date(dateValue);
            if (!isNaN(date.getTime())) {
                return date.toISOString().split('T')[0];
            }
        } catch (e) {
            // 解析失败，继续
        }

        // 如果是数字，可能是Excel日期序列号
        if (typeof dateValue === 'number' || !isNaN(Number(dateValue))) {
            try {
                const num = Number(dateValue);
                // Excel日期序列号转换（1900年1月1日为1）
                const excelDate = new Date((num - 25569) * 86400 * 1000);
                if (!isNaN(excelDate.getTime()) && excelDate.getFullYear() > 1900 && excelDate.getFullYear() < 2100) {
                    return excelDate.toISOString().split('T')[0];
                }
            } catch (e) {
                // 转换失败，继续
            }
        }

        return null;
    }

    // 从位置信息中提取厂区的辅助方法
    extractFactoryFromLocation(location, factoryIds) {
        if (!location) return null;

        // 查找位置中包含的厂区ID
        for (const factoryId of factoryIds) {
            if (location.includes(factoryId)) {
                return factoryId;
            }
        }

        return null;
    }


}

module.exports = DeviceManagement;
