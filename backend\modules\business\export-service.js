const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');

// 导出服务模块
class ExportService {
    constructor() {
        this.tempDir = path.join(__dirname, '..', '..', 'temp');
        this.initializeTempDir();
    }

    // 初始化临时目录
    initializeTempDir() {
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }

    // 导出设备列表到Excel
    async exportDevicesToExcel(devices, filename = null) {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('设备列表');

            // 设置列标题
            const headers = [
                { header: '设备编号', key: 'deviceCode', width: 15 },
                { header: '设备名称', key: 'deviceName', width: 20 },
                { header: '设备位置', key: 'location', width: 15 },
                { header: '负责人', key: 'responsible', width: 12 },
                { header: '进厂日期', key: 'entryDate', width: 15 },
                { header: '状态', key: 'status', width: 10 },
                { header: '创建时间', key: 'createTime', width: 20 },
                { header: '更新时间', key: 'updateTime', width: 20 }
            ];

            worksheet.columns = headers;

            // 设置标题行样式
            worksheet.getRow(1).font = { bold: true };
            worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE6F3FF' }
            };

            // 添加数据行
            devices.forEach(device => {
                worksheet.addRow({
                    deviceCode: device.deviceCode,
                    deviceName: device.deviceName,
                    location: device.location,
                    responsible: device.responsible,
                    entryDate: device.entryDate || (device.createTime ? new Date(device.createTime).toISOString().split('T')[0] : ''),
                    status: device.status,
                    createTime: this.formatDateTime(device.createTime),
                    updateTime: this.formatDateTime(device.updateTime)
                });
            });

            // 设置边框
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            });

            // 生成文件名
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const exportFilename = filename || `设备列表_${timestamp}.xlsx`;
            const filePath = path.join(this.tempDir, exportFilename);

            // 保存文件
            await workbook.xlsx.writeFile(filePath);

            return {
                success: true,
                filename: exportFilename,
                filePath: filePath,
                message: '设备列表导出成功'
            };
        } catch (error) {
            console.error('导出设备列表失败:', error);
            return {
                success: false,
                message: '导出设备列表失败: ' + error.message
            };
        }
    }

    // 导出设备统计报告到Excel
    async exportDeviceStatsToExcel(stats, devices = [], records = [], filename = null) {
        try {
            const workbook = new ExcelJS.Workbook();
            
            // 创建统计概览工作表
            const overviewSheet = workbook.addWorksheet('统计概览');
            
            // 设备统计
            overviewSheet.addRow(['设备统计']);
            overviewSheet.addRow(['总设备数', stats.device.total]);
            overviewSheet.addRow(['启用设备', stats.device.active]);
            overviewSheet.addRow(['停用设备', stats.device.inactive]);
            overviewSheet.addRow([]);
            
            // 记录统计
            overviewSheet.addRow(['维修/保养统计']);
            overviewSheet.addRow(['总记录数', stats.record.total]);
            overviewSheet.addRow(['维修记录', stats.record.maintenance]);
            overviewSheet.addRow(['保养记录', stats.record.upkeep]);
            overviewSheet.addRow(['临时保养记录', stats.record.temporaryUpkeep || 0]);
            
            // 设置样式
            overviewSheet.getColumn(1).width = 20;
            overviewSheet.getColumn(2).width = 15;
            
            // 创建设备详情工作表
            const deviceSheet = workbook.addWorksheet('设备详情');
            const deviceHeaders = [
                { header: '设备编号', key: 'deviceCode', width: 15 },
                { header: '设备名称', key: 'deviceName', width: 20 },
                { header: '设备位置', key: 'location', width: 15 },
                { header: '负责人', key: 'responsible', width: 12 },
                { header: '进厂日期', key: 'entryDate', width: 15 },
                { header: '状态', key: 'status', width: 10 },
                { header: '维修次数', key: 'maintenanceCount', width: 12 },
                { header: '保养次数', key: 'upkeepCount', width: 12 }
            ];
            
            deviceSheet.columns = deviceHeaders;
            deviceSheet.getRow(1).font = { bold: true };
            
            // 统计每个设备的维修保养次数
            const deviceRecordCount = {};
            records.forEach(record => {
                if (!deviceRecordCount[record.deviceId]) {
                    deviceRecordCount[record.deviceId] = { maintenance: 0, upkeep: 0, temporaryUpkeep: 0 };
                }
                if (record.type === '维修') {
                    deviceRecordCount[record.deviceId].maintenance++;
                } else if (record.type === '保养') {
                    deviceRecordCount[record.deviceId].upkeep++;
                } else if (record.type === '临时保养') {
                    deviceRecordCount[record.deviceId].temporaryUpkeep++;
                }
            });
            
            // 添加设备数据
            devices.forEach(device => {
                const counts = deviceRecordCount[device.id] || { maintenance: 0, upkeep: 0, temporaryUpkeep: 0 };
                deviceSheet.addRow({
                    deviceCode: device.deviceCode,
                    deviceName: device.deviceName,
                    location: device.location,
                    responsible: device.responsible,
                    entryDate: device.entryDate || (device.createTime ? new Date(device.createTime).toISOString().split('T')[0] : ''),
                    status: device.status,
                    maintenanceCount: counts.maintenance,
                    upkeepCount: counts.upkeep
                });
            });

            // 生成文件名
            const timestamp = new Date().toISOString().slice(0, 19).replace(/[:-]/g, '');
            const exportFilename = filename || `设备统计报告_${timestamp}.xlsx`;
            const filePath = path.join(this.tempDir, exportFilename);

            // 保存文件
            await workbook.xlsx.writeFile(filePath);

            return {
                success: true,
                filename: exportFilename,
                filePath: filePath,
                message: '设备统计报告导出成功'
            };
        } catch (error) {
            console.error('导出设备统计报告失败:', error);
            return {
                success: false,
                message: '导出设备统计报告失败: ' + error.message
            };
        }
    }

    // 格式化日期时间
    formatDateTime(dateTimeString) {
        if (!dateTimeString) return '';
        try {
            const date = new Date(dateTimeString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });
        } catch (error) {
            return dateTimeString;
        }
    }

    // 格式化日期时间（不显示秒）- 用于维修保养记录导出
    formatDateTimeNoSeconds(dateTimeString) {
        if (!dateTimeString) return '';
        try {
            const date = new Date(dateTimeString);
            return date.toLocaleString('zh-CN', {
                year: 'numeric',
                month: '2-digit',
                day: '2-digit',
                hour: '2-digit',
                minute: '2-digit'
            });
        } catch (error) {
            return dateTimeString;
        }
    }

    // 计算文本在指定列宽下需要的行数
    calculateTextLines(text, columnWidthCm) {
        if (!text || text.toString().trim() === '') return 1;

        const textStr = text.toString();
        const MEASURED_CHARS_PER_CM = 5.17; // 基于实际测量的字符/厘米转换系数
        const charsPerLine = Math.floor(columnWidthCm * MEASURED_CHARS_PER_CM);

        // 按换行符分割文本
        const paragraphs = textStr.split(/\r?\n/);
        let totalLines = 0;

        paragraphs.forEach(paragraph => {
            if (paragraph.trim() === '') {
                totalLines += 1; // 空行也占一行
            } else {
                // 计算每个段落需要的行数
                let charCount = 0;
                for (let i = 0; i < paragraph.length; i++) {
                    const char = paragraph[i];
                    // 中文字符占2个字符宽度，英文字符占1个字符宽度
                    const charWidth = /[\u4e00-\u9fff]/.test(char) ? 2 : 1;
                    charCount += charWidth;
                }
                const linesNeeded = Math.ceil(charCount / charsPerLine);
                totalLines += Math.max(1, linesNeeded); // 至少1行
            }
        });

        return Math.max(1, totalLines); // 至少返回1行
    }

    // 导出设备列表到Excel Buffer（直接下载）
    async exportDevicesToExcelBuffer(devices, filename = null) {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('设备列表');

            // 设置列标题
            const headers = [
                { header: '设备编号', key: 'deviceCode', width: 15 },
                { header: '设备名称', key: 'deviceName', width: 20 },
                { header: '厂区', key: 'factory', width: 12 },
                { header: '设备位置', key: 'location', width: 15 },
                { header: '负责人', key: 'responsible', width: 12 },
                { header: '进厂日期', key: 'entryDate', width: 15 },
                { header: '状态', key: 'status', width: 10 },
                { header: '创建时间', key: 'createTime', width: 20 },
                { header: '更新时间', key: 'updateTime', width: 20 }
            ];

            worksheet.columns = headers;

            // 设置标题行样式
            worksheet.getRow(1).font = { bold: true };
            worksheet.getRow(1).fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE6F3FF' }
            };

            // 添加数据行
            devices.forEach(device => {
                worksheet.addRow({
                    deviceCode: device.deviceCode,
                    deviceName: device.deviceName,
                    factory: device.factory || '',
                    location: device.location,
                    responsible: device.responsible,
                    entryDate: device.entryDate || (device.createTime ? new Date(device.createTime).toISOString().split('T')[0] : ''),
                    status: device.status,
                    createTime: this.formatDateTime(device.createTime),
                    updateTime: this.formatDateTime(device.updateTime)
                });
            });

            // 设置边框
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            });

            // 生成Buffer
            const buffer = await workbook.xlsx.writeBuffer();

            return {
                success: true,
                buffer: buffer,
                message: '设备列表导出成功'
            };
        } catch (error) {
            console.error('导出设备列表失败:', error);
            return {
                success: false,
                message: '导出设备列表失败: ' + error.message
            };
        }
    }

    // 导出维修保养记录到Excel Buffer（直接下载）
    async exportMaintenanceToExcelBuffer(records, devices = [], filename = null) {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet('维修保养记录');

            // 设置页面为A4横印
            worksheet.pageSetup = {
                paperSize: 9, // A4
                orientation: 'landscape', // 横印
                fitToPage: true,
                fitToWidth: 1,
                fitToHeight: 0,
                margins: {
                    left: 0.7,
                    right: 0.7,
                    top: 0.75,
                    bottom: 0.75,
                    header: 0.3,
                    footer: 0.3
                }
            };

            // 创建设备映射表
            const deviceMap = {};
            devices.forEach(device => {
                deviceMap[device.id] = device;
            });

            // 基于实际测量的精确转换系数
            // 实测：设置13磅 → 实际0.3cm，所以 13/0.3 = 43.33 (磅/cm)
            // 目标0.79cm → 0.79 * 43.33 = 34.2磅
            // 目标0.46cm → 0.46 * 43.33 = 19.9磅
            const MEASURED_POINTS_PER_CM = 43.33;

            // 在第1行右上方添加版本号"SO4-09406 R:1.0"
            const versionCell = worksheet.getCell('G1');
            versionCell.value = 'SO4-09406 R:1.0';
            versionCell.font = {
                bold: false,
                size: 10,
                name: '宋体'
            };
            versionCell.alignment = {
                horizontal: 'right',
                vertical: 'top'
            };
            // 设置第1行高度
            worksheet.getRow(1).height = Math.round(0.46 * MEASURED_POINTS_PER_CM * 10) / 10;

            // 在第2行添加主标题"维修及保养记录"
            worksheet.mergeCells('A2:G2');
            const titleCell = worksheet.getCell('A2');
            titleCell.value = '维修及保养记录';
            titleCell.font = {
                bold: true,
                size: 18,
                name: '宋体'
            };
            titleCell.alignment = {
                horizontal: 'center',
                vertical: 'middle'
            };
            worksheet.getRow(2).height = Math.round(0.79 * MEASURED_POINTS_PER_CM * 10) / 10;

            // 基于实际测量的精确列宽转换系数
            // 实测：设置10.4字符 → 实际2.01cm，所以 10.4/2.01 = 5.17 (字符/cm)
            const MEASURED_CHARS_PER_CM = 5.17;
            const headers = [
                { header: '开始时间', key: 'startTime', width: Math.round(3.5 * MEASURED_CHARS_PER_CM * 10) / 10 },        // 3.5cm - 增加宽度以完整显示日期时间
                { header: '结束时间', key: 'endTime', width: Math.round(3.5 * MEASURED_CHARS_PER_CM * 10) / 10 },          // 3.5cm - 增加宽度以完整显示日期时间
                { header: '机台名称', key: 'deviceName', width: Math.round(6.5 * MEASURED_CHARS_PER_CM * 10) / 10 },       // 6.5cm - 稍微减少以平衡总宽度
                { header: '保养/维修', key: 'type', width: Math.round(1.73 * MEASURED_CHARS_PER_CM * 10) / 10 },           // 1.73cm
                { header: '维修/保养记录', key: 'description', width: Math.round(6.5 * MEASURED_CHARS_PER_CM * 10) / 10 },   // 6.5cm - 稍微减少以平衡总宽度
                { header: '机修人员', key: 'operator', width: Math.round(1.59 * MEASURED_CHARS_PER_CM * 10) / 10 },        // 1.59cm
                { header: '审查', key: 'reviewer', width: Math.round(0.92 * MEASURED_CHARS_PER_CM * 10) / 10 }             // 0.92cm
            ];

            // 强制设置列宽
            headers.forEach((header, index) => {
                const column = worksheet.getColumn(index + 1);
                column.width = header.width;
            });

            // 在第3行添加列标题
            const headerRow = worksheet.getRow(3);
            headers.forEach((header, index) => {
                const cell = headerRow.getCell(index + 1);
                cell.value = header.header;
                cell.font = { bold: true, name: '宋体', size: 10 }; // 设置为10号字体
                cell.alignment = {
                    horizontal: 'center',
                    vertical: 'middle',
                    wrapText: false // 列标题不自动换行
                };
                cell.fill = {
                    type: 'pattern',
                    pattern: 'solid',
                    fgColor: { argb: 'FFE6F3FF' }
                };
            });
            // 设置列标题行高为0.46cm (基于实测转换系数)
            headerRow.height = Math.round(0.46 * MEASURED_POINTS_PER_CM * 10) / 10;

            // 添加数据行（从第4行开始）
            let currentRow = 4;
            records.forEach(record => {
                const device = deviceMap[record.deviceId] || {};
                // 格式化机台名称为"设备名称+(设备编号)"
                const deviceDisplayName = device.deviceName && device.deviceCode
                    ? `${device.deviceName}(${device.deviceCode})`
                    : device.deviceName || '未知设备';

                const row = worksheet.getRow(currentRow);

                // 设置单元格值（时间只显示时和分，不显示秒）
                row.getCell(1).value = record.startTime ? this.formatDateTimeNoSeconds(record.startTime) : '-';
                row.getCell(2).value = record.endTime ? this.formatDateTimeNoSeconds(record.endTime) : '-';
                row.getCell(3).value = deviceDisplayName;
                row.getCell(4).value = record.type;
                row.getCell(5).value = record.description;
                row.getCell(6).value = record.operator;
                row.getCell(7).value = record.reviewer || '-';

                // 设置每个单元格的样式
                for (let col = 1; col <= 7; col++) {
                    const cell = row.getCell(col);
                    cell.font = { name: '宋体', size: 10 }; // 设置为10号字体
                    cell.alignment = {
                        horizontal: col === 3 || col === 5 ? 'left' : 'center', // 机台名称和维修记录左对齐，其他居中
                        vertical: 'middle',
                        wrapText: col === 3 || col === 5 // 只有机台名称(第3列)和维修/保养记录(第5列)自动换行
                    };
                }

                // 动态计算行高以适应换行内容
                const deviceNameLines = this.calculateTextLines(deviceDisplayName, 6.5); // 机台名称列宽6.5cm
                const descriptionLines = this.calculateTextLines(record.description || '', 6.5); // 维修/保养记录列宽6.5cm
                const maxLines = Math.max(1, deviceNameLines, descriptionLines); // 至少1行

                // 基础行高0.46cm，每增加一行增加0.46cm
                const calculatedHeight = Math.round(0.46 * maxLines * MEASURED_POINTS_PER_CM * 10) / 10;
                row.height = calculatedHeight;
                currentRow++;
            });

            // 设置边框（从第3行开始，跳过版本号行和标题行）
            for (let rowNum = 3; rowNum <= currentRow - 1; rowNum++) {
                const row = worksheet.getRow(rowNum);
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            }

            // 生成Buffer
            const buffer = await workbook.xlsx.writeBuffer();

            return {
                success: true,
                buffer: buffer,
                message: '维修保养记录导出成功'
            };
        } catch (error) {
            console.error('导出维修保养记录失败:', error);
            return {
                success: false,
                message: '导出维修保养记录失败: ' + error.message
            };
        }
    }



    // 导出设备统计报告到Excel Buffer（直接下载）
    async exportDeviceStatsToExcelBuffer(stats, devices = [], records = [], filename = null) {
        try {
            const workbook = new ExcelJS.Workbook();

            // 创建统计概览工作表
            const overviewSheet = workbook.addWorksheet('统计概览');

            // 设备统计
            overviewSheet.addRow(['设备统计']);
            overviewSheet.addRow(['总设备数', stats.device.total]);
            overviewSheet.addRow(['启用设备', stats.device.active]);
            overviewSheet.addRow(['停用设备', stats.device.inactive]);
            overviewSheet.addRow([]);

            // 记录统计
            overviewSheet.addRow(['维修/保养统计']);
            overviewSheet.addRow(['总记录数', stats.record.total]);
            overviewSheet.addRow(['维修记录', stats.record.maintenance]);
            overviewSheet.addRow(['保养记录', stats.record.upkeep]);
            overviewSheet.addRow(['临时保养记录', stats.record.temporaryUpkeep || 0]);

            // 设置样式
            overviewSheet.getColumn(1).width = 20;
            overviewSheet.getColumn(2).width = 15;

            // 创建设备详情工作表
            const deviceSheet = workbook.addWorksheet('设备详情');
            const deviceHeaders = [
                { header: '设备编号', key: 'deviceCode', width: 15 },
                { header: '设备名称', key: 'deviceName', width: 20 },
                { header: '厂区', key: 'factory', width: 12 },
                { header: '设备位置', key: 'location', width: 15 },
                { header: '负责人', key: 'responsible', width: 12 },
                { header: '进厂日期', key: 'entryDate', width: 15 },
                { header: '状态', key: 'status', width: 10 },
                { header: '维修次数', key: 'maintenanceCount', width: 12 },
                { header: '保养次数', key: 'upkeepCount', width: 12 }
            ];

            deviceSheet.columns = deviceHeaders;
            deviceSheet.getRow(1).font = { bold: true };

            // 统计每个设备的维修保养次数
            const deviceRecordCount = {};
            records.forEach(record => {
                if (!deviceRecordCount[record.deviceId]) {
                    deviceRecordCount[record.deviceId] = { maintenance: 0, upkeep: 0, temporaryUpkeep: 0 };
                }
                if (record.type === '维修') {
                    deviceRecordCount[record.deviceId].maintenance++;
                } else if (record.type === '保养') {
                    deviceRecordCount[record.deviceId].upkeep++;
                } else if (record.type === '临时保养') {
                    deviceRecordCount[record.deviceId].temporaryUpkeep++;
                }
            });

            // 添加设备数据
            devices.forEach(device => {
                const counts = deviceRecordCount[device.id] || { maintenance: 0, upkeep: 0, temporaryUpkeep: 0 };
                deviceSheet.addRow({
                    deviceCode: device.deviceCode,
                    deviceName: device.deviceName,
                    factory: device.factory || '',
                    location: device.location,
                    responsible: device.responsible,
                    entryDate: device.entryDate || (device.createTime ? new Date(device.createTime).toISOString().split('T')[0] : ''),
                    status: device.status,
                    maintenanceCount: counts.maintenance,
                    upkeepCount: counts.upkeep
                });
            });

            // 生成Buffer
            const buffer = await workbook.xlsx.writeBuffer();

            return {
                success: true,
                buffer: buffer,
                message: '设备统计报告导出成功'
            };
        } catch (error) {
            console.error('导出设备统计报告失败:', error);
            return {
                success: false,
                message: '导出设备统计报告失败: ' + error.message
            };
        }
    }

    // 清理临时文件
    cleanupTempFiles(maxAge = 24 * 60 * 60 * 1000) { // 默认24小时
        try {
            const files = fs.readdirSync(this.tempDir);
            const now = Date.now();

            files.forEach(file => {
                const filePath = path.join(this.tempDir, file);
                const stats = fs.statSync(filePath);

                if (now - stats.mtime.getTime() > maxAge) {
                    fs.unlinkSync(filePath);
                    console.log(`清理临时文件: ${file}`);
                }
            });
        } catch (error) {
            console.error('清理临时文件失败:', error);
        }
    }
}

module.exports = ExportService;
