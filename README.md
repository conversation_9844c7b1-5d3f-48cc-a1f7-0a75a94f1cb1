# 管理系统

<div align="center">

![License](https://img.shields.io/badge/license-MIT-blue.svg)
![Node.js](https://img.shields.io/badge/node.js-v18+-green.svg)
![Platform](https://img.shields.io/badge/platform-Windows%20%7C%20Linux%20%7C%20macOS-lightgrey.svg)

**现代化企业管理平台 - 集成申请审批、设备管理、用户管理等核心业务模块**

[功能特性](#-功能特性) • [快速开始](#-快速开始) • [部署指南](#-部署指南) • [API文档](#-api文档) • [贡献指南](#-贡献指南)

</div>

本系统是一个基于Node.js和Express的现代化企业管理平台，集成申请审批、设备管理、用户管理等核心业务模块。采用响应式设计，支持PC端和移动端访问，为企业提供一站式数字化管理解决方案。

### ✨ 核心特性

- 🌍 **全球部署兼容** - 所有前端资源完全本地化，摆脱CDN依赖
- 📱 **响应式设计** - 完美适配PC、平板、手机等各种设备
- 🔒 **安全可靠** - 多层安全防护，数据加密存储
- ⚡ **高性能** - 优化的数据库查询和缓存机制
- 🛠️ **易于维护** - 模块化架构，清晰的代码组织
- 📊 **数据可视化** - 丰富的图表和报表功能

## 🚀 功能特性

### 📝 申请审批系统

- 多级审批流程配置
- 实时邮件通知
- 审批历史追踪
- 附件管理
- 批量操作

### 🔧 设备管理

- 设备信息管理
- 维修保养记录
- 设备健康监控
- 统计分析
- 导出功能

### 👥 用户管理

- 用户权限控制
- 部门组织架构
- 角色管理
- 操作日志

### 📊 数据分析

- 实时数据统计
- 可视化图表
- 报表生成
- 数据导出

**详细功能说明请参考：[FUNC.md](FUNC.md)**

## 🚀 快速开始

### 📋 系统要求

- **Node.js** >= 18.0.0
- **npm** >= 8.0.0
- **操作系统**: Windows 10+, macOS 10.15+, Ubuntu 18.04+

### ⚡ 安装步骤

1. **克隆项目**
   
   ```bash
   git clone https://github.com/Darrowyu/management-system.git
   cd management-system
   ```

2. **安装依赖**
   
   ```bash
   npm install
   ```

3. **配置环境**
   
   ```bash
   # 复制配置文件模板
   cp .env.example .env
   
   # 编辑配置文件
   nano .env
   ```

4. **启动服务**
   
   ```bash
   # 开发模式
   npm run dev
   
   # 生产模式
   npm start
   ```

5. **访问系统**
   
   打开浏览器访问：http://localhost:3000

## ⚙️ 配置说明

### 📧 邮件服务配置

在 `backend/server.js` 中配置SMTP服务：

```javascript
const transporter = nodemailer.createTransport({
    host: 'smtp.gmail.com',
    port: 587,
    secure: false,
    auth: {
        user: '<EMAIL>',
        pass: 'your-app-password'
    }
});
```

### 🌐 服务器配置

```javascript
const SERVER_CONFIG = {
    url: 'http://your-domain.com:3000',
    emailRetryDelay: 3000
};
```

### 📁 目录结构

```
management-system/
├── backend/                 # 后端代码
│   ├── data/               # 数据存储
│   ├── modules/            # 功能模块
│   └── server.js           # 主服务文件
├── frontend/               # 前端代码
│   ├── css/               # 样式文件
│   ├── js/                # JavaScript文件
│   └── index.html         # 主页面
├── docs/                  # 文档
└── package.json           # 项目配置
```

## 🛠️ 部署指南

### 🗄️ 数据库

系统使用SQLite数据库，提供高性能和数据完整性：

**数据库文件**：

- `backend/data/application_system.db` - 主数据库
- `backend/data/application_system.db-shm` - 共享内存
- `backend/data/application_system.db-wal` - 预写日志

**数据表结构**：
| 表名 | 说明 |
|------|------|
| `applications` | 申请数据 |
| `users` | 用户信息 |
| `devices` | 设备管理 |
| `factories` | 厂区信息 |
| `departments` | 部门架构 |
| `maintenance_records` | 维修记录 |

### 💾 备份策略

```bash
# 数据库备份
cp backend/data/application_system.db backup/db_$(date +%Y%m%d).db

# 附件备份
tar -czf backup/files_$(date +%Y%m%d).tar.gz uploads/ archive/

# 自动备份脚本
0 2 * * * /path/to/backup.sh
```

### ⚡ 性能优化

- **数据缓存** - 内存缓存热点数据
- **静态资源** - CDN本地化，减少外部依赖
- **HTTP压缩** - Gzip压缩响应内容
- **数据库索引** - 优化查询性能

### 🔒 安全配置

- **依赖更新** - 定期更新npm包
- **访问控制** - 配置防火墙规则
- **日志监控** - 实时监控异常访问
- **数据加密** - 敏感数据加密存储

## 📚 API文档

### 🔗 主要接口

| 接口                  | 方法   | 说明     |
| ------------------- | ---- | ------ |
| `/api/applications` | GET  | 获取申请列表 |
| `/api/applications` | POST | 创建新申请  |
| `/api/devices`      | GET  | 获取设备列表 |
| `/api/users`        | GET  | 获取用户列表 |
| `/api/maintenance`  | GET  | 获取维修记录 |

### 📝 请求示例

```javascript
// 创建申请
fetch('/api/applications', {
    method: 'POST',
    headers: {
        'Content-Type': 'application/json'
    },
    body: JSON.stringify({
        title: '申请标题',
        content: '申请内容',
        applicant: '申请人'
    })
});
```

## 🤝 贡献指南

### 📋 开发规范

请参考 [CODING_RULES.md](CODING_RULES.md) 了解详细的开发规范和代码组织原则。

### 🔄 提交流程

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 创建 Pull Request

### 🐛 问题报告

发现问题请通过 [Issues](https://github.com/your-username/management-system/issues) 报告。

## ❓ 常见问题

<details>
<summary><strong>邮件通知收不到怎么办？</strong></summary>

检查以下配置：

- SMTP服务器设置是否正确
- 邮箱密码是否为应用专用密码
- 检查垃圾邮件文件夹
- 查看系统日志错误信息
  
  </details>

<details>
<summary><strong>系统支持移动端吗？</strong></summary>

是的，系统采用响应式设计，完美支持手机、平板等移动设备。

</details>

<details>
<summary><strong>如何进行数据备份？</strong></summary>

```bash
# 备份数据库
cp backend/data/application_system.db backup/

# 备份附件
tar -czf backup/files.tar.gz uploads/ archive/
```

</details>

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情。

## 🙏 致谢

感谢所有为这个项目做出贡献的开发者，特别鸣谢 [OG0914]Lucas_huang

---

<div align="center">

**[⬆ 回到顶部](#管理系统)**

Made with ❤️ by [Darrowyu]

</div>
