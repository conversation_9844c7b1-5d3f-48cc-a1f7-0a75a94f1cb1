# Excel导出功能完整实现指南

## 📋 概述

本文档提供了完整的Excel导出功能实现代码，包括前端调用、后端处理、Excel生成等所有相关组件。基于ExcelJS库实现，支持多种数据类型的导出。

## 🛠️ 技术栈

- **后端**: Node.js + Express + ExcelJS
- **前端**: JavaScript (原生) + Fetch API
- **文件格式**: .xlsx (Excel 2007+)

## 📦 依赖安装

```bash
# 安装ExcelJS库
npm install exceljs

# 其他相关依赖
npm install express
npm install path
npm install fs
```

## 🏗️ 后端实现

### 1. 导入必要的库

```javascript
const ExcelJS = require('exceljs');
const path = require('path');
const fs = require('fs');
```

### 2. 基础导出服务类

```javascript
// backend/modules/business/export-service.js
class ExportService {
    constructor() {
        this.tempDir = path.join(__dirname, '../../temp');
        // 确保临时目录存在
        if (!fs.existsSync(this.tempDir)) {
            fs.mkdirSync(this.tempDir, { recursive: true });
        }
    }

    // 导出数据到Excel Buffer（直接下载）
    async exportToExcelBuffer(data, headers, sheetName = '数据', filename = null) {
        try {
            const workbook = new ExcelJS.Workbook();
            const worksheet = workbook.addWorksheet(sheetName);

            // 设置列标题
            worksheet.columns = headers;

            // 添加数据行
            data.forEach(item => {
                worksheet.addRow(item);
            });

            // 设置表头样式
            const headerRow = worksheet.getRow(1);
            headerRow.font = { bold: true };
            headerRow.fill = {
                type: 'pattern',
                pattern: 'solid',
                fgColor: { argb: 'FFE0E0E0' }
            };

            // 设置边框
            worksheet.eachRow((row, rowNumber) => {
                row.eachCell((cell) => {
                    cell.border = {
                        top: { style: 'thin' },
                        left: { style: 'thin' },
                        bottom: { style: 'thin' },
                        right: { style: 'thin' }
                    };
                });
            });

            // 自动调整列宽
            worksheet.columns.forEach(column => {
                if (!column.width) {
                    column.width = 15;
                }
            });

            // 生成Buffer
            const buffer = await workbook.xlsx.writeBuffer();

            return {
                success: true,
                buffer: buffer,
                message: '导出成功'
            };
        } catch (error) {
            console.error('导出Excel失败:', error);
            return {
                success: false,
                message: '导出失败: ' + error.message
            };
        }
    }
}

module.exports = ExportService;
```

### 3. 申请记录导出实现

```javascript
// 在server.js中添加申请记录导出路由
const ExportService = require('./modules/business/export-service');
const exportService = new ExportService();

// 导出申请记录为Excel格式（新版本 - 使用POST）
app.post('/api/export/applications', async (req, res) => {
    try {
        const { filters, filename } = req.body;
        const { username, role, timeRange, startDate, endDate } = filters || {};
        
        const applications = getApplications();

        // 检查用户是否存在
        const users = getUsers();
        const user = users.find(u => u.username === username);
        if (!user) {
            return res.status(403).json({ success: false, message: '用户不存在' });
        }

        // 根据用户角色筛选可见的申请
        let filteredApps = [];
        if (role === 'admin' || role === 'readonly') {
            filteredApps = applications;
        } else {
            filteredApps = applications.filter(app => app.username === username);
        }

        // 时间范围筛选
        if (timeRange && timeRange !== 'all') {
            const now = new Date();
            let startTime;

            switch(timeRange) {
                case 'week':
                    startTime = new Date(now.getFullYear(), now.getMonth(), now.getDate() - now.getDay());
                    break;
                case 'month':
                    startTime = new Date(now.getFullYear(), now.getMonth(), 1);
                    break;
                case 'year':
                    startTime = new Date(now.getFullYear(), 0, 1);
                    break;
            }

            if (startTime) {
                filteredApps = filteredApps.filter(app => new Date(app.date) >= startTime);
            }
        }

        // 自定义日期范围筛选
        if (startDate && endDate) {
            const start = new Date(startDate);
            const end = new Date(endDate);
            end.setHours(23, 59, 59, 999);
            
            filteredApps = filteredApps.filter(app => {
                const appDate = new Date(app.date);
                return appDate >= start && appDate <= end;
            });
        }

        // 按日期倒序排序
        filteredApps.sort((a, b) => new Date(b.date) - new Date(a.date));

        // 定义Excel列结构
        const headers = [
            { header: '申请编号', key: 'applicationCode', width: 15 },
            { header: '申请人', key: 'applicant', width: 12 },
            { header: '部门', key: 'department', width: 15 },
            { header: '申请日期', key: 'date', width: 15 },
            { header: '紧急程度', key: 'priority', width: 10 },
            { header: '申请内容', key: 'content', width: 40 },
            { header: '申请金额', key: 'amount', width: 12 },
            { header: '币种', key: 'currency', width: 8 },
            { header: '状态', key: 'status', width: 12 }
        ];

        // 转换数据格式
        const exportData = filteredApps.map(app => ({
            applicationCode: app.applicationCode || '',
            applicant: app.applicant || '',
            department: app.department || '',
            date: app.date || '',
            priority: app.priority || '',
            content: app.content || '',
            amount: app.amount || '',
            currency: app.currency || '',
            status: app.status || ''
        }));

        // 生成Excel
        const result = await exportService.exportToExcelBuffer(
            exportData, 
            headers, 
            '申请记录', 
            filename
        );

        if (result.success) {
            // 设置文件名
            let exportFilename = filename;
            if (!exportFilename) {
                if (startDate && endDate) {
                    exportFilename = `申请记录_${startDate}_至_${endDate}.xlsx`;
                } else {
                    const timeRangeText = {
                        'week': '本周',
                        'month': '本月', 
                        'year': '本年'
                    }[timeRange] || '全部';
                    const today = new Date().toISOString().slice(0, 10);
                    exportFilename = `申请记录_${timeRangeText}_${today}.xlsx`;
                }
            }

            // 设置响应头，直接下载Excel文件
            res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');
            res.setHeader('Content-Disposition', `attachment; filename="${encodeURIComponent(exportFilename)}"`);
            res.send(result.buffer);
        } else {
            res.status(500).json(result);
        }
    } catch (error) {
        console.error('导出申请记录失败:', error);
        res.status(500).json({ success: false, message: '导出申请记录失败' });
    }
});

// 兼容旧版本的GET请求导出（保持向后兼容）
app.get('/exportApplications', async (req, res) => {
    const { username, role, timeRange, startDate, endDate } = req.query;
    const applications = getApplications();

    // 检查用户是否存在
    const users = getUsers();
    const user = users.find(u => u.username === username);
    if (!user) {
        return res.status(403).json({ success: false, message: '用户不存在' });
    }

    // 根据用户角色筛选可见的申请
    let filteredApps = [];
    if (role === 'admin' || role === 'readonly') {
        filteredApps = applications;
    } else {
        filteredApps = applications.filter(app => app.username === username);
    }

    // 时间范围筛选逻辑（与上面相同）
    // ... 省略重复代码

    // 创建Excel工作簿（传统方式）
    const workbook = new ExcelJS.Workbook();
    const worksheet = workbook.addWorksheet('申请记录');

    // 设置列
    worksheet.columns = [
        { header: '申请编号', key: 'applicationCode', width: 15 },
        { header: '申请人', key: 'applicant', width: 12 },
        { header: '部门', key: 'department', width: 15 },
        { header: '申请日期', key: 'date', width: 15 },
        { header: '紧急程度', key: 'priority', width: 10 },
        { header: '申请内容', key: 'content', width: 40 },
        { header: '申请金额', key: 'amount', width: 12 },
        { header: '币种', key: 'currency', width: 8 },
        { header: '状态', key: 'status', width: 12 }
    ];

    // 添加数据行
    filteredApps.forEach(app => {
        worksheet.addRow({
            applicationCode: app.applicationCode || '',
            applicant: app.applicant || '',
            department: app.department || '',
            date: app.date || '',
            priority: app.priority || '',
            content: app.content || '',
            amount: app.amount || '',
            currency: app.currency || '',
            status: app.status || ''
        });
    });

    // 设置响应头
    res.setHeader('Content-Type', 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet');

    // 设置文件名
    let fileName = '';
    if (startDate && endDate) {
        fileName = `申请记录_${startDate}_至_${endDate}.xlsx`;
    } else {
        const timeRangeText = {
            'week': '本周',
            'month': '本月',
            'year': '本年'
        }[timeRange] || '全部';
        const today = new Date().toISOString().slice(0, 10);
        fileName = `申请记录_${timeRangeText}_${today}.xlsx`;
    }

    res.setHeader('Content-Disposition', `attachment; filename=${encodeURIComponent(fileName)}`);

    // 将工作簿写入响应
    await workbook.xlsx.write(res);
    res.end();
});
```

## 🖥️ 前端实现

### 1. 新版本前端实现（推荐）

```javascript
// frontend/js/application-export.js
class ApplicationExportManager {
    constructor() {
        this.isExporting = false;
    }

    // 导出申请记录
    async exportApplicationRecords(options = {}) {
        if (this.isExporting) {
            console.log('导出正在进行中，请稍候...');
            return;
        }

        try {
            this.isExporting = true;

            // 显示加载状态
            this.showLoadingIndicator('正在导出申请记录...');

            // 获取筛选条件
            const filters = {
                username: options.username || currentUser,
                role: options.role || currentRole,
                timeRange: options.timeRange || document.getElementById('timeRange')?.value || 'all',
                startDate: options.startDate || document.getElementById('exportStartDate')?.value,
                endDate: options.endDate || document.getElementById('exportEndDate')?.value
            };

            // 生成文件名
            const filename = this.generateFilename(filters);

            // 发送POST请求
            const response = await fetch('/api/export/applications', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filters: filters,
                    filename: filename
                })
            });

            if (!response.ok) {
                const errorData = await response.json();
                throw new Error(errorData.message || '导出失败');
            }

            // 获取文件名
            const contentDisposition = response.headers.get('Content-Disposition');
            let downloadFilename = filename;
            if (contentDisposition) {
                const matches = contentDisposition.match(/filename="(.+)"/);
                if (matches) {
                    downloadFilename = decodeURIComponent(matches[1]);
                }
            }

            // 下载文件
            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = downloadFilename;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            // 显示成功消息
            this.showMessage('申请记录导出成功！', 'success');
            console.log('申请记录导出成功');

        } catch (error) {
            console.error('导出申请记录失败:', error);
            this.showMessage('导出失败: ' + error.message, 'error');
        } finally {
            this.isExporting = false;
            this.hideLoadingIndicator();
        }
    }

    // 生成文件名
    generateFilename(filters) {
        if (filters.startDate && filters.endDate) {
            return `申请记录_${filters.startDate}_至_${filters.endDate}.xlsx`;
        }

        const timeRangeText = {
            'week': '本周',
            'month': '本月',
            'year': '本年',
            'all': '全部'
        }[filters.timeRange] || '全部';

        const today = new Date().toISOString().slice(0, 10);
        return `申请记录_${timeRangeText}_${today}.xlsx`;
    }

    // 显示加载指示器
    showLoadingIndicator(message) {
        const loadingIndicator = document.createElement('div');
        loadingIndicator.id = 'exportLoadingIndicator';
        loadingIndicator.className = 'fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50';
        loadingIndicator.innerHTML = `
            <div class="bg-white p-6 rounded-lg shadow-lg">
                <div class="flex items-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mr-4"></div>
                    <p class="text-lg">${message || '导出中...'}</p>
                </div>
            </div>
        `;
        document.body.appendChild(loadingIndicator);
    }

    // 隐藏加载指示器
    hideLoadingIndicator() {
        const loadingIndicator = document.getElementById('exportLoadingIndicator');
        if (loadingIndicator) {
            document.body.removeChild(loadingIndicator);
        }
    }

    // 显示消息
    showMessage(message, type = 'info') {
        const messageDiv = document.createElement('div');
        messageDiv.className = `fixed top-4 right-4 p-4 rounded-lg shadow-lg z-50 ${
            type === 'success' ? 'bg-green-500 text-white' :
            type === 'error' ? 'bg-red-500 text-white' :
            'bg-blue-500 text-white'
        }`;
        messageDiv.textContent = message;
        document.body.appendChild(messageDiv);

        // 3秒后自动移除
        setTimeout(() => {
            if (document.body.contains(messageDiv)) {
                document.body.removeChild(messageDiv);
            }
        }, 3000);
    }
}

// 创建全局实例
const applicationExportManager = new ApplicationExportManager();

// 导出函数（供HTML直接调用）
async function exportApplicationsToExcel() {
    await applicationExportManager.exportApplicationRecords();
}
```

### 2. 兼容旧版本的前端实现

```javascript
// 保持原有的exportApplicationsToExcel函数（在index.html中）
async function exportApplicationsToExcel() {
    try {
        // 显示加载指示器
        showLoadingIndicator('正在导出申请记录...');

        // 获取当前时间范围
        const timeRange = document.getElementById('timeRange').value;

        // 构建请求URL
        let url = `/exportApplications?username=${currentUser}&role=${currentRole}&timeRange=${timeRange}`;

        // 检查是否启用了日期范围筛选
        const dateRangeContainer = document.getElementById('exportDateRangeContainer');
        if (!dateRangeContainer.classList.contains('hidden')) {
            const startDate = document.getElementById('exportStartDate').value;
            const endDate = document.getElementById('exportEndDate').value;

            if (!startDate || !endDate) {
                hideLoadingIndicator();
                alert('请选择完整的日期范围');
                return;
            }

            if (new Date(startDate) > new Date(endDate)) {
                hideLoadingIndicator();
                alert('开始日期不能大于结束日期');
                return;
            }

            url += `&startDate=${startDate}&endDate=${endDate}`;
        }

        // 使用fetch发起请求
        const response = await fetch(url);
        hideLoadingIndicator();

        if (!response.ok) {
            const errorData = await response.json();
            throw new Error(errorData.message || '导出失败');
        }

        // 获取blob数据
        const blob = await response.blob();

        // 创建下载链接
        const downloadUrl = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = downloadUrl;

        // 设置文件名
        let fileName = '';
        if (!dateRangeContainer.classList.contains('hidden')) {
            const startDate = document.getElementById('exportStartDate').value;
            const endDate = document.getElementById('exportEndDate').value;
            fileName = `申请记录_${startDate}_至_${endDate}.xlsx`;
        } else {
            const timeRangeText = {
                'week': '本周',
                'month': '本月',
                'year': '本年'
            }[timeRange] || '全部';
            const today = new Date().toISOString().slice(0, 10);
            fileName = `申请记录_${timeRangeText}_${today}.xlsx`;
        }

        a.download = fileName;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(downloadUrl);
        document.body.removeChild(a);

        console.log('申请记录导出成功');
    } catch (error) {
        hideLoadingIndicator();
        console.error('导出申请记录失败:', error);
        alert('导出申请记录失败: ' + error.message);
    }
}
```
