/* 设备管理模块样式 */

/* 设备统计概览样式 */
.device-stats-section {
    margin-bottom: 1.5rem;
}

/* 统计数字样式优化 */
.stat-card .text-3xl {
    font-size: 2rem;
    line-height: 1.2;
}

.stat-card .text-sm {
    font-size: 0.8rem;
    opacity: 0.9;
}

.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
    gap: 1rem;
    margin-bottom: 1.25rem;
}

.stat-card {
    background: linear-gradient(135deg, var(--from-color), var(--to-color));
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 2px 4px -1px rgba(0, 0, 0, 0.1), 0 1px 2px -1px rgba(0, 0, 0, 0.06);
    transition: transform 0.2s ease-in-out, box-shadow 0.2s ease-in-out;
}

.stat-card:hover {
    transform: translateY(-1px);
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.charts-grid {
    display: grid;
    grid-template-columns: 1fr 1.2fr;
    gap: 1.5rem;
}

.chart-card {
    background: white;
    border-radius: 0.75rem;
    padding: 1.25rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
    border: 1px solid #e5e7eb;
    transition: box-shadow 0.2s ease-in-out;
}

.chart-card:hover {
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.chart-container {
    position: relative;
    height: 220px;
    width: 100%;
}

/* 饼图容器特殊样式 */
.chart-card.doughnut-chart .chart-container {
    height: 200px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 柱状图容器特殊样式 */
.chart-card.bar-chart .chart-container {
    height: 200px;
}

/* 图表标题样式优化 */
.chart-title {
    font-size: 1rem;
    font-weight: 600;
    color: #374151;
    margin-bottom: 0.75rem;
}

.chart-legend {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    font-size: 0.75rem;
    color: #6b7280;
}

.legend-item {
    display: flex;
    align-items: center;
    gap: 0.25rem;
}

.legend-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
}

/* 响应式设计 */
@media (max-width: 1024px) {
    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 180px;
    }

    .chart-card.doughnut-chart .chart-container,
    .chart-card.bar-chart .chart-container {
        height: 180px;
    }
}

@media (max-width: 768px) {
    .stats-grid {
        grid-template-columns: repeat(2, 1fr);
    }

    .charts-grid {
        grid-template-columns: 1fr;
    }

    .chart-container {
        height: 160px;
    }

    .chart-card.doughnut-chart .chart-container,
    .chart-card.bar-chart .chart-container {
        height: 160px;
    }
}

@media (max-width: 640px) {
    .stats-grid {
        grid-template-columns: 1fr;
    }

    .stat-card {
        padding: 1rem;
    }

    .chart-card {
        padding: 1rem;
    }

    .chart-container {
        height: 140px;
    }

    .chart-card.doughnut-chart .chart-container,
    .chart-card.bar-chart .chart-container {
        height: 140px;
    }
}

/* 排序表头样式 */
.sortable {
    user-select: none;
    transition: background-color 0.15s ease-in-out;
}

.sortable:hover {
    background-color: #f3f4f6 !important;
}

.sortable svg {
    transition: color 0.15s ease-in-out;
}

/* 全局select样式重置 - 防止重复下拉图标 */
select {
    -webkit-appearance: none !important;
    -moz-appearance: none !important;
    appearance: none !important;
}

/* 设备管理页面布局 */
.device-management-container {
    max-width: 100%;
    margin: 0 auto;
    padding: 1rem;
}

/* 设备管理头部 */
.device-management-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 1.5rem;
    flex-wrap: wrap;
    gap: 1rem;
}

.device-management-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.device-management-actions {
    display: flex;
    gap: 0.5rem;
    flex-wrap: wrap;
}

/* 筛选器样式 */
.device-filters {
    background: white;
    border-radius: 0.5rem;
    padding: 1rem;
    margin-bottom: 1rem;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
}

.filter-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    align-items: end;
}

.filter-group {
    display: flex;
    flex-direction: column;
}

.filter-label {
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.filter-input {
    padding: 0.5rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.filter-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

/* 为筛选器中的select元素添加下拉图标 */
.filter-input[type="select"],
select.filter-input {
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

/* 设备表格样式 */
.device-table-container {
    background: white;
    border-radius: 0.5rem;
    overflow-x: auto; /* 启用横向滚动 */
    overflow-y: visible;
    box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1);
    /* 确保滚动条可见 */
    scrollbar-width: thin;
    scrollbar-color: #cbd5e1 #f1f5f9;
}

.device-table {
    width: 100%;
    min-width: 1000px; /* 设置最小宽度，确保所有列都能显示 */
    border-collapse: collapse;
    table-layout: fixed; /* 固定表格布局 */
}

/* 设置各列的固定宽度 */
.device-table th:nth-child(1),
.device-table td:nth-child(1) {
    width: 50px; /* 复选框列 */
    min-width: 50px;
}

.device-table th:nth-child(2),
.device-table td:nth-child(2) {
    width: 120px; /* 设备编号列 */
    min-width: 120px;
}

.device-table th:nth-child(3),
.device-table td:nth-child(3) {
    width: 150px; /* 设备名称列 */
    min-width: 150px;
}

.device-table th:nth-child(4),
.device-table td:nth-child(4) {
    width: 120px; /* 厂家列 */
    min-width: 120px;
}

.device-table th:nth-child(5),
.device-table td:nth-child(5) {
    width: 120px; /* 位置列 */
    min-width: 120px;
}

.device-table th:nth-child(6),
.device-table td:nth-child(6) {
    width: 100px; /* 负责人列 */
    min-width: 100px;
}

.device-table th:nth-child(7),
.device-table td:nth-child(7) {
    width: 100px; /* 进厂日期列 */
    min-width: 100px;
}

.device-table th:nth-child(8),
.device-table td:nth-child(8) {
    width: 80px; /* 状态列 */
    min-width: 80px;
}

.device-table th:nth-child(9),
.device-table td:nth-child(9) {
    width: 150px; /* 操作列 */
    min-width: 150px;
}

/* 表格单元格样式优化 */
.device-table th {
    background-color: #f9fafb;
    padding: 0.75rem 1rem;
    text-align: left;
    font-size: 0.75rem;
    font-weight: 500;
    color: #374151;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid #e5e7eb;
    white-space: nowrap; /* 防止表头换行 */
    overflow: hidden;
    text-overflow: ellipsis;
}

.device-table td {
    padding: 0.75rem 1rem;
    border-bottom: 1px solid #e5e7eb;
    font-size: 0.875rem;
    color: #1f2937;
    vertical-align: middle;
}

/* 特定列的文本处理 */
/* 设备编号、负责人、进厂日期列 - 不换行且完整显示 */
.device-table td:nth-child(2),
.device-table td:nth-child(6),
.device-table td:nth-child(7) {
    white-space: nowrap;
    overflow: visible; /* 改为visible，确保内容完整显示 */
    text-overflow: clip; /* 改为clip，不使用省略号 */
}

/* 设备名称、厂家、位置列 - 允许换行但限制高度 */
.device-table td:nth-child(3),
.device-table td:nth-child(4),
.device-table td:nth-child(5) {
    white-space: normal;
    word-wrap: break-word;
    word-break: break-all;
    max-height: 60px;
    overflow: hidden;
    line-height: 1.4;
}

/* 状态列 - 居中显示 */
.device-table td:nth-child(8) {
    text-align: center;
    white-space: nowrap;
}

/* 操作列 - 确保按钮不换行 */
.device-table td:nth-child(9) {
    white-space: nowrap;
    overflow: visible; /* 允许下拉菜单等元素溢出 */
}

/* 自定义滚动条样式 (Webkit浏览器) */
.device-table-container::-webkit-scrollbar {
    height: 8px;
}

.device-table-container::-webkit-scrollbar-track {
    background: #f1f5f9;
    border-radius: 4px;
}

.device-table-container::-webkit-scrollbar-thumb {
    background: #cbd5e1;
    border-radius: 4px;
}

.device-table-container::-webkit-scrollbar-thumb:hover {
    background: #94a3b8;
}

/* 表格行悬停效果 */
.device-table tbody tr:hover {
    background-color: #f9fafb;
}

/* 状态标签样式 */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 0.25rem 0.5rem;
    border-radius: 9999px;
    font-size: 0.75rem;
    font-weight: 500;
    line-height: 1.25;
}

.status-badge.active {
    background-color: #dcfce7;
    color: #166534;
}

.status-badge.inactive {
    background-color: #fee2e2;
    color: #991b1b;
}

/* 操作按钮样式 */
.action-button {
    padding: 0.25rem 0.5rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    display: inline-block;
}

.action-button.view {
    color: #2563eb;
    background-color: transparent;
}

.action-button.view:hover {
    color: #1d4ed8;
    background-color: #dbeafe;
}

.action-button.edit {
    color: #7c3aed;
    background-color: transparent;
}

.action-button.edit:hover {
    color: #6d28d9;
    background-color: #ede9fe;
}

.action-button.delete {
    color: #dc2626;
    background-color: transparent;
}

.action-button.delete:hover {
    color: #b91c1c;
    background-color: #fee2e2;
}

/* 分页样式 */
.pagination-container {
    padding: 1rem 1.5rem;
    background-color: #f9fafb;
    border-top: 1px solid #e5e7eb;
}

.pagination-info {
    font-size: 0.875rem;
    color: #374151;
}

.pagination-controls {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.pagination-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    background-color: white;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
}

.pagination-button:hover:not(:disabled) {
    background-color: #f9fafb;
}

.pagination-button:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-current {
    padding: 0.5rem 0.75rem;
    font-size: 0.875rem;
    background-color: #dbeafe;
    border: 1px solid #93c5fd;
    border-radius: 0.375rem;
    color: #1e40af;
}

/* 模态框样式 */
.device-modal {
    position: fixed;
    inset: 0;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 50;
    padding: 1rem;
}

.device-modal-content {
    background-color: white;
    border-radius: 0.5rem;
    box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    width: 100%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
}

.device-modal-header {
    padding: 1.5rem 1.5rem 1rem 1.5rem;
    border-bottom: 1px solid #e5e7eb;
}

.device-modal-title {
    font-size: 1.125rem;
    font-weight: 600;
    color: #1f2937;
    margin: 0;
}

.device-modal-body {
    padding: 1.5rem;
}

.device-modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e5e7eb;
    display: flex;
    justify-content: flex-end;
    gap: 0.75rem;
}

/* 表单样式 */
.form-group {
    margin-bottom: 1rem;
}

.form-label {
    display: block;
    font-size: 0.875rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.25rem;
}

.form-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.form-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-input:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

.form-select {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    background-color: white;
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right 0.5rem center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
    -webkit-appearance: none;
    -moz-appearance: none;
    appearance: none;
}

.form-select:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.form-select:disabled {
    background-color: #f9fafb;
    color: #6b7280;
    cursor: not-allowed;
}

/* 按钮样式 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    font-weight: 500;
    border-radius: 0.375rem;
    border: none;
    cursor: pointer;
    transition: all 0.15s ease-in-out;
    text-decoration: none;
    gap: 0.5rem;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.btn-primary {
    background-color: #3b82f6;
    color: white;
}

.btn-primary:hover:not(:disabled) {
    background-color: #2563eb;
}

.btn-secondary {
    background-color: #6b7280;
    color: white;
}

.btn-secondary:hover:not(:disabled) {
    background-color: #4b5563;
}

.btn-success {
    background-color: #10b981;
    color: white;
}

.btn-success:hover:not(:disabled) {
    background-color: #059669;
}

.btn-danger {
    background-color: #ef4444;
    color: white;
}

.btn-danger:hover:not(:disabled) {
    background-color: #dc2626;
}

.btn-outline {
    background-color: transparent;
    border: 1px solid #d1d5db;
    color: #374151;
}

.btn-outline:hover:not(:disabled) {
    background-color: #f9fafb;
}

/* 滚动提示样式 */
.scroll-hint {
    position: relative;
    margin-bottom: 0.5rem;
}

.scroll-hint::after {
    content: "← 左右滑动查看更多列 →";
    display: block;
    text-align: center;
    font-size: 0.75rem;
    color: #6b7280;
    padding: 0.25rem;
    background-color: #f9fafb;
    border-radius: 0.25rem;
    border: 1px solid #e5e7eb;
}

/* 当表格宽度超出容器时显示滚动提示 */
@media (max-width: 1000px) {
    .scroll-hint::after {
        display: block;
    }
}

@media (min-width: 1001px) {
    .scroll-hint::after {
        display: none;
    }
}

/* 响应式优化 */
@media (max-width: 1024px) {
    .device-table {
        min-width: 900px; /* 在中等屏幕上稍微减少最小宽度 */
    }
}

@media (max-width: 768px) {
    .device-management-header {
        flex-direction: column;
        align-items: stretch;
    }

    .device-management-actions {
        justify-content: center;
    }

    .filter-grid {
        grid-template-columns: 1fr;
    }

    .device-table {
        min-width: 800px; /* 在小屏幕上进一步减少最小宽度 */
    }

    /* 在移动设备上调整列宽 */
    .device-table th:nth-child(3),
    .device-table td:nth-child(3) {
        width: 120px;
        min-width: 120px;
    }

    .device-table th:nth-child(4),
    .device-table td:nth-child(4) {
        width: 100px;
        min-width: 100px;
    }

    .device-table th:nth-child(5),
    .device-table td:nth-child(5) {
        width: 100px;
        min-width: 100px;
    }

    .device-table th:nth-child(9),
    .device-table td:nth-child(9) {
        width: 120px;
        min-width: 120px;
    }

    .device-modal-content {
        margin: 0.5rem;
        max-width: none;
    }

    .device-modal-footer {
        flex-direction: column;
    }

    .btn {
        width: 100%;
        justify-content: center;
    }
}

/* 选择计数器样式 */
.selection-counter {
    font-size: 0.875rem;
    color: #3b82f6;
    font-weight: 500;
    padding: 0.5rem;
    background-color: #dbeafe;
    border-radius: 0.375rem;
    margin-bottom: 1rem;
    text-align: center;
}

/* 空状态样式 */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #6b7280;
}

.empty-state-icon {
    width: 3rem;
    height: 3rem;
    margin: 0 auto 1rem;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 1.125rem;
    font-weight: 500;
    color: #374151;
    margin-bottom: 0.5rem;
}

.empty-state-description {
    font-size: 0.875rem;
    color: #6b7280;
}

/* 设备搜索组件样式 */
.device-search-container {
    position: relative;
    width: 100%;
}

.device-search-input {
    width: 100%;
    padding: 0.5rem 0.75rem;
    border: 1px solid #d1d5db;
    border-radius: 0.375rem;
    font-size: 0.875rem;
    transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
}

.device-search-input:focus {
    outline: none;
    border-color: #3b82f6;
    box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.device-search-results {
    position: absolute;
    top: 100%;
    left: 0;
    right: 0;
    background: white;
    border: 1px solid #d1d5db;
    border-top: none;
    border-radius: 0 0 0.375rem 0.375rem;
    max-height: 200px;
    overflow-y: auto;
    z-index: 1000;
    box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
}

.device-search-item {
    padding: 0.75rem;
    cursor: pointer;
    border-bottom: 1px solid #f3f4f6;
    transition: background-color 0.15s ease-in-out;
}

.device-search-item:last-child {
    border-bottom: none;
}

.device-search-item:hover {
    background-color: #f9fafb;
}

.device-search-item.selected {
    background-color: #dbeafe;
    color: #1e40af;
}

.device-search-item-title {
    font-weight: 500;
    color: #1f2937;
    margin-bottom: 0.25rem;
}

.device-search-item-subtitle {
    font-size: 0.75rem;
    color: #6b7280;
}

.device-search-no-results {
    padding: 0.75rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
}

/* 设备搜索加载状态 */
.device-search-loading {
    padding: 0.75rem;
    text-align: center;
    color: #6b7280;
    font-size: 0.875rem;
}

.device-search-loading::after {
    content: '';
    display: inline-block;
    width: 12px;
    height: 12px;
    margin-left: 0.5rem;
    border: 2px solid #d1d5db;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
