# 系统架构文档

## 🏗️ 整体架构

管理系统采用经典的三层架构模式，确保系统的可维护性、可扩展性和安全性。

```
┌─────────────────────────────────────────────────────────────┐
│                    前端展示层 (Frontend)                      │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   HTML5     │ │  Tailwind   │ │  Chart.js   │ │ PDF.js  │ │
│  │   页面      │ │    CSS      │ │   图表      │ │ 文档    │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                         HTTP/HTTPS
                              │
┌─────────────────────────────────────────────────────────────┐
│                   业务逻辑层 (Backend)                       │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │  Express    │ │   设备管理   │ │   用户管理   │ │ 导出服务│ │
│  │   服务器    │ │    模块     │ │    模块     │ │  模块   │ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
                              │
                         SQL查询
                              │
┌─────────────────────────────────────────────────────────────┐
│                    数据存储层 (Database)                     │
│  ┌─────────────┐ ┌─────────────┐ ┌─────────────┐ ┌─────────┐ │
│  │   SQLite    │ │   文件存储   │ │   日志系统   │ │ 备份系统│ │
│  │   数据库    │ │   (uploads)  │ │   (logs)    │ │(archive)│ │
│  └─────────────┘ └─────────────┘ └─────────────┘ └─────────┘ │
└─────────────────────────────────────────────────────────────┘
```

## 🎯 技术栈

### 前端技术栈
- **HTML5** - 语义化标记，支持现代Web标准
- **Tailwind CSS** - 实用优先的CSS框架，快速构建响应式界面
- **Vanilla JavaScript** - 原生JavaScript，无框架依赖，性能优异
- **Chart.js** - 数据可视化图表库，支持多种图表类型
- **PDF.js** - PDF文件处理和预览
- **html2canvas** - HTML转图片，支持截图功能
- **jsPDF** - 客户端PDF生成

### 后端技术栈
- **Node.js** - JavaScript运行时环境
- **Express.js** - Web应用框架，提供路由和中间件
- **SQLite** - 轻量级关系型数据库
- **Multer** - 文件上传中间件
- **Nodemailer** - 邮件发送服务
- **ExcelJS** - Excel文件处理
- **bcrypt** - 密码加密
- **Helmet** - 安全中间件
- **CORS** - 跨域资源共享

## 📁 模块化设计

### 后端模块结构

```
backend/
├── server.js              # 主服务器入口
├── data-access.js          # 数据访问层抽象
├── database.js             # 数据库连接和初始化
└── modules/                # 业务模块
    ├── device-management.js      # 设备管理
    ├── device-health.js          # 设备健康监控
    ├── maintenance-management.js # 维修保养管理
    └── export-service.js         # 数据导出服务
```

### 前端模块结构

```
frontend/
├── index.html              # 主页面入口
├── css/                    # 样式文件
│   ├── dashboard.css             # 仪表板样式
│   ├── device-management.css     # 设备管理样式
│   ├── maintenance-management.css # 维修管理样式
│   └── user-management.css       # 用户管理样式
├── js/                     # JavaScript文件
│   ├── api.js                    # API接口封装
│   ├── dashboard.js              # 仪表板功能
│   ├── device-management.js      # 设备管理功能
│   ├── maintenance-management.js # 维修管理功能
│   ├── user-management.js        # 用户管理功能
│   ├── libs/                     # 第三方库
│   └── utils/                    # 工具函数
└── pages/                  # 页面文件
```

## 🔄 数据流架构

### 请求处理流程

```
用户操作 → 前端JavaScript → API请求 → Express路由 → 业务模块 → 数据访问层 → SQLite数据库
    ↑                                                                              ↓
响应数据 ← 前端渲染 ← JSON响应 ← Express响应 ← 业务逻辑 ← 数据处理 ← 查询结果
```

### 数据缓存策略

1. **内存缓存** - 热点数据缓存，减少数据库查询
2. **静态资源缓存** - 浏览器缓存，提高加载速度
3. **API响应缓存** - 短期缓存，平衡性能和数据实时性

## 🛡️ 安全架构

### 多层安全防护

1. **网络层安全**
   - HTTPS加密传输
   - CORS跨域保护
   - 请求速率限制

2. **应用层安全**
   - Helmet安全头设置
   - 输入数据验证
   - SQL注入防护

3. **数据层安全**
   - 密码bcrypt加密
   - 敏感数据加密存储
   - 数据库访问控制

### 权限控制模型

```
用户 → 角色 → 权限 → 资源
 │      │      │      │
 └──────┴──────┴──────┴─── 访问控制矩阵
```

## 📊 数据库设计

### 核心数据表

```sql
-- 用户表
users (id, username, password, email, role, department_id, created_at)

-- 设备表  
devices (id, name, model, serial_number, location, status, health_score)

-- 维修记录表
maintenance_records (id, device_id, type, description, cost, date, technician)

-- 申请表
applications (id, title, content, applicant, status, created_at, updated_at)

-- 部门表
departments (id, name, description, manager_id)

-- 厂区表
factories (id, name, location, description)
```

### 数据关系图

```
users ──┐
        ├── departments
        └── maintenance_records
                │
devices ────────┴── device_health_logs
   │
   └── applications
```

## 🚀 性能优化

### 前端优化策略

1. **资源本地化** - 所有第三方库本地部署，避免CDN依赖
2. **代码分割** - 按需加载，减少初始包大小
3. **图片优化** - WebP格式，懒加载
4. **缓存策略** - 浏览器缓存，Service Worker

### 后端优化策略

1. **数据库优化**
   - 索引优化
   - 查询优化
   - 连接池管理

2. **内存管理**
   - 对象池
   - 垃圾回收优化
   - 内存泄漏监控

3. **网络优化**
   - Gzip压缩
   - Keep-Alive连接
   - 响应缓存

## 🔧 部署架构

### 单机部署模式

```
┌─────────────────────────────────────┐
│            服务器 (Linux/Windows)     │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │   Node.js   │ │     SQLite      │ │
│  │   应用服务   │ │     数据库      │ │
│  └─────────────┘ └─────────────────┘ │
│  ┌─────────────┐ ┌─────────────────┐ │
│  │   静态文件   │ │     日志系统     │ │
│  │   (uploads)  │ │     (logs)      │ │
│  └─────────────┘ └─────────────────┘ │
└─────────────────────────────────────┘
```

### 扩展部署模式

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│  负载均衡器  │    │   应用服务器  │    │   数据库服务器│
│   (Nginx)   │────│  (Node.js)  │────│   (SQLite)  │
└─────────────┘    └─────────────┘    └─────────────┘
                           │
                   ┌─────────────┐
                   │   文件存储   │
                   │   (NFS/S3)  │
                   └─────────────┘
```

## 📈 监控和运维

### 系统监控指标

1. **性能指标**
   - 响应时间
   - 吞吐量
   - 错误率
   - 资源使用率

2. **业务指标**
   - 用户活跃度
   - 功能使用率
   - 数据增长趋势

3. **安全指标**
   - 异常访问
   - 登录失败次数
   - 权限违规操作

### 日志管理

```
logs/
├── combined.log    # 综合日志
├── error.log       # 错误日志
├── access.log      # 访问日志
└── security.log    # 安全日志
```

## 🔄 持续集成/持续部署

### CI/CD流程

```
代码提交 → 自动测试 → 构建打包 → 部署测试环境 → 人工验证 → 部署生产环境
    ↓           ↓           ↓            ↓            ↓            ↓
  Git Hook   单元测试    Docker构建   自动化测试    手动测试    滚动更新
```

---

*本文档描述了管理系统的完整技术架构，为开发、部署和维护提供指导。*
