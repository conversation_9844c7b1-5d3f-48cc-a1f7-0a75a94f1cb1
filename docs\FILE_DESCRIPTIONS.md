# 文件结构说明

本文档详细描述了管理系统中所有重要文件和目录的用途和功能。

## 📁 根目录文件

### 📋 配置文件
- **`package.json`** - Node.js项目配置文件，包含依赖包、脚本命令和项目元信息
- **`package-lock.json`** - 锁定依赖包版本，确保团队开发环境一致性
- **`LICENSE`** - MIT开源许可证文件，定义项目使用条款和版权信息

### 📖 文档文件
- **`README.md`** - 项目主文档，包含功能介绍、安装指南、使用说明
- **`FUNC.md`** - 详细功能说明文档，描述各模块具体功能
- **`CODING_RULES.md`** - 开发规范文档，定义代码风格和开发原则
- **`Excel格式修复说明.md`** - Excel文件处理相关的技术说明
- **`FILE_DESCRIPTIONS.md`** - 本文件，项目文件结构详细说明

## 🖥️ 后端目录 (`backend/`)

### 🔧 核心服务文件
- **`server.js`** - Express服务器主入口文件，配置路由、中间件和服务启动
- **`database.js`** - 数据库连接和初始化配置，SQLite数据库操作封装
- **`data-access.js`** - 数据访问层，提供统一的数据库操作接口

### 📊 数据存储 (`backend/data/`)
- **`application_system.db`** - SQLite主数据库文件，存储所有业务数据
- **`application_system.db-shm`** - SQLite共享内存文件，提高数据库性能
- **`application_system.db-wal`** - SQLite预写日志文件，确保数据完整性
- **`database.db`** - 备用数据库文件
- **`json_backup_2025-06-23/`** - JSON格式数据备份目录

### 🔨 功能模块 (`backend/modules/`)
- **`device-health.js`** - 设备健康监控模块，设备状态检测和预警
- **`device-management.js`** - 设备管理模块，设备信息CRUD操作
- **`export-service.js`** - 数据导出服务，支持Excel、PDF等格式导出
- **`maintenance-management.js`** - 维修保养管理模块，维修记录和计划管理

## 🎨 前端目录 (`frontend/`)

### 🏠 主页面
- **`index.html`** - 系统主页面，单页应用入口
- **`index_backup .html`** - 主页面备份文件

### 🎨 样式文件 (`frontend/css/`)
- **`dashboard.css`** - 仪表板页面样式
- **`device-health.css`** - 设备健康监控页面样式
- **`device-management.css`** - 设备管理页面样式
- **`maintenance-management.css`** - 维修管理页面样式
- **`todo-manager.css`** - 待办事项管理页面样式
- **`user-management.css`** - 用户管理页面样式

### 🖼️ 图片资源 (`frontend/img/`)
- **`Makrite-logo.png`** - 公司Logo图片

### ⚡ JavaScript文件 (`frontend/js/`)

#### 🔧 核心功能模块
- **`api.js`** - API接口封装，统一管理前后端数据交互
- **`dashboard.js`** - 仪表板功能实现，数据统计和图表展示
- **`device-management.js`** - 设备管理功能，设备增删改查操作
- **`device-health.js`** - 设备健康监控功能，实时状态显示
- **`maintenance-management.js`** - 维修管理功能，维修记录和计划管理
- **`user-management.js`** - 用户管理功能，用户权限和信息管理

#### 🏢 组织管理模块
- **`department-management.js`** - 部门管理功能，组织架构管理
- **`factory-management.js`** - 厂区管理功能，厂区信息维护

#### 🔍 搜索和统计模块
- **`device-search.js`** - 设备搜索功能，支持多条件筛选
- **`device-stats.js`** - 设备统计功能，数据分析和报表

#### 📤 导出和工具模块
- **`export-manager.js`** - 数据导出管理，支持多种格式导出
- **`todo-manager.js`** - 待办事项管理，任务跟踪和提醒
- **`network-diagnostics.js`** - 网络诊断工具，系统健康检查

#### 🎛️ UI控制模块
- **`content-modal.js`** - 模态框内容管理，弹窗交互控制
- **`modal-height-control.js`** - 模态框高度自适应控制

#### 📚 第三方库目录 (`frontend/js/libs/`)
存放本地化的第三方JavaScript库，避免CDN依赖：
- Chart.js - 图表绘制库
- PDF.js - PDF文件处理库
- 其他工具库

#### 🛠️ 工具函数 (`frontend/js/utils/`)
存放通用工具函数和辅助模块

### 📄 页面目录 (`frontend/pages/`)
存放各功能模块的HTML页面文件

## 📁 其他目录

### 📦 上传文件 (`uploads/`)
用户上传的文件存储目录，包含：
- PDF文档（合同、报价单、申请书等）
- 图片文件
- 其他附件

### 📚 归档目录 (`archive/`)
历史数据和备份文件存储目录，按时间戳组织：
- `app_[timestamp]/` - 应用状态快照备份

### 📝 日志目录 (`logs/`)
系统运行日志文件：
- **`combined.log`** - 综合日志，记录所有系统活动
- **`error.log`** - 错误日志，记录系统错误和异常
- **`out.log`** - 输出日志，记录系统标准输出

### 📦 依赖包 (`node_modules/`)
Node.js依赖包安装目录，由npm自动管理，包含：
- Express框架及相关中间件
- 数据库操作库
- 文件处理库
- 安全防护库
- 其他工具库

## 🔧 文件命名规范

### 📝 文档文件
- 使用大写字母和下划线：`README.md`、`CODING_RULES.md`
- 中文文档使用描述性名称：`Excel格式修复说明.md`

### 💻 代码文件
- 使用小写字母和连字符：`device-management.js`
- 功能模块按用途分组：`device-*`、`user-*`、`maintenance-*`

### 🎨 样式文件
- 与对应JavaScript文件同名：`device-management.css`
- 使用小写字母和连字符

### 📊 数据文件
- 数据库文件使用下划线：`application_system.db`
- 备份文件包含时间戳：`json_backup_2025-06-23`

## 📋 维护说明

1. **定期清理**：定期清理`uploads/`和`archive/`目录中的过期文件
2. **日志轮转**：配置日志轮转，避免日志文件过大
3. **依赖更新**：定期检查和更新`package.json`中的依赖包
4. **备份策略**：定期备份数据库和重要配置文件
5. **文档同步**：代码变更时及时更新相关文档

---

*本文档随项目发展持续更新，最后更新时间：2025-07-03*
