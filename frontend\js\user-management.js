/**
 * 用户管理模块 - 独立JavaScript文件
 * 遵循模块化设计原则，提供完整的用户管理功能
 */

class UserManager {
    constructor() {
        this.users = [];
        this.filteredUsers = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.isLoading = false;
        this.searchTimeout = null;
        this.deleteMode = false;
        this.selectedUsers = new Set();

        // 当前用户信息
        this.currentUser = sessionStorage.getItem('username') || '';
        this.currentRole = sessionStorage.getItem('role') || '';

        // 筛选条件
        this.filters = {
            search: '',
            role: '',
            department: '',
            status: 'all' // all, active, inactive
        };

        // 排序相关属性
        this.sortField = null;
        this.sortDirection = 'asc'; // 'asc' 或 'desc'

        this.init();
    }

    /**
     * 初始化用户管理模块
     */
    init() {
        this.initializeUI();
        this.bindEvents();
        // 初始化时静默加载，不显示成功消息
        this.loadUsers(false);
        this.updateDepartmentOptions(); // 初始化时加载部门选项
    }

    /**
     * 绑定事件监听器
     */
    bindEvents() {
        // 搜索功能 - 实时搜索
        const searchInput = document.getElementById('userSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.handleSearch(e.target.value);
            });
        }

        // 绑定筛选器事件
        this.bindFilterEvents();

        // 分页事件
        document.addEventListener('click', (e) => {
            if (e.target.matches('.pagination-btn')) {
                const page = parseInt(e.target.dataset.page);
                this.goToPage(page);
            }
        });

        // 批量选择
        const selectAllCheckbox = document.getElementById('selectAllUsers');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => {
                this.handleSelectAll(e.target.checked);
            });
        }

        // 模态框事件
        this.bindModalEvents();

        // 表格排序事件
        this.bindSortEvents();
    }

    /**
     * 绑定筛选器事件
     */
    bindFilterEvents() {
        // 筛选器事件
        const roleFilter = document.getElementById('userRoleFilter');
        const departmentFilter = document.getElementById('userDepartmentFilter');
        const statusFilter = document.getElementById('userStatusFilter');

        if (roleFilter) {
            roleFilter.addEventListener('change', async (e) => {
                this.filters.role = e.target.value;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(roleFilter);
                await this.applyFilters();
                this.hideFilterLoading(roleFilter);
            });
        }

        if (departmentFilter) {
            departmentFilter.addEventListener('change', async (e) => {
                this.filters.department = e.target.value;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(departmentFilter);
                await this.applyFilters();
                this.hideFilterLoading(departmentFilter);
            });
        }

        if (statusFilter) {
            statusFilter.addEventListener('change', async (e) => {
                this.filters.status = e.target.value;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(statusFilter);
                await this.applyFilters();
                this.hideFilterLoading(statusFilter);
            });
        }
    }

    /**
     * 显示筛选器加载状态
     */
    showFilterLoading(filterElement) {
        if (!filterElement) return;

        // 先清除已存在的加载指示器，避免重复显示
        this.hideFilterLoading(filterElement);

        filterElement.style.opacity = '0.6';
        filterElement.style.pointerEvents = 'none';

        // 添加加载指示器
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'filter-loading-indicator';
        loadingIndicator.innerHTML = `
            <div class="inline-flex items-center text-blue-600 text-sm">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                筛选中...
            </div>
        `;

        filterElement.parentNode.appendChild(loadingIndicator);
    }

    /**
     * 隐藏筛选器加载状态
     */
    hideFilterLoading(filterElement) {
        if (!filterElement) return;

        filterElement.style.opacity = '1';
        filterElement.style.pointerEvents = 'auto';

        // 移除加载指示器
        const loadingIndicator = filterElement.parentNode.querySelector('.filter-loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }

    /**
     * 绑定排序事件
     */
    bindSortEvents() {
        // 表格排序功能
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const sortField = e.currentTarget.dataset.sort;
                if (sortField) {
                    this.sortTable(sortField);
                }
            });
        });
    }

    /**
     * 绑定模态框相关事件
     */
    bindModalEvents() {
        // 添加用户表单
        const addUserForm = document.getElementById('addUserForm');
        if (addUserForm) {
            addUserForm.addEventListener('submit', (e) => this.handleAddUser(e));
        }

        // 编辑用户表单
        const editUserForm = document.getElementById('editUserForm');
        if (editUserForm) {
            editUserForm.addEventListener('submit', (e) => this.handleEditUser(e));
        }

        // 角色变化时的部门字段控制
        const newUserRole = document.getElementById('newUserRole');
        const editUserRole = document.getElementById('editRole');

        if (newUserRole) {
            newUserRole.addEventListener('change', () => this.toggleDepartmentField('add'));
        }

        if (editUserRole) {
            editUserRole.addEventListener('change', () => this.toggleDepartmentField('edit'));
        }
    }

    /**
     * 初始化UI组件
     */
    initializeUI() {
        this.createAdvancedFilters();
        this.createBatchOperationBar();
        this.createUserStatsCards();
        this.updateUIPermissions();
    }

    /**
     * 创建高级筛选器UI
     */
    createAdvancedFilters() {
        const filtersContainer = document.querySelector('.user-filters-container');
        if (!filtersContainer) return;

        const filtersHTML = `
            <div class="advanced-filters bg-gray-50 p-4 rounded-lg mb-4">
                <div class="grid grid-cols-1 md:grid-cols-4 gap-4">
                    <div class="filter-group">
                        <label class="block text-sm font-medium text-gray-700 mb-1">角色筛选</label>
                        <div class="relative">
                            <select id="userRoleFilter" class="w-full p-2 pr-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 appearance-none">
                                <option value="">全部角色</option>
                                <option value="admin">管理员</option>
                                <option value="ceo">CEO</option>
                                <option value="manager">经理</option>
                                <option value="chief">总监</option>
                                <option value="director">厂长</option>
                                <option value="user">普通用户</option>
                                <option value="readonly">只读用户</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label class="block text-sm font-medium text-gray-700 mb-1">部门筛选</label>
                        <div class="relative">
                            <select id="userDepartmentFilter" class="w-full p-2 pr-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 appearance-none">
                                <option value="">全部部门</option>
                                <!-- 部门选项将通过JavaScript动态加载 -->
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="filter-group">
                        <label class="block text-sm font-medium text-gray-700 mb-1">状态筛选</label>
                        <div class="relative">
                            <select id="userStatusFilter" class="w-full p-2 pr-8 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 appearance-none">
                                <option value="all">全部状态</option>
                                <option value="active">活跃用户</option>
                                <option value="inactive">非活跃用户</option>
                            </select>
                            <div class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
                                <svg class="w-4 h-4 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div class="filter-group flex items-end">
                        <button id="resetFiltersBtn" class="bg-gray-500 text-white px-3 py-2 rounded-md hover:bg-gray-600 transition-colors text-sm whitespace-nowrap">
                            重置筛选
                        </button>
                    </div>
                </div>
            </div>
        `;

        filtersContainer.innerHTML = filtersHTML;

        // 绑定重置按钮事件
        document.getElementById('resetFiltersBtn')?.addEventListener('click', () => {
            this.resetFilters();
        });

        // 重新绑定筛选器事件
        this.bindFilterEvents();
    }

    /**
     * 创建批量操作栏
     */
    createBatchOperationBar() {
        const container = document.querySelector('.user-management-actions');
        if (!container) return;

        const batchBarHTML = `
            <div id="batchOperationBar" class="batch-operation-bar hidden bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center space-x-2">
                        <span class="text-blue-700 font-medium">已选择 <span id="selectedCount">0</span> 个用户</span>
                    </div>
                    <div class="flex items-center space-x-2">
                        <button id="batchDeleteBtn" class="bg-red-600 text-white px-3 py-1 rounded-md hover:bg-red-700 text-sm">
                            批量删除
                        </button>
                        <button id="batchExportBtn" class="bg-green-600 text-white px-3 py-1 rounded-md hover:bg-green-700 text-sm">
                            导出选中
                        </button>
                        <button id="cancelBatchBtn" class="bg-gray-500 text-white px-3 py-1 rounded-md hover:bg-gray-600 text-sm">
                            取消选择
                        </button>
                    </div>
                </div>
            </div>
        `;

        container.insertAdjacentHTML('afterend', batchBarHTML);

        // 绑定批量操作事件
        document.getElementById('batchDeleteBtn')?.addEventListener('click', () => this.handleBatchDelete());
        document.getElementById('batchExportBtn')?.addEventListener('click', () => this.handleBatchExport());
        document.getElementById('cancelBatchBtn')?.addEventListener('click', () => this.cancelBatchSelection());
    }

    /**
     * 创建用户统计卡片
     */
    createUserStatsCards() {
        const statsContainer = document.querySelector('.user-stats-container');
        if (!statsContainer) return;

        const statsHTML = `
            <div class="user-stats-grid grid grid-cols-1 md:grid-cols-4 gap-4 mb-6">
                <div class="stat-card bg-gradient-to-r from-blue-500 to-blue-600 text-white p-4 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-blue-100 text-sm font-medium">用户总数</p>
                            <p id="totalUsersCount" class="text-2xl font-bold">0</p>
                        </div>
                        <div class="bg-blue-400 bg-opacity-30 p-2 rounded-full">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M13 6a3 3 0 11-6 0 3 3 0 016 0zM18 8a2 2 0 11-4 0 2 2 0 014 0zM14 15a4 4 0 00-8 0v3h8v-3zM6 8a2 2 0 11-4 0 2 2 0 014 0zM16 18v-3a5.972 5.972 0 00-.75-2.906A3.005 3.005 0 0119 15v3h-3zM4.75 12.094A5.973 5.973 0 004 15v3H1v-3a3 3 0 013.75-2.906z"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="stat-card bg-gradient-to-r from-green-500 to-green-600 text-white p-4 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-green-100 text-sm font-medium">活跃用户</p>
                            <p id="activeUsersCount" class="text-2xl font-bold">0</p>
                        </div>
                        <div class="bg-green-400 bg-opacity-30 p-2 rounded-full">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M10 12a2 2 0 100-4 2 2 0 000 4z"/>
                                <path fill-rule="evenodd" d="M.458 10C1.732 5.943 5.522 3 10 3s8.268 2.943 9.542 7c-1.274 4.057-5.064 7-9.542 7S1.732 14.057.458 10zM14 10a4 4 0 11-8 0 4 4 0 018 0z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="stat-card bg-gradient-to-r from-purple-500 to-purple-600 text-white p-4 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-purple-100 text-sm font-medium">管理员</p>
                            <p id="adminUsersCount" class="text-2xl font-bold">0</p>
                        </div>
                        <div class="bg-purple-400 bg-opacity-30 p-2 rounded-full">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z"/>
                                <path fill-rule="evenodd" d="M4 5a2 2 0 012-2h8a2 2 0 012 2v6a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 1a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                </div>
                <div class="stat-card bg-gradient-to-r from-orange-500 to-orange-600 text-white p-4 rounded-lg shadow-lg">
                    <div class="flex items-center justify-between">
                        <div>
                            <p class="text-orange-100 text-sm font-medium">部门数量</p>
                            <p id="departmentsCount" class="text-2xl font-bold">0</p>
                        </div>
                        <div class="bg-orange-400 bg-opacity-30 p-2 rounded-full">
                            <svg class="w-6 h-6" fill="currentColor" viewBox="0 0 20 20">
                                <path fill-rule="evenodd" d="M4 4a2 2 0 00-2 2v8a2 2 0 002 2h12a2 2 0 002-2V6a2 2 0 00-2-2H4zm3 2a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clip-rule="evenodd"/>
                            </svg>
                        </div>
                    </div>
                </div>
            </div>
        `;

        statsContainer.innerHTML = statsHTML;
    }

    /**
     * 处理搜索功能 - 实时搜索
     */
    handleSearch(searchTerm) {
        // 清除之前的搜索超时
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 延迟搜索，避免频繁请求
        this.searchTimeout = setTimeout(async () => {
            this.filters.search = searchTerm.toLowerCase();

            // 显示搜索加载状态
            const searchInput = document.getElementById('userSearchInput');
            if (searchInput) {
                this.showFilterLoading(searchInput);
                await this.applyFilters();
                this.hideFilterLoading(searchInput);
            } else {
                await this.applyFilters();
            }
        }, 300);
    }

    /**
     * 应用筛选条件 - 使用服务器端筛选
     */
    async applyFilters() {
        // 重置到第一页
        this.currentPage = 1;

        // 重新加载数据（服务器端会应用筛选条件）
        // 使用后台加载，不显示全屏遮罩
        await this.loadUsers(false);
    }

    /**
     * 重置所有筛选条件
     */
    async resetFilters() {
        this.filters = {
            search: '',
            role: '',
            department: '',
            status: 'all'
        };

        // 重置UI控件
        const searchInput = document.getElementById('userSearchInput');
        const roleFilter = document.getElementById('userRoleFilter');
        const departmentFilter = document.getElementById('userDepartmentFilter');
        const statusFilter = document.getElementById('userStatusFilter');

        if (searchInput) searchInput.value = '';
        if (roleFilter) roleFilter.value = '';
        if (departmentFilter) departmentFilter.value = '';
        if (statusFilter) statusFilter.value = 'all';

        // 显示重置加载状态
        const resetBtn = document.getElementById('resetFiltersBtn');
        if (resetBtn) {
            this.showFilterLoading(resetBtn);
            await this.applyFilters();
            this.hideFilterLoading(resetBtn);
        } else {
            await this.applyFilters();
        }
    }

    /**
     * 加载用户数据 - 使用新的API接口
     */
    async loadUsers(showLoading = true) {
        if (this.currentRole !== 'admin') {
            console.warn('权限不足，无法加载用户数据');
            return;
        }

        try {
            if (showLoading) {
                this.showLoadingState();
            }

            // 构建查询参数
            const params = new URLSearchParams({
                username: this.currentUser,
                page: 1,
                limit: 1000 // 获取所有用户
            });

            // 添加筛选参数
            if (this.filters.search) params.append('search', this.filters.search);
            if (this.filters.role) params.append('role', this.filters.role);
            if (this.filters.department) params.append('department', this.filters.department);
            if (this.filters.status !== 'all') params.append('status', this.filters.status);

            const response = await apiRequest(`/users?${params.toString()}`);

            if (response.success || Array.isArray(response)) {
                // 处理不同的响应格式
                this.users = response.data || response || [];
                this.filteredUsers = [...this.users]; // 服务器端已经筛选过了

                // 同步到全局 allUsers 变量（用于签名等功能）
                if (typeof window !== 'undefined' && window.allUsers !== undefined) {
                    window.allUsers = [...this.users];
                }

                // 应用当前排序（如果有的话）
                if (this.sortField) {
                    this.applySorting();
                } else {
                    this.updatePagination();
                    this.renderUserTable();
                }

                // 更新统计数据
                if (response.stats) {
                    this.updateStatsFromServer(response.stats);
                } else {
                    // 如果服务器没有返回统计数据，使用本地计算
                    this.updateStats();
                }

                // 更新排序图标状态
                this.updateSortIcons();

                // 只有在显式要求显示加载状态时才显示成功消息
                // 这样可以避免筛选器切换时的重复提示
                if (showLoading) {
                    this.showSuccessMessage('用户数据加载成功', false);
                }
            } else {
                throw new Error(response.message || '获取用户数据失败');
            }
        } catch (error) {
            console.error('加载用户数据失败:', error);
            this.showErrorMessage('加载用户数据失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 使用服务器返回的统计数据更新UI
     */
    updateStatsFromServer(stats) {
        document.getElementById('totalUsersCount').textContent = stats.totalUsers || 0;
        document.getElementById('activeUsersCount').textContent = stats.activeUsers || 0;
        document.getElementById('adminUsersCount').textContent = stats.adminUsers || 0;
        document.getElementById('departmentsCount').textContent = stats.departments || 0;
    }

    /**
     * 渲染用户表格
     */
    renderUserTable() {
        const tbody = document.getElementById('usersList');
        if (!tbody) return;

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

        if (pageUsers.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="7" class="px-6 py-8 text-center text-gray-500">
                        <div class="flex flex-col items-center">
                            <svg class="w-12 h-12 text-gray-300 mb-2" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 515.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z"/>
                            </svg>
                            <p class="text-lg font-medium">暂无用户数据</p>
                            <p class="text-sm">请尝试调整筛选条件或添加新用户</p>
                        </div>
                    </td>
                </tr>
            `;
            return;
        }

        tbody.innerHTML = pageUsers.map(user => this.renderUserRow(user)).join('');
        this.updateSelectAllCheckbox();

        // 绑定复选框事件
        this.bindCheckboxEvents();
    }

    /**
     * 渲染单个用户行
     */
    renderUserRow(user) {
        const isSelected = this.selectedUsers.has(user.username);
        const canDelete = user.username !== this.currentUser;

        return `
            <tr class="hover:bg-gray-50 transition-colors ${isSelected ? 'bg-blue-50' : ''}">
                <td class="p-3 text-center border-b border-gray-200">
                    <input type="checkbox"
                           class="user-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                           data-username="${user.username}"
                           ${isSelected ? 'checked' : ''}>
                </td>
                <td class="p-3 text-center border-b border-gray-200">
                    <div class="flex flex-col items-center">
                        <div class="font-medium text-gray-900">${user.username}</div>
                        <div class="text-xs text-gray-500 font-mono">ID: ${user.userId || 'N/A'}</div>
                    </div>
                </td>
                <td class="p-3 text-center border-b border-gray-200 text-gray-700">${user.userCode || '-'}</td>
                <td class="p-3 text-center border-b border-gray-200">
                    <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium ${this.getRoleBadgeClass(user.role)}">
                        ${this.getRoleDisplayName(user.role)}
                    </span>
                </td>
                <td class="p-3 text-center border-b border-gray-200 text-gray-700">${user.department || '-'}</td>
                <td class="p-3 text-center border-b border-gray-200">
                    ${user.signature ?
                        `<img src="${user.signature}" alt="电子签名" class="max-h-8 max-w-16 object-contain mx-auto">` :
                        '<span class="text-gray-400 text-sm">未设置</span>'
                    }
                </td>
                <td class="p-3 text-center border-b border-gray-200">
                    <div class="flex items-center justify-center space-x-2">
                        <button onclick="userManager.showEditModal('${user.username}')"
                                class="text-blue-600 hover:text-blue-900 p-1 rounded transition-colors"
                                title="编辑用户">
                            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                            </svg>
                        </button>
                        ${canDelete ? `
                            <button onclick="userManager.deleteUser('${user.username}')"
                                    class="text-red-600 hover:text-red-900 p-1 rounded transition-colors"
                                    title="删除用户">
                                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                                </svg>
                            </button>
                        ` : '<span class="text-gray-400 text-sm">当前用户</span>'}
                    </div>
                </td>
            </tr>
        `;
    }



    /**
     * 获取角色徽章样式类
     */
    getRoleBadgeClass(role) {
        const roleClasses = {
            'admin': 'bg-red-100 text-red-800',
            'ceo': 'bg-purple-100 text-purple-800',
            'manager': 'bg-blue-100 text-blue-800',
            'chief': 'bg-indigo-100 text-indigo-800',
            'director': 'bg-green-100 text-green-800',
            'user': 'bg-gray-100 text-gray-800',
            'mechanical': 'bg-yellow-100 text-yellow-800',
            'readonly': 'bg-orange-100 text-orange-800'
        };
        return roleClasses[role] || 'bg-gray-100 text-gray-800';
    }

    /**
     * 获取角色显示名称
     */
    getRoleDisplayName(role) {
        const roleNames = {
            'admin': '管理员',
            'ceo': 'CEO',
            'manager': '经理',
            'chief': '总监',
            'director': '厂长',
            'user': '普通用户',
            'mechanical': '机电部用户',
            'readonly': '只读用户'
        };
        return roleNames[role] || role;
    }

    /**
     * 绑定复选框事件
     */
    bindCheckboxEvents() {
        const checkboxes = document.querySelectorAll('.user-checkbox[data-username]');
        checkboxes.forEach(checkbox => {
            checkbox.addEventListener('change', (e) => {
                const username = e.target.dataset.username;
                if (username) { // 确保username存在
                    if (e.target.checked) {
                        this.selectedUsers.add(username);
                    } else {
                        this.selectedUsers.delete(username);
                    }
                    this.updateBatchOperationBar();
                    this.updateSelectAllCheckbox();
                }
            });
        });
    }

    /**
     * 处理全选/取消全选
     */
    handleSelectAll(checked) {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

        pageUsers.forEach(user => {
            if (checked) {
                this.selectedUsers.add(user.username);
            } else {
                this.selectedUsers.delete(user.username);
            }
        });

        this.renderUserTable();
        this.updateBatchOperationBar();
    }

    /**
     * 更新全选复选框状态
     */
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAllUsers');
        if (!selectAllCheckbox) return;

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageUsers = this.filteredUsers.slice(startIndex, endIndex);

        const selectedPageUsers = pageUsers.filter(user => this.selectedUsers.has(user.username));

        if (selectedPageUsers.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedPageUsers.length === pageUsers.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    /**
     * 更新批量操作栏
     */
    updateBatchOperationBar() {
        const batchBar = document.getElementById('batchOperationBar');
        const selectedCount = document.getElementById('selectedCount');

        if (!batchBar || !selectedCount) return;

        // 清理无效的选择项（如undefined或空字符串）
        this.selectedUsers.forEach(username => {
            if (!username || username.trim() === '') {
                this.selectedUsers.delete(username);
            }
        });

        if (this.selectedUsers.size > 0) {
            batchBar.classList.remove('hidden');
            selectedCount.textContent = this.selectedUsers.size;
        } else {
            batchBar.classList.add('hidden');
        }
    }

    /**
     * 取消批量选择
     */
    cancelBatchSelection() {
        this.selectedUsers.clear();
        this.renderUserTable();
        this.updateBatchOperationBar();
    }

    /**
     * 更新分页
     */
    updatePagination() {
        this.totalPages = Math.ceil(this.filteredUsers.length / this.pageSize);

        const paginationContainer = document.getElementById('usersPagination');
        if (!paginationContainer) return;

        const totalItems = this.filteredUsers.length;

        paginationContainer.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    共 <span id="userTotalItems">${totalItems}</span> 个用户，每页 ${this.pageSize} 个
                </div>
                <div class="flex space-x-2">
                    <button id="userPrevPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed" ${this.currentPage <= 1 ? 'disabled' : ''}>上一页</button>
                    <div id="userPageNumbers" class="flex space-x-1"></div>
                    <button id="userNextPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed" ${this.currentPage >= this.totalPages ? 'disabled' : ''}>下一页</button>
                </div>
            </div>
        `;

        // 设置按钮点击事件
        document.getElementById('userPrevPage').onclick = () => this.goToPage(this.currentPage - 1);
        document.getElementById('userNextPage').onclick = () => this.goToPage(this.currentPage + 1);

        // 生成页码按钮
        this.generatePageNumbers();
    }

    /**
     * 生成页码按钮
     */
    generatePageNumbers() {
        const pageNumbersContainer = document.getElementById('userPageNumbers');
        if (!pageNumbersContainer) return;

        pageNumbersContainer.innerHTML = '';

        // 确定要显示的页码范围
        let startPage = Math.max(1, this.currentPage - 2);
        let endPage = Math.min(this.totalPages, startPage + 4);

        // 调整起始页，确保始终显示5个页码（如果有足够的页数）
        if (endPage - startPage < 4 && this.totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        // 添加第一页按钮（如果不在显示范围内）
        if (startPage > 1) {
            const firstPageBtn = document.createElement('button');
            firstPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            firstPageBtn.textContent = '1';
            firstPageBtn.onclick = () => this.goToPage(1);
            pageNumbersContainer.appendChild(firstPageBtn);

            // 添加省略号（如果第一页和起始页之间有间隔）
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }
        }

        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `px-3 py-1 border rounded-md ${i === this.currentPage ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbersContainer.appendChild(pageBtn);
        }

        // 添加最后一页按钮（如果不在显示范围内）
        if (endPage < this.totalPages) {
            // 添加省略号（如果结束页和最后一页之间有间隔）
            if (endPage < this.totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }

            const lastPageBtn = document.createElement('button');
            lastPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            lastPageBtn.textContent = this.totalPages;
            lastPageBtn.onclick = () => this.goToPage(this.totalPages);
            pageNumbersContainer.appendChild(lastPageBtn);
        }
    }

    /**
     * 跳转到指定页面
     */
    goToPage(page) {
        if (page < 1 || page > this.totalPages) return;

        this.currentPage = page;
        this.renderUserTable();
        this.updatePagination();
    }

    /**
     * 更新统计数据
     */
    updateStats() {
        const totalCount = this.users.length;
        const activeCount = this.users.filter(user => user.email && user.email.trim() !== '').length;
        const adminCount = this.users.filter(user => user.role === 'admin').length;
        const departments = [...new Set(this.users.map(user => user.department).filter(Boolean))];

        document.getElementById('totalUsersCount').textContent = totalCount;
        document.getElementById('activeUsersCount').textContent = activeCount;
        document.getElementById('adminUsersCount').textContent = adminCount;
        document.getElementById('departmentsCount').textContent = departments.length;
    }

    /**
     * 显示添加用户模态框
     */
    showAddModal() {
        const modal = document.getElementById('addUserModal');
        if (!modal) return;

        // 重置表单
        const form = document.getElementById('addUserForm');
        if (form) form.reset();

        // 重置签名预览
        const signaturePreview = document.getElementById('signaturePreview');
        if (signaturePreview) signaturePreview.classList.add('hidden');

        // 处理部门字段显示/隐藏
        this.toggleDepartmentField('add');

        modal.classList.remove('hidden');
    }

    /**
     * 关闭添加用户模态框
     */
    closeAddModal() {
        const modal = document.getElementById('addUserModal');
        if (modal) modal.classList.add('hidden');
    }

    /**
     * 显示编辑用户模态框
     */
    showEditModal(username) {
        const user = this.users.find(u => u.username === username);
        if (!user) return;

        const modal = document.getElementById('editUserModal');
        if (!modal) return;

        // 重置删除签名标志
        this.pendingSignatureDeletion = false;

        // 填充表单数据
        document.getElementById('editTargetUsername').value = username;
        document.getElementById('editUserCode').value = user.userCode || '';
        document.getElementById('editRole').value = user.role || 'user';
        document.getElementById('editUserDepartment').value = user.department || '生产部';
        document.getElementById('editEmail').value = user.email || '';
        document.getElementById('editPassword').value = '';

        // 处理部门字段显示/隐藏
        this.toggleDepartmentField('edit');

        // 处理签名预览
        const signaturePreview = document.getElementById('editSignaturePreview');
        const signatureImage = document.getElementById('editSignatureImage');
        if (user.signature && signaturePreview && signatureImage) {
            signatureImage.src = user.signature;
            signaturePreview.classList.remove('hidden');
        } else if (signaturePreview) {
            signaturePreview.classList.add('hidden');
        }

        modal.classList.remove('hidden');
    }

    /**
     * 关闭编辑用户模态框
     */
    closeEditModal() {
        const modal = document.getElementById('editUserModal');
        if (modal) modal.classList.add('hidden');
        // 重置删除签名标志
        this.pendingSignatureDeletion = false;
    }

    /**
     * 删除用户电子签名
     */
    deleteUserSignature() {
        if (!confirm('确定要删除该用户的电子签名吗？')) {
            return;
        }

        // 设置删除标志
        this.pendingSignatureDeletion = true;

        // 隐藏签名预览
        const signaturePreview = document.getElementById('editSignaturePreview');
        if (signaturePreview) {
            signaturePreview.classList.add('hidden');
        }

        // 清空文件输入
        const signatureInput = document.getElementById('editSignature');
        if (signatureInput) {
            signatureInput.value = '';
        }

        this.showSuccessMessage('电子签名将在保存时删除', false);
    }



    /**
     * 切换部门字段显示/隐藏
     */
    toggleDepartmentField(type) {
        const roleSelect = document.getElementById(type === 'add' ? 'newUserRole' : 'editRole');
        const departmentField = document.getElementById(type === 'add' ? 'newUserDepartmentField' : 'editDepartmentField');

        if (!roleSelect || !departmentField) return;

        const role = roleSelect.value;
        if (role === 'chief' || role === 'manager' || role === 'admin' || role === 'ceo') {
            departmentField.classList.add('hidden');
        } else {
            departmentField.classList.remove('hidden');
        }
    }

    /**
     * 处理添加用户
     */
    async handleAddUser(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const userData = {
            adminUsername: this.currentUser,
            username: formData.get('username'),
            password: formData.get('password'),
            role: formData.get('role'),
            email: formData.get('email'),
            department: formData.get('department'),
            userCode: formData.get('userCode')
        };

        // 处理电子签名
        const signatureFile = formData.get('signature');
        if (signatureFile && signatureFile.size > 0) {
            try {
                userData.signature = await this.readFileAsDataURL(signatureFile);
            } catch (error) {
                this.showErrorMessage('读取电子签名文件失败');
                return;
            }
        }

        try {
            this.showLoadingState('正在添加用户...');

            const response = await apiRequest('/addUser', {
                method: 'POST',
                data: userData
            });

            if (response.success) {
                this.showSuccessMessage('用户添加成功');
                this.closeAddModal();
                await this.loadUsers(false);
                // 同步到全局变量
                if (typeof window !== 'undefined' && window.allUsers !== undefined) {
                    window.allUsers = [...this.users];
                }
            } else {
                this.showErrorMessage('用户添加失败: ' + response.message);
            }
        } catch (error) {
            console.error('添加用户失败:', error);
            this.showErrorMessage('添加用户失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 处理编辑用户
     */
    async handleEditUser(e) {
        e.preventDefault();

        const formData = new FormData(e.target);
        const userData = {
            adminUsername: this.currentUser,
            targetUsername: formData.get('targetUsername'),
            newRole: formData.get('role'),
            email: formData.get('email'),
            department: formData.get('department'),
            userCode: formData.get('userCode'),
            newPassword: formData.get('password')
        };

        // 处理电子签名
        const signatureFile = formData.get('signature');
        if (signatureFile && signatureFile.size > 0) {
            try {
                userData.signature = await this.readFileAsDataURL(signatureFile);
            } catch (error) {
                this.showErrorMessage('读取电子签名文件失败');
                return;
            }
        }

        // 检查是否需要删除签名
        if (this.pendingSignatureDeletion) {
            userData.deleteSignature = true;
            this.pendingSignatureDeletion = false;
            console.log('发送删除签名请求');
        }

        try {
            this.showLoadingState('正在更新用户...');

            console.log('发送更新用户请求:', userData);

            const response = await apiRequest('/updateUser', {
                method: 'POST',
                data: userData
            });

            console.log('更新用户响应:', response);

            if (response.success) {
                this.showSuccessMessage('用户更新成功');
                this.closeEditModal();
                console.log('重新加载用户数据...');
                await this.loadUsers(false);
                console.log('用户数据重新加载完成，当前用户数量:', this.users.length);
                // 同步到全局变量
                if (typeof window !== 'undefined' && window.allUsers !== undefined) {
                    window.allUsers = [...this.users];
                }
            } else {
                this.showErrorMessage('用户更新失败: ' + response.message);
            }
        } catch (error) {
            console.error('更新用户失败:', error);
            this.showErrorMessage('更新用户失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 删除用户
     */
    async deleteUser(username) {
        if (!confirm(`确定要删除用户 ${username} 吗？此操作不可恢复！`)) {
            return;
        }

        try {
            this.showLoadingState('正在删除用户...');

            const response = await apiRequest('/deleteUser', {
                method: 'POST',
                data: {
                    adminUsername: this.currentUser,
                    targetUsername: username
                }
            });

            if (response.success) {
                this.showSuccessMessage('用户删除成功');
                // 从选中列表中移除
                this.selectedUsers.delete(username);
                await this.loadUsers(false);
                // 同步到全局变量
                if (typeof window !== 'undefined' && window.allUsers !== undefined) {
                    window.allUsers = [...this.users];
                }
            } else {
                this.showErrorMessage('用户删除失败: ' + response.message);
            }
        } catch (error) {
            console.error('删除用户失败:', error);
            this.showErrorMessage('删除用户失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 批量删除用户
     */
    async handleBatchDelete() {
        if (this.selectedUsers.size === 0) {
            this.showErrorMessage('请先选择要删除的用户');
            return;
        }

        const usernames = Array.from(this.selectedUsers);
        if (!confirm(`确定要删除选中的 ${usernames.length} 个用户吗？此操作不可恢复！`)) {
            return;
        }

        try {
            this.showLoadingState('正在批量删除用户...');

            const promises = usernames.map(username =>
                apiRequest('/deleteUser', {
                    method: 'POST',
                    data: {
                        adminUsername: this.currentUser,
                        targetUsername: username
                    }
                })
            );

            const results = await Promise.allSettled(promises);
            const successCount = results.filter(result => result.status === 'fulfilled' && result.value.success).length;
            const failCount = results.length - successCount;

            if (successCount > 0) {
                this.showSuccessMessage(`成功删除 ${successCount} 个用户${failCount > 0 ? `，${failCount} 个失败` : ''}`);
                this.selectedUsers.clear();
                await this.loadUsers(false);
                // 同步到全局变量
                if (typeof window !== 'undefined' && window.allUsers !== undefined) {
                    window.allUsers = [...this.users];
                }
            } else {
                this.showErrorMessage('批量删除失败');
            }
        } catch (error) {
            console.error('批量删除用户失败:', error);
            this.showErrorMessage('批量删除用户失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 批量导出用户
     */
    async handleBatchExport() {
        if (this.selectedUsers.size === 0) {
            this.showErrorMessage('请先选择要导出的用户');
            return;
        }

        try {
            this.showLoadingState('正在导出用户数据...');

            const selectedUserData = this.users.filter(user => this.selectedUsers.has(user.username));
            await this.exportUsersToCSV(selectedUserData, `选中用户_${new Date().toISOString().slice(0, 10)}.csv`);

            this.showSuccessMessage('用户数据导出成功');
        } catch (error) {
            console.error('导出用户数据失败:', error);
            this.showErrorMessage('导出用户数据失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 导出所有用户
     */
    async exportAllUsers() {
        try {
            this.showLoadingState('正在导出用户数据...');

            const response = await fetch(`/exportUsers?username=${this.currentUser}`);
            if (!response.ok) {
                throw new Error('导出请求失败');
            }

            const blob = await response.blob();
            const url = window.URL.createObjectURL(blob);
            const a = document.createElement('a');
            a.href = url;
            a.download = `用户列表_${new Date().toISOString().slice(0, 10)}.csv`;
            document.body.appendChild(a);
            a.click();
            window.URL.revokeObjectURL(url);
            document.body.removeChild(a);

            this.showSuccessMessage('用户数据导出成功');
        } catch (error) {
            console.error('导出用户数据失败:', error);
            this.showErrorMessage('导出用户数据失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 导入用户数据
     */
    async importUsers(file) {
        if (!file) {
            this.showErrorMessage('请选择要导入的CSV文件');
            return;
        }

        try {
            this.showLoadingState('正在导入用户数据...');

            const formData = new FormData();
            formData.append('file', file);

            const response = await fetch(`/importUsers?username=${this.currentUser}`, {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showSuccessMessage(result.message);
                await this.loadUsers(false);
                // 同步到全局变量
                if (typeof window !== 'undefined' && window.allUsers !== undefined) {
                    window.allUsers = [...this.users];
                }
            } else {
                this.showErrorMessage('导入失败: ' + result.message);
            }
        } catch (error) {
            console.error('导入用户数据失败:', error);
            this.showErrorMessage('导入用户数据失败: ' + error.message);
        } finally {
            this.hideLoadingState();
        }
    }

    /**
     * 工具函数：读取文件为DataURL
     */
    readFileAsDataURL(file) {
        return new Promise((resolve, reject) => {
            const reader = new FileReader();
            reader.onload = () => resolve(reader.result);
            reader.onerror = () => reject(new Error('文件读取失败'));
            reader.readAsDataURL(file);
        });
    }

    /**
     * 工具函数：导出用户数据为CSV
     */
    async exportUsersToCSV(users, filename) {
        const headers = ['用户名', '用户ID', '用户代码', '角色', '部门', '邮箱'];
        const csvContent = [
            headers.join(','),
            ...users.map(user => [
                user.username,
                user.userId || 'N/A',
                user.userCode || '',
                this.getRoleDisplayName(user.role),
                user.department || '',
                user.email || ''
            ].join(','))
        ].join('\n');

        const blob = new Blob(['\uFEFF' + csvContent], { type: 'text/csv;charset=utf-8;' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    }

    /**
     * 更新UI权限
     */
    updateUIPermissions() {
        const isAdmin = this.currentRole === 'admin';

        // 隐藏/显示管理员功能
        const adminElements = document.querySelectorAll('.admin-only');
        adminElements.forEach(element => {
            if (isAdmin) {
                element.classList.remove('hidden');
            } else {
                element.classList.add('hidden');
            }
        });
    }

    /**
     * 显示加载状态
     */
    showLoadingState(message = '正在加载...') {
        if (typeof showLoading === 'function') {
            showLoading(message);
        }
    }

    /**
     * 隐藏加载状态
     */
    hideLoadingState() {
        if (typeof hideLoading === 'function') {
            hideLoading();
        }
    }

    /**
     * 显示成功消息
     */
    showSuccessMessage(message, autoHide = true) {
        if (typeof showSuccess === 'function') {
            showSuccess(message);
        } else {
            console.log('成功:', message);
        }
    }

    /**
     * 显示错误消息
     */
    showErrorMessage(message) {
        if (typeof showError === 'function') {
            showError(message);
        } else {
            console.error('错误:', message);
            alert(message);
        }
    }

    /**
     * 刷新用户数据（页面切换时使用，静默刷新）
     */
    async refresh() {
        // 页面切换时的刷新不显示成功消息，避免重复提示
        await this.loadUsers(false);
    }

    /**
     * 手动刷新用户数据（用户主动操作时使用，显示成功消息）
     */
    async manualRefresh() {
        await this.loadUsers(true);
    }

    /**
     * 表格排序功能
     */
    sortTable(field) {
        // 如果点击的是同一个字段，切换排序方向
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果是新字段，默认升序
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        // 更新排序图标
        this.updateSortIcons();

        // 执行排序
        this.applySorting();
    }

    /**
     * 应用排序到用户数据
     */
    applySorting() {
        if (!this.sortField) return;

        this.filteredUsers.sort((a, b) => {
            let aValue = a[this.sortField] || '';
            let bValue = b[this.sortField] || '';

            // 特殊处理角色字段 - 按角色权重排序
            if (this.sortField === 'role') {
                const roleOrder = {
                    'admin': 1,
                    'ceo': 2,
                    'manager': 3,
                    'chief': 4,
                    'director': 5,
                    'user': 6,
                    'mechanical': 7,
                    'readonly': 8
                };
                aValue = roleOrder[aValue] || 999;
                bValue = roleOrder[bValue] || 999;
            } else {
                // 字符串比较，忽略大小写
                if (typeof aValue === 'string' && typeof bValue === 'string') {
                    aValue = aValue.toLowerCase();
                    bValue = bValue.toLowerCase();
                }
            }

            let result = 0;
            if (aValue < bValue) result = -1;
            else if (aValue > bValue) result = 1;

            return this.sortDirection === 'desc' ? -result : result;
        });

        // 重新渲染表格
        this.currentPage = 1; // 排序后回到第一页
        this.renderUserTable();
        this.updatePagination();
    }

    /**
     * 更新排序图标 - 与设备管理保持一致的样式
     */
    updateSortIcons() {
        // 重置所有排序图标
        document.querySelectorAll('.sortable svg').forEach(icon => {
            // 使用 setAttribute 来设置 SVG 元素的 class
            icon.setAttribute('class', 'w-4 h-4 inline ml-1 text-gray-400');
            icon.innerHTML = '<path d="M5 12l5-5 5 5H5z"/>';
        });

        // 设置当前排序字段的图标
        if (this.sortField) {
            const activeHeader = document.querySelector(`[data-sort="${this.sortField}"] svg`);
            if (activeHeader) {
                // 使用 setAttribute 来设置 SVG 元素的 class
                activeHeader.setAttribute('class', 'w-4 h-4 inline ml-1 text-blue-600');
                if (this.sortDirection === 'desc') {
                    activeHeader.innerHTML = '<path d="M15 8l-5 5-5-5h10z"/>';
                } else {
                    activeHeader.innerHTML = '<path d="M5 12l5-5 5 5H5z"/>';
                }
            }
        }
    }

    /**
     * 获取当前筛选的用户数据
     */
    getFilteredUsers() {
        return this.filteredUsers;
    }

    /**
     * 获取选中的用户数据
     */
    getSelectedUsers() {
        return this.users.filter(user => this.selectedUsers.has(user.username));
    }

    /**
     * 显示部门管理模态框
     */
    showDepartmentModal() {
        const modal = document.getElementById('departmentModal');
        if (modal) {
            modal.classList.remove('hidden');

            // 初始化部门管理器
            if (!window.departmentManager) {
                window.departmentManager = initDepartmentManagement();
            }
            window.departmentManager.init();
        }
    }

    /**
     * 关闭部门管理模态框
     */
    closeDepartmentModal() {
        const modal = document.getElementById('departmentModal');
        if (modal) {
            modal.classList.add('hidden');

            // 隐藏所有表单
            this.hideAddDepartmentForm();
            this.hideEditDepartmentForm();
        }
    }

    /**
     * 显示添加部门表单
     */
    showAddDepartmentForm() {
        if (window.departmentManager) {
            window.departmentManager.showAddDepartmentForm();
        }
    }

    /**
     * 隐藏添加部门表单
     */
    hideAddDepartmentForm() {
        if (window.departmentManager) {
            window.departmentManager.hideAddDepartmentForm();
        }
    }

    /**
     * 隐藏编辑部门表单
     */
    hideEditDepartmentForm() {
        if (window.departmentManager) {
            window.departmentManager.hideEditDepartmentForm();
        }
    }

    /**
     * 编辑部门
     */
    editDepartment(departmentId) {
        if (window.departmentManager) {
            window.departmentManager.editDepartment(departmentId);
        }
    }

    /**
     * 删除部门
     */
    deleteDepartment(departmentId, departmentName) {
        if (window.departmentManager) {
            window.departmentManager.deleteDepartment(departmentId, departmentName);
        }
    }

    /**
     * 更新部门选项 - 动态加载部门数据到下拉选项
     */
    async updateDepartmentOptions() {
        try {
            const response = await apiRequest('/departments');
            if (response.success) {
                const departments = response.data || [];

                // 更新筛选器中的部门选项
                this.updateDepartmentFilterOptions(departments);

                // 更新添加用户表单中的部门选项
                this.updateAddUserDepartmentOptions(departments);

                // 更新编辑用户表单中的部门选项
                this.updateEditUserDepartmentOptions(departments);

                // 更新申请表单中的部门选项（如果存在）
                this.updateApplicationDepartmentOptions(departments);
            }
        } catch (error) {
            console.error('更新部门选项失败:', error);
        }
    }

    /**
     * 更新筛选器中的部门选项
     */
    updateDepartmentFilterOptions(departments) {
        const filterSelect = document.getElementById('userDepartmentFilter');
        if (filterSelect) {
            const currentValue = filterSelect.value;
            filterSelect.innerHTML = '<option value="">全部部门</option>';

            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.name;
                option.textContent = dept.name;
                filterSelect.appendChild(option);
            });

            // 恢复之前选择的值
            if (currentValue) {
                filterSelect.value = currentValue;
            }
        }
    }

    /**
     * 更新添加用户表单中的部门选项
     */
    updateAddUserDepartmentOptions(departments) {
        const departmentSelect = document.querySelector('#addUserModal select[name="department"]');
        if (departmentSelect) {
            const currentValue = departmentSelect.value;
            departmentSelect.innerHTML = '';

            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.name;
                option.textContent = dept.name;
                departmentSelect.appendChild(option);
            });

            // 设置默认值
            if (departments.length > 0) {
                departmentSelect.value = currentValue || departments[0].name;
            }
        }
    }

    /**
     * 更新编辑用户表单中的部门选项
     */
    updateEditUserDepartmentOptions(departments) {
        const departmentSelect = document.getElementById('editUserDepartment');
        if (departmentSelect) {
            const currentValue = departmentSelect.value;
            departmentSelect.innerHTML = '';

            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.name;
                option.textContent = dept.name;
                departmentSelect.appendChild(option);
            });

            // 恢复之前选择的值
            if (currentValue) {
                departmentSelect.value = currentValue;
            }
        }
    }

    /**
     * 更新申请表单中的部门选项
     */
    updateApplicationDepartmentOptions(departments) {
        const departmentSelect = document.getElementById('editDepartment');
        if (departmentSelect) {
            const currentValue = departmentSelect.value;
            departmentSelect.innerHTML = '';

            departments.forEach(dept => {
                const option = document.createElement('option');
                option.value = dept.name;
                option.textContent = dept.name;
                departmentSelect.appendChild(option);
            });

            // 恢复之前选择的值
            if (currentValue) {
                departmentSelect.value = currentValue;
            }
        }
    }
}

// 全局用户管理器实例
let userManager = null;

// 初始化用户管理模块
function initUserManagement() {
    if (!userManager) {
        userManager = new UserManager();
    }
    return userManager;
}

// 全局函数，供HTML调用
window.showAddUserModal = () => userManager?.showAddModal();
window.closeAddUserModal = () => userManager?.closeAddModal();
window.showEditUserModal = (username) => userManager?.showEditModal(username);
window.closeEditUserModal = () => userManager?.closeEditModal();
window.deleteUser = (username) => userManager?.deleteUser(username);
window.exportUsers = () => userManager?.exportAllUsers();
window.importUsers = (event) => {
    const file = event.target.files[0];
    if (file) {
        userManager?.importUsers(file);
        event.target.value = ''; // 重置文件输入
    }
};
window.searchUsers = () => {
    const searchInput = document.getElementById('userSearchInput');
    if (searchInput && userManager) {
        userManager.handleSearch(searchInput.value);
    }
};
window.resetUserSearch = () => userManager?.resetFilters();

// 部门管理相关的全局函数
window.showDepartmentModal = () => userManager?.showDepartmentModal();
window.closeDepartmentModal = () => userManager?.closeDepartmentModal();
window.showAddDepartmentForm = () => userManager?.showAddDepartmentForm();
window.hideAddDepartmentForm = () => userManager?.hideAddDepartmentForm();
window.hideEditDepartmentForm = () => userManager?.hideEditDepartmentForm();
window.editDepartment = (departmentId) => userManager?.editDepartment(departmentId);
window.deleteDepartment = (departmentId, departmentName) => userManager?.deleteDepartment(departmentId, departmentName);

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = { UserManager, initUserManagement };
}
