/**
 * 设备搜索组件
 * 提供设备搜索和选择功能
 */
class DeviceSearch {
    constructor(inputId, hiddenInputId, resultsId) {
        this.inputElement = document.getElementById(inputId);
        this.hiddenInputElement = document.getElementById(hiddenInputId);
        this.resultsElement = document.getElementById(resultsId);
        this.devices = [];
        this.filteredDevices = [];
        this.selectedIndex = -1;
        this.selectedDevice = null;
        this.searchTimeout = null;
        this.isLoading = false;
        
        this.init();
    }
    
    init() {
        if (!this.inputElement || !this.hiddenInputElement || !this.resultsElement) {
            console.error('设备搜索组件初始化失败：找不到必要的DOM元素');
            console.error('输入框ID: recordDeviceSearch, 找到:', !!this.inputElement);
            console.error('隐藏输入框ID: recordDevice, 找到:', !!this.hiddenInputElement);
            console.error('结果容器ID: deviceSearchResults, 找到:', !!this.resultsElement);
            return;
        }

        // 绑定事件
        this.inputElement.addEventListener('input', this.handleInput.bind(this));
        this.inputElement.addEventListener('focus', this.handleFocus.bind(this));
        this.inputElement.addEventListener('blur', this.handleBlur.bind(this));
        this.inputElement.addEventListener('keydown', this.handleKeydown.bind(this));

        // 点击外部关闭搜索结果
        document.addEventListener('click', this.handleDocumentClick.bind(this));

        // 加载设备数据
        this.loadDevices();
    }
    
    async loadDevices() {
        try {
            this.isLoading = true;
            const response = await fetch('/api/devices');
            if (response.ok) {
                this.devices = await response.json();
                console.log('设备数据加载成功:', this.devices.length, '个设备');
            } else {
                console.error('加载设备数据失败:', response.status);
                this.devices = [];
            }
        } catch (error) {
            console.error('加载设备数据出错:', error);
            this.devices = [];
        } finally {
            this.isLoading = false;
        }
    }
    
    handleInput(event) {
        const query = event.target.value.trim();

        // 清除之前的搜索超时
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 如果输入为空，清除选择
        if (!query) {
            this.clearSelection();
            this.hideResults();
            return;
        }

        // 延迟搜索，避免频繁请求
        this.searchTimeout = setTimeout(() => {
            this.searchDevices(query);
        }, 300);
    }
    
    handleFocus() {
        const query = this.inputElement.value.trim();
        if (query) {
            this.searchDevices(query);
        }
    }
    
    handleBlur(event) {
        // 延迟隐藏结果，允许点击搜索结果
        setTimeout(() => {
            this.hideResults();
        }, 200);
    }
    
    handleKeydown(event) {
        if (!this.resultsElement.classList.contains('hidden')) {
            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    this.selectNext();
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    this.selectPrevious();
                    break;
                case 'Enter':
                    event.preventDefault();
                    this.selectCurrent();
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.hideResults();
                    break;
            }
        }
    }
    
    handleDocumentClick(event) {
        if (!this.inputElement.contains(event.target) && 
            !this.resultsElement.contains(event.target)) {
            this.hideResults();
        }
    }
    
    searchDevices(query) {
        if (this.isLoading) {
            this.showLoading();
            return;
        }

        const lowerQuery = query.toLowerCase();
        this.filteredDevices = this.devices.filter(device => {
            const matchCode = device.deviceCode && device.deviceCode.toLowerCase().includes(lowerQuery);
            const matchName = device.deviceName && device.deviceName.toLowerCase().includes(lowerQuery);
            const matchLocation = device.location && device.location.toLowerCase().includes(lowerQuery);
            return matchCode || matchName || matchLocation;
        });

        this.selectedIndex = -1;
        this.renderResults();
        this.showResults();
    }
    
    renderResults() {
        if (this.filteredDevices.length === 0) {
            this.resultsElement.innerHTML = `
                <div class="device-search-no-results">
                    未找到匹配的设备
                </div>
            `;
            return;
        }
        
        const html = this.filteredDevices.map((device, index) => `
            <div class="device-search-item ${index === this.selectedIndex ? 'selected' : ''}"
                 data-index="${index}">
                <div class="device-search-item-title">
                    ${device.deviceCode} - ${device.deviceName}
                </div>
                <div class="device-search-item-subtitle">
                    位置: ${device.location} | 负责人: ${device.responsible} | 状态: ${device.status}
                </div>
            </div>
        `).join('');
        
        this.resultsElement.innerHTML = html;

        // 为搜索结果项添加点击事件
        const items = this.resultsElement.querySelectorAll('.device-search-item');
        items.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.selectDevice(index);
            });
        });
    }
    
    showLoading() {
        this.resultsElement.innerHTML = `
            <div class="device-search-loading">
                正在加载设备数据...
            </div>
        `;
        this.showResults();
    }
    
    showResults() {
        this.resultsElement.classList.remove('hidden');
    }
    
    hideResults() {
        this.resultsElement.classList.add('hidden');
    }
    
    selectNext() {
        if (this.selectedIndex < this.filteredDevices.length - 1) {
            this.selectedIndex++;
            this.updateSelection();
        }
    }
    
    selectPrevious() {
        if (this.selectedIndex > 0) {
            this.selectedIndex--;
            this.updateSelection();
        }
    }
    
    selectCurrent() {
        if (this.selectedIndex >= 0 && this.selectedIndex < this.filteredDevices.length) {
            this.selectDevice(this.selectedIndex);
        }
    }
    
    selectDevice(index) {
        if (index >= 0 && index < this.filteredDevices.length) {
            const device = this.filteredDevices[index];
            this.selectedDevice = device;
            
            // 更新输入框显示
            this.inputElement.value = `${device.deviceCode} - ${device.deviceName}`;
            
            // 更新隐藏字段
            this.hiddenInputElement.value = device.id;
            
            // 隐藏搜索结果
            this.hideResults();
            
            console.log('选择设备:', device);
        }
    }
    
    updateSelection() {
        const items = this.resultsElement.querySelectorAll('.device-search-item');
        items.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.classList.add('selected');
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.classList.remove('selected');
            }
        });
    }
    
    clearSelection() {
        this.selectedDevice = null;
        this.hiddenInputElement.value = '';
        this.selectedIndex = -1;
    }
    
    // 获取当前选择的设备
    getSelectedDevice() {
        return this.selectedDevice;
    }
    
    // 设置选择的设备（用于编辑模式）
    setSelectedDevice(device) {
        if (device) {
            this.selectedDevice = device;
            this.inputElement.value = `${device.deviceCode} - ${device.deviceName}`;
            this.hiddenInputElement.value = device.id;
        } else {
            this.clearSelection();
            this.inputElement.value = '';
        }
    }
    
    // 重新加载设备数据
    async refresh() {
        await this.loadDevices();
    }

    // 设置只读状态
    setReadonly(readonly) {
        if (this.inputElement) {
            this.inputElement.disabled = readonly;
            if (readonly) {
                this.hideResults();
            }
        }
    }

    // 检查是否有效
    isValid() {
        return this.selectedDevice && this.hiddenInputElement.value;
    }
}

/**
 * 维修保养记录专用设备搜索组件
 * 支持按厂区过滤设备
 */
class MaintenanceDeviceSearch {
    constructor(inputId, hiddenInputId, resultsId) {
        this.inputElement = document.getElementById(inputId);
        this.hiddenInputElement = document.getElementById(hiddenInputId);
        this.resultsElement = document.getElementById(resultsId);
        this.devices = [];
        this.filteredDevices = [];
        this.selectedIndex = -1;
        this.selectedDevice = null;
        this.searchTimeout = null;
        this.isLoading = false;
        this.factoryFilter = null; // 厂区过滤

        this.init();
    }

    init() {
        if (!this.inputElement || !this.hiddenInputElement || !this.resultsElement) {
            console.error('维修保养设备搜索组件初始化失败：找不到必要的DOM元素');
            return;
        }

        // 绑定事件
        this.inputElement.addEventListener('input', this.handleInput.bind(this));
        this.inputElement.addEventListener('focus', this.handleFocus.bind(this));
        this.inputElement.addEventListener('blur', this.handleBlur.bind(this));
        this.inputElement.addEventListener('keydown', this.handleKeydown.bind(this));

        // 点击外部关闭搜索结果
        document.addEventListener('click', this.handleDocumentClick.bind(this));

        // 加载设备数据
        this.loadDevices();
    }

    async loadDevices() {
        try {
            this.isLoading = true;
            const response = await apiRequest('/api/devices');
            if (response.success) {
                this.devices = response.devices || [];
                console.log('维修保养设备数据加载成功:', this.devices.length, '个设备');
            } else {
                console.error('加载设备数据失败:', response.message);
                this.devices = [];
            }
        } catch (error) {
            console.error('加载设备数据出错:', error);
            this.devices = [];
        } finally {
            this.isLoading = false;
        }
    }

    // 设置厂区过滤
    setFactoryFilter(factoryId) {
        this.factoryFilter = factoryId;
        this.clearSelection();

        if (!factoryId) {
            this.inputElement.disabled = true;
            this.inputElement.placeholder = '请先选择厂区';
            this.inputElement.value = '';
            this.hideResults();
        } else {
            this.inputElement.disabled = false;
            this.inputElement.placeholder = '输入设备编号或名称进行搜索...';
            this.inputElement.value = '';
        }
    }

    handleInput(event) {
        const query = event.target.value.trim();

        // 清除之前的搜索超时
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 如果输入为空，清除选择
        if (!query) {
            this.clearSelection();
            this.hideResults();
            return;
        }

        // 延迟搜索，避免频繁请求
        this.searchTimeout = setTimeout(() => {
            this.searchDevices(query);
        }, 300);
    }

    handleFocus() {
        const query = this.inputElement.value.trim();
        if (query && this.factoryFilter) {
            this.searchDevices(query);
        }
    }

    handleBlur(event) {
        // 延迟隐藏结果，允许点击搜索结果
        setTimeout(() => {
            this.hideResults();
        }, 200);
    }

    handleKeydown(event) {
        if (this.resultsElement.style.display !== 'none') {
            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    this.selectNext();
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    this.selectPrevious();
                    break;
                case 'Enter':
                    event.preventDefault();
                    this.selectCurrent();
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.hideResults();
                    break;
            }
        }
    }

    handleDocumentClick(event) {
        if (!this.inputElement.contains(event.target) &&
            !this.resultsElement.contains(event.target)) {
            this.hideResults();
        }
    }

    searchDevices(query) {
        if (this.isLoading) {
            this.showLoading();
            return;
        }

        if (!this.factoryFilter) {
            this.hideResults();
            return;
        }

        const lowerQuery = query.toLowerCase();

        // 先按厂区过滤，再按搜索条件过滤
        this.filteredDevices = this.devices.filter(device => {
            // 厂区过滤
            if (device.factory !== this.factoryFilter) {
                return false;
            }

            // 搜索条件过滤
            const matchCode = device.deviceCode && device.deviceCode.toLowerCase().includes(lowerQuery);
            const matchName = device.deviceName && device.deviceName.toLowerCase().includes(lowerQuery);
            const matchLocation = device.location && device.location.toLowerCase().includes(lowerQuery);
            return matchCode || matchName || matchLocation;
        });

        this.selectedIndex = -1;
        this.renderResults();
        this.showResults();
    }

    renderResults() {
        if (this.filteredDevices.length === 0) {
            this.resultsElement.innerHTML = `
                <div style="padding: 12px; text-align: center; color: #6b7280; font-size: 14px;">
                    未找到匹配的设备
                </div>
            `;
            return;
        }

        const html = this.filteredDevices.map((device, index) => `
            <div class="device-search-item"
                 data-index="${index}"
                 style="padding: 12px; cursor: pointer; border-bottom: 1px solid #f3f4f6; ${index === this.selectedIndex ? 'background-color: #eff6ff;' : ''}"
                 onmouseover="this.style.backgroundColor='#f9fafb'"
                 onmouseout="this.style.backgroundColor='${index === this.selectedIndex ? '#eff6ff' : 'white'}'">
                <div style="font-weight: 500; color: #111827; margin-bottom: 4px;">
                    ${device.deviceCode} - ${device.deviceName}
                </div>
                <div style="font-size: 12px; color: #6b7280;">
                    位置: ${device.location} | 负责人: ${device.responsible} | 状态: ${device.status}
                </div>
            </div>
        `).join('');

        this.resultsElement.innerHTML = html;

        // 为搜索结果项添加点击事件
        const items = this.resultsElement.querySelectorAll('.device-search-item');
        items.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.selectDevice(index);
            });
        });
    }

    showLoading() {
        this.resultsElement.innerHTML = `
            <div style="padding: 12px; text-align: center; color: #6b7280; font-size: 14px;">
                正在加载设备数据...
            </div>
        `;
        this.showResults();
    }

    showResults() {
        this.resultsElement.style.display = 'block';
    }

    hideResults() {
        this.resultsElement.style.display = 'none';
    }

    selectNext() {
        if (this.selectedIndex < this.filteredDevices.length - 1) {
            this.selectedIndex++;
            this.updateSelection();
        }
    }

    selectPrevious() {
        if (this.selectedIndex > 0) {
            this.selectedIndex--;
            this.updateSelection();
        }
    }

    selectCurrent() {
        if (this.selectedIndex >= 0 && this.selectedIndex < this.filteredDevices.length) {
            this.selectDevice(this.selectedIndex);
        }
    }

    selectDevice(index) {
        if (index >= 0 && index < this.filteredDevices.length) {
            const device = this.filteredDevices[index];
            this.selectedDevice = device;

            // 更新输入框显示
            this.inputElement.value = `${device.deviceCode} - ${device.deviceName}`;

            // 更新隐藏字段
            this.hiddenInputElement.value = device.id;

            // 隐藏搜索结果
            this.hideResults();

            console.log('选择设备:', device);
        }
    }

    updateSelection() {
        const items = this.resultsElement.querySelectorAll('.device-search-item');
        items.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.style.backgroundColor = '#eff6ff';
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.style.backgroundColor = 'white';
            }
        });
    }

    clearSelection() {
        this.selectedDevice = null;
        this.hiddenInputElement.value = '';
        this.selectedIndex = -1;
    }

    // 获取当前选择的设备
    getSelectedDevice() {
        return this.selectedDevice;
    }

    // 设置选择的设备（用于编辑模式）
    setSelectedDevice(device) {
        if (device) {
            this.selectedDevice = device;
            this.inputElement.value = `${device.deviceCode} - ${device.deviceName}`;
            this.hiddenInputElement.value = device.id;
        } else {
            this.clearSelection();
            this.inputElement.value = '';
        }
    }

    // 重新加载设备数据
    async refresh() {
        await this.loadDevices();
    }

    // 设置只读状态
    setReadonly(readonly) {
        if (this.inputElement) {
            this.inputElement.disabled = readonly;
            if (readonly) {
                this.hideResults();
            }
        }
    }

    // 检查是否有效
    isValid() {
        return this.selectedDevice && this.hiddenInputElement.value;
    }
}

/**
 * 筛选器专用设备搜索组件
 * 支持按厂区过滤设备，用于筛选器
 */
class FilterDeviceSearch {
    constructor(inputId, hiddenInputId, resultsId) {
        this.inputElement = document.getElementById(inputId);
        this.hiddenInputElement = document.getElementById(hiddenInputId);
        this.resultsElement = document.getElementById(resultsId);
        this.devices = [];
        this.filteredDevices = [];
        this.selectedIndex = -1;
        this.selectedDevice = null;
        this.searchTimeout = null;
        this.isLoading = false;
        this.factoryFilter = null; // 厂区过滤
        this.onSelectionChange = null; // 选择变化回调

        this.init();
    }

    init() {
        if (!this.inputElement || !this.hiddenInputElement || !this.resultsElement) {
            console.error('筛选器设备搜索组件初始化失败：找不到必要的DOM元素');
            return;
        }

        // 绑定事件
        this.inputElement.addEventListener('input', this.handleInput.bind(this));
        this.inputElement.addEventListener('focus', this.handleFocus.bind(this));
        this.inputElement.addEventListener('blur', this.handleBlur.bind(this));
        this.inputElement.addEventListener('keydown', this.handleKeydown.bind(this));

        // 点击外部关闭搜索结果
        document.addEventListener('click', this.handleDocumentClick.bind(this));

        // 加载设备数据
        this.loadDevices();
    }

    async loadDevices() {
        try {
            this.isLoading = true;
            const response = await apiRequest('/api/devices');
            if (response.success) {
                this.devices = response.devices || [];
                console.log('筛选器设备数据加载成功:', this.devices.length, '个设备');
            } else {
                console.error('加载设备数据失败:', response.message);
                this.devices = [];
            }
        } catch (error) {
            console.error('加载设备数据出错:', error);
            this.devices = [];
        } finally {
            this.isLoading = false;
        }
    }

    // 设置厂区过滤
    setFactoryFilter(factoryId) {
        this.factoryFilter = factoryId;
        this.clearSelection();

        if (!factoryId) {
            this.inputElement.placeholder = '全部设备';
            this.inputElement.value = '';
        } else {
            this.inputElement.placeholder = '输入设备编号或名称进行搜索...';
            this.inputElement.value = '';
        }
    }

    // 设置选择变化回调
    setOnSelectionChange(callback) {
        this.onSelectionChange = callback;
    }

    handleInput(event) {
        const query = event.target.value.trim();

        // 清除之前的搜索超时
        if (this.searchTimeout) {
            clearTimeout(this.searchTimeout);
        }

        // 如果输入为空，清除选择并显示"全部设备"选项
        if (!query) {
            this.clearSelection();
            this.showAllDevicesOption();
            return;
        }

        // 延迟搜索，避免频繁请求
        this.searchTimeout = setTimeout(() => {
            this.searchDevices(query);
        }, 300);
    }

    handleFocus() {
        const query = this.inputElement.value.trim();
        if (!query) {
            this.showAllDevicesOption();
        } else {
            this.searchDevices(query);
        }
    }

    handleBlur(event) {
        // 延迟隐藏结果，允许点击搜索结果
        setTimeout(() => {
            this.hideResults();
        }, 200);
    }

    handleKeydown(event) {
        if (this.resultsElement.style.display !== 'none') {
            switch (event.key) {
                case 'ArrowDown':
                    event.preventDefault();
                    this.selectNext();
                    break;
                case 'ArrowUp':
                    event.preventDefault();
                    this.selectPrevious();
                    break;
                case 'Enter':
                    event.preventDefault();
                    this.selectCurrent();
                    break;
                case 'Escape':
                    event.preventDefault();
                    this.hideResults();
                    break;
            }
        }
    }

    handleDocumentClick(event) {
        if (!this.inputElement.contains(event.target) &&
            !this.resultsElement.contains(event.target)) {
            this.hideResults();
        }
    }

    showAllDevicesOption() {
        this.filteredDevices = [{ id: '', deviceCode: '全部设备', deviceName: '', isAllOption: true }];
        this.selectedIndex = -1;
        this.renderResults();
        this.showResults();
    }

    searchDevices(query) {
        if (this.isLoading) {
            this.showLoading();
            return;
        }

        const lowerQuery = query.toLowerCase();

        // 添加"全部设备"选项
        this.filteredDevices = [{ id: '', deviceCode: '全部设备', deviceName: '', isAllOption: true }];

        // 过滤设备
        const matchedDevices = this.devices.filter(device => {
            // 如果有厂区过滤，先按厂区过滤
            if (this.factoryFilter && device.factory !== this.factoryFilter) {
                return false;
            }

            // 搜索条件过滤
            const matchCode = device.deviceCode && device.deviceCode.toLowerCase().includes(lowerQuery);
            const matchName = device.deviceName && device.deviceName.toLowerCase().includes(lowerQuery);
            const matchLocation = device.location && device.location.toLowerCase().includes(lowerQuery);
            return matchCode || matchName || matchLocation;
        });

        this.filteredDevices = this.filteredDevices.concat(matchedDevices);
        this.selectedIndex = -1;
        this.renderResults();
        this.showResults();
    }

    renderResults() {
        if (this.filteredDevices.length === 0) {
            this.resultsElement.innerHTML = `
                <div style="padding: 12px; text-align: center; color: #6b7280; font-size: 14px;">
                    未找到匹配的设备
                </div>
            `;
            return;
        }

        const html = this.filteredDevices.map((device, index) => {
            if (device.isAllOption) {
                return `
                    <div class="device-search-item"
                         data-index="${index}"
                         style="padding: 12px; cursor: pointer; border-bottom: 1px solid #f3f4f6; font-weight: 500; color: #3b82f6; ${index === this.selectedIndex ? 'background-color: #eff6ff;' : ''}"
                         onmouseover="this.style.backgroundColor='#f9fafb'"
                         onmouseout="this.style.backgroundColor='${index === this.selectedIndex ? '#eff6ff' : 'white'}'">
                        ${device.deviceCode}
                    </div>
                `;
            } else {
                return `
                    <div class="device-search-item"
                         data-index="${index}"
                         style="padding: 12px; cursor: pointer; border-bottom: 1px solid #f3f4f6; ${index === this.selectedIndex ? 'background-color: #eff6ff;' : ''}"
                         onmouseover="this.style.backgroundColor='#f9fafb'"
                         onmouseout="this.style.backgroundColor='${index === this.selectedIndex ? '#eff6ff' : 'white'}'">
                        <div style="font-weight: 500; color: #111827; margin-bottom: 4px;">
                            ${device.deviceCode} - ${device.deviceName}
                        </div>
                        <div style="font-size: 12px; color: #6b7280;">
                            位置: ${device.location} | 负责人: ${device.responsible} | 状态: ${device.status}
                        </div>
                    </div>
                `;
            }
        }).join('');

        this.resultsElement.innerHTML = html;

        // 为搜索结果项添加点击事件
        const items = this.resultsElement.querySelectorAll('.device-search-item');
        items.forEach((item, index) => {
            item.addEventListener('click', () => {
                this.selectDevice(index);
            });
        });
    }

    showLoading() {
        this.resultsElement.innerHTML = `
            <div style="padding: 12px; text-align: center; color: #6b7280; font-size: 14px;">
                正在加载设备数据...
            </div>
        `;
        this.showResults();
    }

    showResults() {
        this.resultsElement.style.display = 'block';
    }

    hideResults() {
        this.resultsElement.style.display = 'none';
    }

    selectNext() {
        if (this.selectedIndex < this.filteredDevices.length - 1) {
            this.selectedIndex++;
            this.updateSelection();
        }
    }

    selectPrevious() {
        if (this.selectedIndex > 0) {
            this.selectedIndex--;
            this.updateSelection();
        }
    }

    selectCurrent() {
        if (this.selectedIndex >= 0 && this.selectedIndex < this.filteredDevices.length) {
            this.selectDevice(this.selectedIndex);
        }
    }

    selectDevice(index) {
        if (index >= 0 && index < this.filteredDevices.length) {
            const device = this.filteredDevices[index];
            this.selectedDevice = device;

            if (device.isAllOption) {
                // 选择"全部设备"
                this.inputElement.value = '全部设备';
                this.hiddenInputElement.value = '';
            } else {
                // 选择具体设备
                this.inputElement.value = `${device.deviceCode} - ${device.deviceName}`;
                this.hiddenInputElement.value = device.id;
            }

            // 隐藏搜索结果
            this.hideResults();

            // 触发选择变化回调
            if (this.onSelectionChange) {
                this.onSelectionChange(device.isAllOption ? '' : device.id);
            }

            console.log('筛选器选择设备:', device);
        }
    }

    updateSelection() {
        const items = this.resultsElement.querySelectorAll('.device-search-item');
        items.forEach((item, index) => {
            if (index === this.selectedIndex) {
                item.style.backgroundColor = '#eff6ff';
                item.scrollIntoView({ block: 'nearest' });
            } else {
                item.style.backgroundColor = 'white';
            }
        });
    }

    clearSelection() {
        this.selectedDevice = null;
        this.hiddenInputElement.value = '';
        this.selectedIndex = -1;

        // 触发选择变化回调
        if (this.onSelectionChange) {
            this.onSelectionChange('');
        }
    }

    // 重新加载设备数据
    async refresh() {
        await this.loadDevices();
    }
}

// 全局实例，供HTML onclick事件使用
let deviceSearchInstance = null;
let maintenanceDeviceSearchInstance = null;
let filterDeviceSearchInstance = null;
