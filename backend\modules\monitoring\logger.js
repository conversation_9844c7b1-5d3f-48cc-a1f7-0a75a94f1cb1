const fs = require('fs');
const path = require('path');

/**
 * 结构化日志系统
 * 提供多级别日志记录、日志轮转、性能监控和错误追踪
 * 支持JSON格式输出、文件分割和日志聚合
 */
class Logger {
    constructor(options = {}) {
        this.config = {
            level: options.level || 'info',
            logDir: options.logDir || path.join(__dirname, '../logs'),
            maxFileSize: options.maxFileSize || 10 * 1024 * 1024, // 10MB
            maxFiles: options.maxFiles || 10,
            enableConsole: options.enableConsole !== false,
            enableFile: options.enableFile !== false,
            format: options.format || 'json', // 'json' or 'text'
            timezone: options.timezone || 'Asia/Shanghai',
            silentMode: options.silentMode || false // 静默模式
        };

        // 日志级别定义
        this.levels = {
            error: 0,
            warn: 1,
            info: 2,
            debug: 3,
            trace: 4
        };

        // 当前日志级别
        this.currentLevel = this.levels[this.config.level] || this.levels.info;

        // 确保日志目录存在
        this.ensureLogDirectory();

        // 初始化日志文件流
        this.initializeLogStreams();

        // 启动日志轮转检查
        this.startLogRotation();
    }

    /**
     * 确保日志目录存在
     */
    ensureLogDirectory() {
        if (!fs.existsSync(this.config.logDir)) {
            fs.mkdirSync(this.config.logDir, { recursive: true });
        }
    }

    /**
     * 初始化日志文件流
     */
    initializeLogStreams() {
        this.streams = {};
        
        if (this.config.enableFile) {
            // 创建不同级别的日志文件流
            this.streams.combined = this.createLogStream('combined.log');
            this.streams.error = this.createLogStream('error.log');
            this.streams.access = this.createLogStream('access.log');
            this.streams.security = this.createLogStream('security.log');
            this.streams.performance = this.createLogStream('performance.log');
        }
    }

    /**
     * 创建日志文件流
     */
    createLogStream(filename) {
        const filePath = path.join(this.config.logDir, filename);
        return fs.createWriteStream(filePath, { flags: 'a' });
    }

    /**
     * 格式化日志消息
     */
    formatMessage(level, message, meta = {}) {
        const now = new Date();
        const timestamp = now.toLocaleString('zh-CN', {
            timeZone: this.config.timezone,
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            second: '2-digit',
            hour12: false
        });

        // 格式化为【2025/07/04 13:57:25】格式
        const formattedTimestamp = `【${timestamp.replace(/\//g, '/').replace(/\s/g, ' ')}】`;

        const logEntry = {
            timestamp: formattedTimestamp,
            level: level.toUpperCase(),
            message,
            ...meta,
            pid: process.pid,
            hostname: require('os').hostname(),
            version: process.env.npm_package_version || '1.0.0'
        };

        if (this.config.format === 'json') {
            return JSON.stringify(logEntry) + '\n';
        } else {
            return `${formattedTimestamp} ${level.toUpperCase()}: ${message} ${Object.keys(meta).length ? JSON.stringify(meta) : ''}\n`;
        }
    }

    /**
     * 写入日志
     */
    writeLog(level, message, meta = {}) {
        if (this.levels[level] > this.currentLevel) {
            return; // 跳过低于当前级别的日志
        }

        const formattedMessage = this.formatMessage(level, message, meta);

        // 控制台输出（静默模式下只输出错误）
        if (this.config.enableConsole && (!this.config.silentMode || level === 'error')) {
            this.writeToConsole(level, formattedMessage);
        }

        // 文件输出
        if (this.config.enableFile && this.streams) {
            this.writeToFile(level, formattedMessage);
        }
    }

    /**
     * 写入控制台
     */
    writeToConsole(level, message) {
        const colors = {
            error: '\x1b[31m',   // 红色
            warn: '\x1b[33m',    // 黄色
            info: '\x1b[36m',    // 青色
            debug: '\x1b[35m',   // 紫色
            trace: '\x1b[37m'    // 白色
        };

        const reset = '\x1b[0m';

        // 如果是JSON格式，解析并重新格式化为更友好的控制台输出
        let consoleMessage = message;
        if (this.config.format === 'json') {
            try {
                const logData = JSON.parse(message);
                consoleMessage = `${logData.timestamp} ${logData.level}: ${logData.message}`;
                if (Object.keys(logData).length > 5) { // 除了基本字段外还有其他信息
                    const extraData = { ...logData };
                    delete extraData.timestamp;
                    delete extraData.level;
                    delete extraData.message;
                    delete extraData.pid;
                    delete extraData.hostname;
                    delete extraData.version;
                    if (Object.keys(extraData).length > 0) {
                        consoleMessage += ` ${JSON.stringify(extraData)}`;
                    }
                }
            } catch (e) {
                // 如果解析失败，使用原始消息
                consoleMessage = message;
            }
        }

        const coloredMessage = `${colors[level] || ''}${consoleMessage.trim()}${reset}`;

        if (level === 'error') {
            console.error(coloredMessage);
        } else if (level === 'warn') {
            console.warn(coloredMessage);
        } else {
            console.log(coloredMessage);
        }
    }

    /**
     * 写入文件
     */
    writeToFile(level, message) {
        try {
            // 写入综合日志
            if (this.streams.combined) {
                this.streams.combined.write(message);
            }

            // 写入特定级别日志
            if (level === 'error' && this.streams.error) {
                this.streams.error.write(message);
            }
        } catch (error) {
            // 避免日志写入错误导致系统崩溃
            console.error('日志写入失败:', error.message);
        }
    }

    /**
     * 错误日志
     */
    error(message, meta = {}) {
        this.writeLog('error', message, {
            ...meta,
            stack: meta.stack || (new Error()).stack
        });
    }

    /**
     * 警告日志
     */
    warn(message, meta = {}) {
        this.writeLog('warn', message, meta);
    }

    /**
     * 信息日志
     */
    info(message, meta = {}) {
        this.writeLog('info', message, meta);
    }

    /**
     * 调试日志
     */
    debug(message, meta = {}) {
        this.writeLog('debug', message, meta);
    }

    /**
     * 跟踪日志
     */
    trace(message, meta = {}) {
        this.writeLog('trace', message, meta);
    }

    /**
     * 访问日志
     */
    access(req, res, responseTime) {
        const logData = {
            method: req.method,
            url: req.originalUrl || req.url,
            statusCode: res.statusCode,
            responseTime: `${responseTime}ms`,
            userAgent: req.get('User-Agent'),
            ip: req.ip || req.connection.remoteAddress,
            userId: req.user?.id,
            sessionId: req.sessionID,
            contentLength: res.get('Content-Length') || 0
        };

        const message = `${req.method} ${req.originalUrl || req.url} ${res.statusCode} ${responseTime}ms`;
        
        if (this.streams.access) {
            const formattedMessage = this.formatMessage('info', message, logData);
            this.streams.access.write(formattedMessage);
        }

        this.info(message, logData);
    }

    /**
     * 安全日志
     */
    security(event, details = {}) {
        const logData = {
            event,
            ...details,
            severity: details.severity || 'medium'
        };

        const message = `Security Event: ${event}`;
        
        if (this.streams.security) {
            const formattedMessage = this.formatMessage('warn', message, logData);
            this.streams.security.write(formattedMessage);
        }

        this.warn(message, logData);
    }

    /**
     * 性能日志
     */
    performance(operation, duration, details = {}) {
        const logData = {
            operation,
            duration: `${duration}ms`,
            ...details
        };

        const message = `Performance: ${operation} took ${duration}ms`;
        
        if (this.streams.performance) {
            const formattedMessage = this.formatMessage('info', message, logData);
            this.streams.performance.write(formattedMessage);
        }

        // 如果操作时间过长，记录为警告
        const threshold = details.threshold || 1000; // 默认1秒
        if (duration > threshold) {
            this.warn(`Slow operation detected: ${message}`, logData);
        } else {
            this.debug(message, logData);
        }
    }

    /**
     * 数据库操作日志
     */
    database(operation, table, duration, details = {}) {
        const logData = {
            operation,
            table,
            duration: `${duration}ms`,
            ...details
        };

        const message = `Database: ${operation} on ${table} (${duration}ms)`;
        this.performance(`db_${operation}`, duration, logData);
    }

    /**
     * API请求日志
     */
    api(endpoint, method, statusCode, duration, details = {}) {
        const logData = {
            endpoint,
            method,
            statusCode,
            duration: `${duration}ms`,
            ...details
        };

        const message = `API: ${method} ${endpoint} ${statusCode} (${duration}ms)`;
        this.performance(`api_${method.toLowerCase()}`, duration, logData);
    }

    /**
     * 用户操作日志
     */
    userAction(userId, action, details = {}) {
        const logData = {
            userId,
            action,
            ...details
        };

        const message = `User Action: ${userId} performed ${action}`;
        this.info(message, logData);
    }

    /**
     * 系统事件日志
     */
    system(event, details = {}) {
        const logData = {
            event,
            ...details
        };

        const message = `System Event: ${event}`;
        this.info(message, logData);
    }

    /**
     * 检查日志文件大小并轮转
     */
    checkLogRotation() {
        if (!this.config.enableFile) return;

        for (const [name, stream] of Object.entries(this.streams)) {
            const filename = `${name}.log`;
            const filePath = path.join(this.config.logDir, filename);
            
            try {
                const stats = fs.statSync(filePath);
                if (stats.size > this.config.maxFileSize) {
                    this.rotateLogFile(name, filename);
                }
            } catch (error) {
                // 文件不存在或其他错误，忽略
            }
        }
    }

    /**
     * 轮转日志文件
     */
    rotateLogFile(streamName, filename) {
        try {
            // 关闭当前流
            if (this.streams[streamName]) {
                this.streams[streamName].end();
            }

            const basePath = path.join(this.config.logDir, filename);
            const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
            const rotatedPath = path.join(this.config.logDir, `${filename}.${timestamp}`);

            // 重命名当前文件
            fs.renameSync(basePath, rotatedPath);

            // 创建新的流
            this.streams[streamName] = this.createLogStream(filename);

            // 清理旧文件
            this.cleanupOldLogs(filename);

            this.info(`Log file rotated: ${filename}`, { rotatedTo: rotatedPath });
        } catch (error) {
            this.error('Log rotation failed', { streamName, filename, error: error.message });
        }
    }

    /**
     * 清理旧日志文件
     */
    cleanupOldLogs(baseFilename) {
        try {
            const files = fs.readdirSync(this.config.logDir)
                .filter(file => file.startsWith(baseFilename) && file !== baseFilename)
                .map(file => ({
                    name: file,
                    path: path.join(this.config.logDir, file),
                    stats: fs.statSync(path.join(this.config.logDir, file))
                }))
                .sort((a, b) => b.stats.mtime - a.stats.mtime);

            // 删除超过最大文件数的旧文件
            if (files.length > this.config.maxFiles) {
                const filesToDelete = files.slice(this.config.maxFiles);
                filesToDelete.forEach(file => {
                    fs.unlinkSync(file.path);
                    this.debug(`Deleted old log file: ${file.name}`);
                });
            }
        } catch (error) {
            this.error('Failed to cleanup old logs', { baseFilename, error: error.message });
        }
    }

    /**
     * 启动日志轮转检查
     */
    startLogRotation() {
        // 每小时检查一次
        setInterval(() => {
            this.checkLogRotation();
        }, 60 * 60 * 1000);
    }

    /**
     * 创建Express中间件
     */
    expressMiddleware() {
        return (req, res, next) => {
            const startTime = Date.now();

            // 避免重复监听
            if (!res.locals.loggerAttached) {
                res.locals.loggerAttached = true;

                // 监听响应完成
                res.on('finish', () => {
                    const responseTime = Date.now() - startTime;
                    this.access(req, res, responseTime);
                });
            }

            next();
        };
    }

    /**
     * 获取日志统计信息
     */
    getLogStats() {
        const stats = {};
        
        if (this.config.enableFile) {
            for (const [name] of Object.entries(this.streams)) {
                const filename = `${name}.log`;
                const filePath = path.join(this.config.logDir, filename);
                
                try {
                    const fileStats = fs.statSync(filePath);
                    stats[name] = {
                        size: fileStats.size,
                        created: fileStats.birthtime,
                        modified: fileStats.mtime
                    };
                } catch (error) {
                    stats[name] = { error: 'File not found' };
                }
            }
        }

        return {
            config: this.config,
            currentLevel: this.currentLevel,
            files: stats
        };
    }

    /**
     * 关闭所有日志流
     */
    close() {
        if (this.streams) {
            for (const stream of Object.values(this.streams)) {
                if (stream && typeof stream.end === 'function') {
                    stream.end();
                }
            }
        }
    }
}

module.exports = Logger;
