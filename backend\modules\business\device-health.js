/**
 * 设备健康度评估模块
 *
 * 评估体系概述：
 * 设备健康度评估采用综合评分制，满分100分，基于四个核心维度进行评估：
 *
 * 1. 设备年龄 (20%权重)
 *    - 100分：使用不足1年的新设备
 *    - 90分：使用1-3年，设备较新
 *    - 80分：使用3-5年，设备状态良好
 *    - 70分：使用5-8年，进入中期
 *    - 60分：使用8-10年，需要关注
 *    - 30-60分：超过10年，每年递减3分
 *
 * 2. 维修频率 (30%权重)
 *    - 100分：年均维修次数 < 0.5次
 *    - 90分：年均维修次数 0.5-1次
 *    - 80分：年均维修次数 1-2次
 *    - 70分：年均维修次数 2-3次
 *    - 60分：年均维修次数 3-4次
 *    - 20-60分：超过4次，每次递减10分
 *
 * 3. 故障严重程度 (30%权重)
 *    采用时间加权 + 严重程度分层算法：
 *    - 时间权重：近期故障影响更大
 *      · 近3个月：权重1.0
 *      · 3-6个月：权重0.8
 *      · 6-12个月：权重0.6
 *      · 12-24个月：权重0.4
 *      · 24个月以上：权重0.2
 *    - 严重程度分层扣分：
 *      · 严重故障：最多扣50分
 *      · 一般故障：最多扣25分
 *      · 轻微问题：最多扣10分
 *    - 向后兼容：历史记录使用关键词分析确定严重程度
 *    评分公式：100 - 严重故障扣分 - 一般故障扣分 - 轻微故障扣分，最低15分
 *
 * 4. 保养情况 (20%权重)
 *    综合考虑保养频率和最近保养时间：
 *    - 保养比例：保养记录占总记录的比例 (60%权重)
 *    - 最近保养：距离上次保养的时间 (40%权重)
 *    最近保养时间评分：
 *    - 30天内：100分
 *    - 30-60天：90分
 *    - 60-90天：80分
 *    - 90-180天：70分
 *    - 180-365天：60分
 *    - 超过365天：40分
 *
 * 健康度等级划分：
 * - 90-100分：优秀 (设备状态极佳，维护良好)
 * - 80-89分：良好 (设备状态良好，正常运行)
 * - 70-79分：一般 (设备状态一般，需要关注)
 * - 60-69分：较差 (设备状态较差，需要维护)
 * - 0-59分：危险 (设备状态危险，急需检修)
 *
 * 故障预测算法：
 * - 数据要求：至少需要2次维修记录
 * - 计算方法：分析维修间隔的平均值和方差
 * - 置信度：基于数据稳定性计算，范围30%-90%
 * - 预测时间：上次维修时间 + 平均间隔
 *
 * 总得分计算方法：
 * 采用加权平均法计算设备健康度总得分
 *
 * 计算公式：
 * 总得分 = 设备年龄得分 × 0.2 + 维修频率得分 × 0.3 + 故障严重程度得分 × 0.3 + 保养情况得分 × 0.2
 *
 * 计算步骤：
 * 1. 设备年龄得分：根据使用年限查表获得
 * 2. 维修频率得分：年均维修次数 = 维修记录总数 ÷ 设备使用年限，然后查表获得
 * 3. 故障严重程度得分：采用时间加权 + 严重程度分层算法，公式：100 - 严重故障扣分 - 一般故障扣分 - 轻微故障扣分
 * 4. 保养情况得分：保养比例得分 × 0.6 + 最近保养得分 × 0.4
 * 5. 最终总得分：按权重加权求和，限制在0-100分范围内
 *
 * 特殊情况处理：
 * - 新设备：缺乏历史数据时，保养得分默认为80分
 * - 无维修记录：维修频率得分为100分，故障严重程度得分为100分
 * - 数据异常：各维度得分限制在0-100分范围内
 *
 * 维护建议生成规则：
 * - 总分 < 60分：立即全面检修
 * - 维修频率评分 < 70分：检查运行环境和操作规范
 * - 故障严重度评分 < 70分：进行全面检修
 * - 保养评分 < 70分：制定定期保养计划
 * - 设备年龄 > 5年：考虑设备升级评估
 */
class DeviceHealth {
    constructor(deviceManager, maintenanceManager) {
        this.deviceManager = deviceManager;
        this.maintenanceManager = maintenanceManager;
    }

    // 计算设备健康度评分 (0-100分)
    calculateHealthScore(deviceId) {
        const device = this.deviceManager.getDeviceById(deviceId);
        if (!device) {
            return { score: 0, details: '设备不存在' };
        }

        const records = this.maintenanceManager.getAllRecords({ deviceId });
        // 优先使用进厂日期，如果没有则使用创建时间（向后兼容）
        const ageCalculationDate = device.entryDate || device.createTime;
        const deviceAge = this.calculateDeviceAge(ageCalculationDate);
        
        // 各项评分权重
        const weights = {
            ageScore: 0.2,        // 设备年龄 20%
            maintenanceScore: 0.3, // 维修频率 30%
            faultScore: 0.3,      // 故障严重程度 30%
            upkeepScore: 0.2      // 保养情况 20%
        };

        // 1. 设备年龄评分 (越新越好)
        const ageScore = this.calculateAgeScore(deviceAge);

        // 2. 维修频率评分 (维修越少越好)
        const maintenanceScore = this.calculateMaintenanceScore(records, deviceAge);

        // 3. 故障严重程度评分
        const faultScore = this.calculateFaultScore(records);

        // 4. 保养情况评分 (定期保养越好)
        const upkeepScore = this.calculateUpkeepScore(records);

        // 综合评分
        const totalScore = Math.round(
            ageScore * weights.ageScore +
            maintenanceScore * weights.maintenanceScore +
            faultScore * weights.faultScore +
            upkeepScore * weights.upkeepScore
        );

        return {
            score: Math.max(0, Math.min(100, totalScore)),
            details: {
                ageScore: Math.round(ageScore),
                maintenanceScore: Math.round(maintenanceScore),
                faultScore: Math.round(faultScore),
                upkeepScore: Math.round(upkeepScore),
                deviceAge: deviceAge,
                totalRecords: records.length,
                maintenanceRecords: records.filter(r => r.type === '维修').length,
                upkeepRecords: records.filter(r => r.type === '保养' || r.type === '临时保养').length
            },
            level: this.getHealthLevel(totalScore),
            recommendations: this.generateRecommendations(totalScore, {
                ageScore, maintenanceScore, faultScore, upkeepScore
            })
        };
    }

    // 计算设备年龄（年）
    calculateDeviceAge(dateTime) {
        const now = new Date();
        const startDate = new Date(dateTime);
        return (now - startDate) / (1000 * 60 * 60 * 24 * 365);
    }

    // 设备年龄评分
    calculateAgeScore(ageInYears) {
        if (ageInYears < 1) return 100;
        if (ageInYears < 3) return 90;
        if (ageInYears < 5) return 80;
        if (ageInYears < 8) return 70;
        if (ageInYears < 10) return 60;
        return Math.max(30, 60 - (ageInYears - 10) * 3);
    }

    // 维修频率评分
    calculateMaintenanceScore(records, deviceAge) {
        const maintenanceRecords = records.filter(r => r.type === '维修');
        if (maintenanceRecords.length === 0) return 100;

        // 计算年均维修次数
        const yearsInService = Math.max(0.5, deviceAge);
        const maintenancePerYear = maintenanceRecords.length / yearsInService;

        if (maintenancePerYear < 0.5) return 100;
        if (maintenancePerYear < 1) return 90;
        if (maintenancePerYear < 2) return 80;
        if (maintenancePerYear < 3) return 70;
        if (maintenancePerYear < 4) return 60;
        return Math.max(20, 60 - (maintenancePerYear - 4) * 10);
    }

    // 故障严重程度评分 - 时间加权 + 严重程度分层算法
    calculateFaultScore(records) {
        const maintenanceRecords = records.filter(r => r.type === '维修');
        if (maintenanceRecords.length === 0) return 100;

        const now = new Date();

        // 分层统计不同严重程度的故障
        const faultLayers = {
            severe: { totalWeight: 0, count: 0 },      // 严重故障
            moderate: { totalWeight: 0, count: 0 },    // 一般故障
            minor: { totalWeight: 0, count: 0 }        // 轻微问题
        };

        let totalWeightedRecords = 0;

        maintenanceRecords.forEach(record => {
            // 计算时间权重
            const recordDate = new Date(record.date);
            const monthsAgo = (now - recordDate) / (1000 * 60 * 60 * 24 * 30);
            const timeWeight = this.calculateTimeWeight(monthsAgo);

            totalWeightedRecords += timeWeight;

            // 确定故障严重程度并累加权重
            let severityLevel = 'moderate'; // 默认一般故障

            if (record.faultSeverity) {
                // 基于用户选择的故障程度
                severityLevel = record.faultSeverity;
            } else {
                // 向后兼容：基于关键词分析确定严重程度
                severityLevel = this.analyzeFaultSeverityFromDescription(record.description || '');
            }

            // 标准化严重程度级别
            if (severityLevel === 'severe') {
                faultLayers.severe.totalWeight += timeWeight;
                faultLayers.severe.count++;
            } else if (severityLevel === 'minor') {
                faultLayers.minor.totalWeight += timeWeight;
                faultLayers.minor.count++;
            } else {
                // moderate 或其他情况
                faultLayers.moderate.totalWeight += timeWeight;
                faultLayers.moderate.count++;
            }
        });

        if (totalWeightedRecords === 0) return 100;

        // 计算各层级的权重比例
        const severeRatio = faultLayers.severe.totalWeight / totalWeightedRecords;
        const moderateRatio = faultLayers.moderate.totalWeight / totalWeightedRecords;
        const minorRatio = faultLayers.minor.totalWeight / totalWeightedRecords;

        // 分层计算扣分
        const severeDeduction = severeRatio * 50;    // 严重故障最多扣50分
        const moderateDeduction = moderateRatio * 25; // 一般故障最多扣25分
        const minorDeduction = minorRatio * 10;       // 轻微问题最多扣10分

        // 计算最终得分
        const finalScore = 100 - severeDeduction - moderateDeduction - minorDeduction;

        // 确保分数在合理范围内
        return Math.max(15, Math.min(100, Math.round(finalScore)));
    }

    // 计算时间权重
    calculateTimeWeight(monthsAgo) {
        if (monthsAgo < 3) return 1.0;      // 近3个月：权重1.0
        if (monthsAgo < 6) return 0.8;      // 3-6个月：权重0.8
        if (monthsAgo < 12) return 0.6;     // 6-12个月：权重0.6
        if (monthsAgo < 24) return 0.4;     // 12-24个月：权重0.4
        return 0.2;                         // 24个月以上：权重0.2
    }

    // 基于描述分析故障严重程度（向后兼容）
    analyzeFaultSeverityFromDescription(description) {
        const desc = description.toLowerCase();
        const severityKeywords = {
            severe: ['停机', '无法启动', '完全故障', '严重', '紧急', '危险', '损坏', '失效'],
            minor: ['调整', '清洁', '润滑', '校准', '检查', '轻微', '小问题']
        };

        if (severityKeywords.severe.some(keyword => desc.includes(keyword))) {
            return 'severe';
        } else if (severityKeywords.minor.some(keyword => desc.includes(keyword))) {
            return 'minor';
        } else {
            return 'moderate'; // 默认一般故障
        }
    }

    // 保养情况评分
    calculateUpkeepScore(records) {
        const upkeepRecords = records.filter(r => r.type === '保养' || r.type === '临时保养');

        if (records.length === 0) return 80; // 新设备默认80分

        // 保养与维修的比例
        const upkeepRatio = upkeepRecords.length / Math.max(1, records.length);
        
        // 最近保养情况
        const recentUpkeep = this.getRecentUpkeepScore(upkeepRecords);
        
        // 保养频率评分
        const frequencyScore = upkeepRatio * 100;
        
        return Math.round((frequencyScore * 0.6 + recentUpkeep * 0.4));
    }

    // 最近保养情况评分
    getRecentUpkeepScore(upkeepRecords) {
        if (upkeepRecords.length === 0) return 50;

        const now = new Date();
        const sortedRecords = upkeepRecords.sort((a, b) => new Date(b.date) - new Date(a.date));
        const lastUpkeep = new Date(sortedRecords[0].date);
        const daysSinceLastUpkeep = (now - lastUpkeep) / (1000 * 60 * 60 * 24);

        if (daysSinceLastUpkeep < 30) return 100;
        if (daysSinceLastUpkeep < 60) return 90;
        if (daysSinceLastUpkeep < 90) return 80;
        if (daysSinceLastUpkeep < 180) return 70;
        if (daysSinceLastUpkeep < 365) return 60;
        return 40;
    }

    // 获取健康等级
    getHealthLevel(score) {
        if (score >= 90) return { level: 'excellent', text: '优秀', color: '#10b981' };
        if (score >= 80) return { level: 'good', text: '良好', color: '#3b82f6' };
        if (score >= 70) return { level: 'fair', text: '一般', color: '#f59e0b' };
        if (score >= 60) return { level: 'poor', text: '较差', color: '#f97316' };
        return { level: 'critical', text: '危险', color: '#ef4444' };
    }

    // 生成维护建议
    generateRecommendations(totalScore, scores) {
        const recommendations = [];

        if (scores.ageScore < 70) {
            recommendations.push({
                type: 'age',
                priority: 'medium',
                message: '设备使用年限较长，建议考虑更换或加强维护'
            });
        }

        if (scores.maintenanceScore < 70) {
            recommendations.push({
                type: 'maintenance',
                priority: 'high',
                message: '维修频率过高，建议检查设备运行环境和操作规范'
            });
        }

        if (scores.faultScore < 70) {
            recommendations.push({
                type: 'fault',
                priority: 'high',
                message: '故障严重程度较高，建议进行全面检修'
            });
        }

        if (scores.upkeepScore < 70) {
            recommendations.push({
                type: 'upkeep',
                priority: 'medium',
                message: '保养不够及时，建议制定定期保养计划'
            });
        }

        if (totalScore >= 90) {
            recommendations.push({
                type: 'maintain',
                priority: 'low',
                message: '设备状态良好，继续保持当前维护水平'
            });
        }

        return recommendations;
    }

    // 预测下次故障时间
    predictNextFailure(deviceId) {
        const records = this.maintenanceManager.getAllRecords({ deviceId });
        const maintenanceRecords = records.filter(r => r.type === '维修');

        if (maintenanceRecords.length < 2) {
            return {
                prediction: null,
                confidence: 0,
                message: '维修记录不足，无法预测'
            };
        }

        // 计算维修间隔
        const intervals = [];
        const sortedRecords = maintenanceRecords.sort((a, b) => new Date(a.date) - new Date(b.date));

        for (let i = 1; i < sortedRecords.length; i++) {
            const prev = new Date(sortedRecords[i - 1].date);
            const curr = new Date(sortedRecords[i].date);
            intervals.push((curr - prev) / (1000 * 60 * 60 * 24)); // 天数
        }

        // 计算平均间隔和趋势
        const avgInterval = intervals.reduce((sum, interval) => sum + interval, 0) / intervals.length;
        const lastMaintenanceDate = new Date(sortedRecords[sortedRecords.length - 1].date);
        const predictedDate = new Date(lastMaintenanceDate.getTime() + avgInterval * 24 * 60 * 60 * 1000);

        // 计算置信度
        const variance = intervals.reduce((sum, interval) => sum + Math.pow(interval - avgInterval, 2), 0) / intervals.length;
        const confidence = Math.max(0.3, Math.min(0.9, 1 - (Math.sqrt(variance) / avgInterval)));

        return {
            prediction: predictedDate,
            confidence: Math.round(confidence * 100),
            avgInterval: Math.round(avgInterval),
            message: `基于历史数据预测，下次故障可能在 ${predictedDate.toLocaleDateString()} 左右发生`
        };
    }

    // 生成维护计划建议
    generateMaintenancePlan(deviceId) {
        const healthData = this.calculateHealthScore(deviceId);
        const failurePrediction = this.predictNextFailure(deviceId);
        
        const plan = {
            immediate: [], // 立即执行
            shortTerm: [], // 1个月内
            longTerm: []   // 3个月内
        };

        // 基于健康度生成建议
        if (healthData.score < 60) {
            plan.immediate.push({
                action: '全面检修',
                reason: '设备健康度较低',
                priority: 'high'
            });
        }

        if (healthData.details.upkeepScore < 70) {
            plan.shortTerm.push({
                action: '定期保养',
                reason: '保养不够及时',
                priority: 'medium'
            });
        }

        if (failurePrediction.prediction) {
            const daysUntilFailure = (failurePrediction.prediction - new Date()) / (1000 * 60 * 60 * 24);
            if (daysUntilFailure < 30) {
                plan.immediate.push({
                    action: '预防性维修',
                    reason: '预测即将发生故障',
                    priority: 'high'
                });
            } else if (daysUntilFailure < 90) {
                plan.shortTerm.push({
                    action: '预防性检查',
                    reason: '预测可能发生故障',
                    priority: 'medium'
                });
            }
        }

        // 基于设备年龄的建议
        if (healthData.details.deviceAge > 5) {
            plan.longTerm.push({
                action: '设备升级评估',
                reason: '设备使用年限较长',
                priority: 'low'
            });
        }

        return {
            healthScore: healthData.score,
            plan: plan,
            nextReview: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000) // 30天后复查
        };
    }

    // 获取所有设备的健康度概览
    getAllDevicesHealthOverview() {
        const devices = this.deviceManager.getAllDevices({});
        const factories = this.deviceManager.getFactories();

        const overview = devices.map(device => {
            const health = this.calculateHealthScore(device.id);
            return {
                deviceId: device.id,
                deviceCode: device.deviceCode,
                deviceName: device.deviceName,
                healthScore: health.score,
                healthLevel: health.level,
                location: device.location,
                responsible: device.responsible,
                factory: device.factory
            };
        });

        // 按厂区分组设备
        const devicesByFactory = {};
        overview.forEach(device => {
            const factoryId = device.factory;
            if (!devicesByFactory[factoryId]) {
                const factory = factories.find(f => f.id === factoryId);
                devicesByFactory[factoryId] = {
                    factoryId: factoryId,
                    factoryName: factory ? factory.name : factoryId,
                    devices: []
                };
            }
            devicesByFactory[factoryId].devices.push(device);
        });

        // 对每个厂区内的设备按健康度排序
        Object.values(devicesByFactory).forEach(factory => {
            factory.devices.sort((a, b) => a.healthScore - b.healthScore);
        });

        return {
            devices: overview,
            devicesByFactory: devicesByFactory,
            summary: {
                total: overview.length,
                excellent: overview.filter(d => d.healthScore >= 90).length,
                good: overview.filter(d => d.healthScore >= 80 && d.healthScore < 90).length,
                fair: overview.filter(d => d.healthScore >= 70 && d.healthScore < 80).length,
                poor: overview.filter(d => d.healthScore >= 60 && d.healthScore < 70).length,
                critical: overview.filter(d => d.healthScore < 60).length
            }
        };
    }
}

module.exports = DeviceHealth;
