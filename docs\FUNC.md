# Makrite管理系统功能说明

## 系统概述

Makrite管理系统是一个基于Node.js和Express的综合性企业管理平台，集成了申请审批、设备管理、用户管理等多个核心业务模块。系统采用现代化的Web技术架构，提供响应式设计界面，支持PC端和移动端访问，为企业提供一站式的数字化管理解决方案。

### 核心模块

#### 1. 申请审批模块
- **多级审批流程**：厂长→总监→经理→CEO的四级审批体系
- **智能路由**：支持总监直接指派CEO审批，跳过经理环节
- **特殊审批规则**：E10002经理的快速审批通道
- **附件管理**：支持多格式文件上传、下载和在线预览
- **邮件通知**：全流程自动邮件通知和智能定时提醒
- **数据归档**：已完成申请的自动归档和备份

#### 2. 设备管理模块
- **厂区管理**：多厂区配置和层级管理
- **设备信息管理**：设备全生命周期信息维护
- **维修保养记录**：三类记录管理（维修、保养、临时保养）
- **设备健康度评估**：基于AI算法的设备状态评估
- **数据导出**：Excel格式的综合数据导出功能

#### 3. 用户管理模块
- **多角色权限**：八种用户角色的精细化权限控制
- **批量操作**：用户数据的批量导入导出
- **电子签名**：集成数字签名功能
- **部门管理**：多部门组织架构支持，优化表格显示和数据管理

### 技术特色

- **响应式设计**：自适应PC端、平板和手机端
- **实时数据同步**：前后端数据实时同步更新
- **智能提醒系统**：基于优先级的分级提醒机制
- **安全性保障**：多层次的权限验证和数据保护
- **高性能优化**：数据缓存、压缩传输、分页加载
- **可扩展架构**：模块化设计，便于功能扩展
- **网络诊断**：智能网络连接检测和代理兼容性
- **待办事项管理**：基于角色的智能待办事项系统

## 用户角色与权限

系统采用基于角色的访问控制（RBAC），支持以下八种用户角色：

### 1. 普通用户（user）
**申请管理权限：**
- 提交新申请并上传附件
- 查看自己提交的所有申请
- 修改/删除待厂长审核状态的申请
- 接收申请状态变更的邮件通知

**系统访问权限：**
- 访问申请提交和历史记录页面
- 查看个人申请的详细信息和审批进度

### 2. 厂长（director）
**审批权限：**
- 审批分配给自己的待审核申请
- 批准或拒绝申请，并添加审批意见
- 上传审批相关附件和电子签名
- 查看所有需要厂长审批的申请

**系统访问权限：**
- 访问待审核和已审核申请页面
- 查看申请详情和审批历史记录

### 3. 总监（chief）
**审批权限：**
- 审批已通过厂长审批的申请
- 批准或拒绝申请，并添加审批意见
- 选择后续审批的经理（可选择多个或跳过）
- 上传审批相关附件和电子签名

**特殊权限：**
- 可直接将申请流转给CEO，跳过经理审批
- 部门自动设置为"经理室"

### 4. 经理（manager）
**审批权限：**
- 审批总监分配给自己的申请
- 批准或拒绝申请，并添加审批意见
- 上传审批相关附件和电子签名
- 查看分配给自己的所有申请

**特殊规则：**
- 用户代码为"E10002"的经理具有快速审批通道
- 当E10002经理参与审批时，所有经理完成审批后申请直接通过

### 5. CEO（ceo）
**审批权限：**
- CEO审批权限，处理经理审批后的申请
- 批准或拒绝申请，并添加审批意见
- 上传审批相关附件和电子签名
- 查看所有需要CEO审批的申请

**特殊功能：**
- 当申请金额超过100,000时，审批通过后自动通知所有只读用户
- 具有最终决策权，审批结果为最终结果

### 6. 管理员（admin）
**用户管理权限：**
- 添加、修改、删除用户账户
- 批量导入导出用户数据
- 管理用户角色和权限分配
- 重置用户密码和用户代码

**申请管理权限：**
- 查看所有申请（不限状态和申请人）
- 修改任何状态的申请信息
- 删除任何状态的申请记录
- **注意：管理员没有审批权限**

**设备管理权限：**
- 厂区信息的增删改查
- 设备信息的全生命周期管理
- 维修/保养记录的管理
- 设备健康度评估查看
- 数据导出功能（Excel格式）

**系统管理权限：**
- 系统配置和参数设置
- 数据备份和恢复
- 系统日志查看

### 7. 机电部用户（mechanical）
**设备管理权限：**
- 查看所有设备信息和厂区信息
- 添加、修改、删除维修保养记录
- 只能修改自己创建的维修保养记录
- 导出设备和维修保养数据

**系统访问权限：**
- 访问设备管理相关页面
- 查看设备健康度评估
- 接收设备相关的待办事项通知

**限制：**
- 不能修改设备基本信息
- 不能管理厂区信息
- 不能进行申请审批操作

### 8. 只读用户（readonly）
**查看权限：**
- 查看所有申请信息（只读）
- 查看申请详情和审批记录
- 接收特定条件下的邮件通知

**限制：**
- 不能提交、修改或删除申请
- 不能进行任何审批操作
- 不能访问管理功能

## 审批流程

系统实现了灵活的多级审批流程，支持标准四级审批和特殊审批规则：

### 标准审批流程

#### 1. 申请提交阶段
**操作流程：**
- 用户填写申请信息（标题、内容、金额、优先级等）
- 选择审批厂长（支持多选）
- 上传相关附件文件
- 添加电子签名（可选）

**系统处理：**
- 自动生成唯一申请编号（基于时间戳）
- 申请状态设置为"待厂长审核"
- 发送邮件通知所有选定的厂长
- 启动定时提醒机制

#### 2. 厂长审批阶段
**审批权限：**
- 只有被指定的厂长可以审批该申请
- 支持多个厂长并行审批

**操作选项：**
- 批准：添加审批意见、上传附件、电子签名
- 拒绝：必须填写拒绝原因

**流程控制：**
- 任一厂长拒绝 → 申请状态变为"已拒绝"，流程结束
- 所有厂长批准 → 申请状态变为"待总监审批"
- 系统发送邮件通知总监

#### 3. 总监审批阶段
**审批权限：**
- 系统中所有总监角色用户均可审批

**关键决策：**
- 批准或拒绝申请
- 选择后续审批经理（可选择多个或不选择）

**流程分支：**
- 拒绝 → 申请状态变为"已拒绝"，流程结束
- 批准且选择经理 → 申请状态变为"待经理审批"
- 批准且未选择经理 → 申请状态变为"待CEO审批"

#### 4. 经理审批阶段
**审批权限：**
- 只有总监指定的经理可以审批
- 支持多个经理并行审批

**特殊规则检查：**
- 检查是否包含用户代码为"E10002"的经理
- 如包含E10002经理，所有经理完成审批后申请直接通过

**流程控制：**
- 任一经理拒绝 → 申请状态变为"已拒绝"
- 所有经理批准 → 根据特殊规则决定下一步

#### 5. CEO审批阶段
**审批权限：**
- 系统中所有CEO角色用户均可审批
- 具有最终决策权

**特殊处理：**
- 审批通过后检查申请金额
- 金额超过100,000时自动通知所有只读用户
- 系统自动归档申请及所有附件

### 特殊审批规则

#### E10002经理快速通道
- 当审批经理中包含用户代码为"E10002"的经理时
- 所有指定经理完成审批后，申请直接设置为"已通过"
- 跳过CEO审批环节，但保持完整的邮件通知和归档功能

#### 总监直通CEO
- 总监审批时可选择不指定任何经理
- 申请直接流转到CEO进行审批
- 适用于紧急或高级别申请

### 流程状态说明

| 状态 | 说明 | 可操作角色 |
|------|------|------------|
| 待厂长审核 | 等待指定厂长审批 | 指定厂长、申请人（可修改）、管理员 |
| 待总监审批 | 等待总监审批并选择经理 | 总监、管理员 |
| 待经理审批 | 等待指定经理审批 | 指定经理、管理员 |
| 待CEO审批 | 等待CEO审批 | CEO、管理员 |
| 已通过 | 申请审批完成 | 所有角色（只读） |
| 已拒绝 | 申请被拒绝 | 所有角色（只读） |

## 邮件通知机制

系统在以下情况下会自动发送邮件通知：

1. **申请提交后**：通知相关厂长有新申请待审批
2. **厂长审批通过后**：通知总监有新申请待审批
3. **总监审批通过后**：
   - 如果选择了经理，通知相关经理有新申请待审批
   - 如果未选择经理，通知CEO有新申请待审批
4. **经理审批通过后**：通知CEO有新申请待审批
5. **CEO审批通过后**：
   - 通知申请人申请已通过
   - 如果申请金额超过100000，还会通知所有只读用户
6. **申请被拒绝时**：通知申请人申请被拒绝，并说明拒绝原因和拒绝人

邮件通知功能具有以下特点：

- 自动重试：发送失败时自动重试，最多重试2次
- 错误处理：记录发送失败的原因，便于排查问题
- 收件人验证：确保收件人邮箱有效
- 统一格式：所有邮件使用统一的格式，包含申请编号、申请人、部门等关键信息
- 详细日志：系统记录详细的邮件发送日志，包括发送状态、收件人和错误信息

## 定时提醒邮件功能

系统内置了智能的定时提醒邮件功能，确保申请不会因为遗忘而长时间滞留在审批环节。

### 提醒机制概述

- **自动检查**：系统每30分钟自动检查一次所有待审批的申请
- **智能提醒**：根据申请优先级和等待时间自动发送提醒邮件
- **升级机制**：随着等待时间增长，提醒频率会自动增加
- **时间控制**：支持工作日和工作时间限制，避免非工作时间发送提醒

### 提醒策略

系统根据申请的**优先级**采用不同的提醒策略：

#### 高优先级申请
- **初始延迟**：4小时后开始提醒
- **普通级别**：每4小时提醒一次
- **中等级别**：每2小时提醒一次（等待24小时后）
- **紧急级别**：每1小时提醒一次（等待48小时后）

#### 中等优先级申请
- **初始延迟**：8小时后开始提醒
- **普通级别**：每8小时提醒一次
- **中等级别**：每4小时提醒一次（等待24小时后）
- **紧急级别**：每2小时提醒一次（等待48小时后）

#### 低优先级申请
- **初始延迟**：12小时后开始提醒
- **普通级别**：每12小时提醒一次
- **中等级别**：每6小时提醒一次（等待24小时后）
- **紧急级别**：每3小时提醒一次（等待48小时后）

### 提醒内容

提醒邮件包含以下信息：
- 申请基本信息（编号、申请人、部门、内容等）
- 申请优先级和当前状态
- 等待时间统计
- 紧急程度提示
- 提醒次数统计
- 直接审批链接

### 时间控制设置

系统支持灵活的时间控制配置：

#### 工作日和工作时间限制
- **启用状态**：可开启/关闭工作时间限制
- **工作日设置**：可自定义工作日（周一至周日）
- **工作时间**：可设置具体的工作时间段（如：09:00-18:00）
- **非工作时间**：在非工作时间内不会发送提醒邮件

#### 自定义日期控制
- **特殊日期**：可设置特定日期不发送提醒（如节假日）
- **灵活配置**：支持添加多个跳过日期

### 配置文件

提醒功能的配置存储在 `backend/data/reminder-settings.json` 文件中，管理员可以根据需要调整：

```json
{
  "priority": {
    "high": {
      "initialDelay": 4,
      "normalInterval": 4,
      "mediumInterval": 2,
      "urgentInterval": 1
    },
    "medium": {
      "initialDelay": 8,
      "normalInterval": 8,
      "mediumInterval": 4,
      "urgentInterval": 2
    },
    "low": {
      "initialDelay": 12,
      "normalInterval": 12,
      "mediumInterval": 6,
      "urgentInterval": 3
    }
  },
  "timeControl": {
    "workingDays": {
      "enabled": true,
      "days": [1, 2, 3, 4, 5, 6],
      "startTime": "08:00",
      "endTime": "23:00"
    },
    "customDates": {
      "enabled": false,
      "skipDates": []
    }
  }
}
```

### 系统日志

提醒系统会记录详细的运行日志：
- 提醒检查的执行时间和结果
- 每个申请的提醒决策过程
- 邮件发送的成功/失败状态
- 时间控制的生效情况
- 系统进入静默模式的提示

## 最近更新

### 功能增强
1. **CEO审批流程**
   - 添加了CEO角色作为审批环节
   - 经理审批后流转到CEO进行审批
   - 总监不选择经理时，申请直接流转到CEO审批
   - CEO角色的功能页面与经理角色一样，显示待审核和已审核页面

2. **特殊审批流程**
   - 新增特殊经理审批规则：当审核经理中包含用户代码为"E10002"的经理时，所有经理签核完成后申请直接通过，跳过CEO审批环节
   - 保持完整的邮件通知和附件归档功能
   - 添加详细的日志记录，便于追踪特殊审批流程的执行

3. **定时提醒邮件系统**
   - 新增智能定时提醒功能，每30分钟自动检查待审批申请
   - 根据申请优先级采用不同的提醒策略（高/中/低优先级）
   - 实现三级提醒升级机制：普通→中等→紧急
   - 支持工作日和工作时间控制，避免非工作时间发送提醒
   - 可通过配置文件灵活调整提醒参数

4. **特殊金额处理**
   - 当CEO完成审批且申请金额超过100000时，系统会自动向所有只读用户发送邮件提醒
   - 增强了金额解析逻辑，确保正确识别超过100000的金额
   - 添加了更详细的日志记录，便于排查问题

5. **界面优化**
   - 优化了申请详情中的状态显示，保持一致的格式
   - 改进了总监提交申请时的状态显示
   - 优化了审批附件上传功能，解决了保留上一次选择的问题

### 问题修复
1. **审批附件上传问题**
   - 修复了审批人选择附件审批后，在上传新附件的选框中会保留上一次选择的问题
   - 在审批完成后、关闭详情模态框时、打开新的申请详情时以及确认附件后，系统会自动清除文件上传控件的值

2. **邮件通知问题**
   - 修复了经理审批后错误发送通过邮件给申请人的问题
   - 确保只在CEO审批后才发送通过邮件给申请人
   - 增强了邮件发送的错误处理和日志记录

3. **用户界面问题**
   - 修复了申请书模板中电子签名图档显示的黑色边框问题
   - 优化了审批附件中上传者的显示，现在显示用户名而非角色

## 界面优化

系统进行了全面的界面优化，主要包括以下方面：

1. **用户管理页面**
   - 表格布局优化，减少右侧空白区域
   - 添加图标和视觉元素，提升用户体验
   - 优化按钮和搜索框样式，使其更加美观
   - 改进分页控件，提供更好的视觉反馈
   - 表格表头添加图标，使界面更加直观

2. **响应式设计**
   - 适配不同尺寸的屏幕，包括手机和平板
   - 移动端优化的交互体验
   - 自适应布局，确保在各种设备上都能正常显示

3. **视觉一致性**
   - 统一的色彩方案和设计语言
   - 一致的按钮样式和交互效果
   - 优化的表单和控件设计

## 设备管理系统

系统内置了完整的设备管理功能，仅管理员可以访问，包括厂区管理、设备信息管理、维修/保养记录管理和数据导出功能。

### 厂区管理

厂区管理功能允许管理员动态配置和管理生产厂区信息：

#### 主要功能
- **添加厂区**：创建新的生产厂区，设置厂区ID、名称和描述
- **编辑厂区**：修改现有厂区的名称和描述信息
- **删除厂区**：删除不再使用的厂区（需确保没有设备关联）
- **厂区统计**：显示每个厂区关联的设备数量

#### 厂区信息字段
- **厂区ID**：唯一标识符，建议使用简短英文字母（如：SR、TW、VN）
- **厂区名称**：厂区的中文名称
- **描述**：厂区的详细描述信息（可选）
- **创建时间**：厂区创建的时间戳
- **更新时间**：厂区最后修改的时间戳

#### 安全机制
- **关联检查**：删除厂区前自动检查是否有设备正在使用该厂区
- **数据验证**：确保厂区ID和名称的唯一性
- **数据备份**：修改厂区数据时自动创建备份文件

### 设备信息管理

设备信息管理提供完整的设备生命周期管理功能：

#### 设备基本信息
- **设备编号**：系统自动生成的唯一设备编号
- **设备名称**：设备的中文名称
- **设备型号**：设备的具体型号信息
- **所属厂区**：设备所在的生产厂区（支持级联选择）
- **安装位置**：设备在厂区内的具体位置
- **设备状态**：启用/停用状态管理
- **购买日期**：设备的采购日期
- **供应商**：设备供应商信息
- **保修期限**：设备保修的截止日期
- **技术参数**：设备的详细技术规格
- **备注信息**：其他相关说明

#### 设备管理功能
- **添加设备**：录入新设备信息，支持厂区级联选择
- **编辑设备**：修改现有设备的各项信息
- **删除设备**：移除不再使用的设备记录
- **批量操作**：支持批量删除多个设备
- **状态管理**：快速启用/停用设备
- **搜索筛选**：支持按设备名称、编号、厂区等条件筛选
- **分页显示**：大量设备数据的分页浏览

#### 设备统计分析
系统提供了丰富的设备统计和可视化功能：

**统计卡片**：
- **设备总数**：显示系统中所有设备的总数量
- **启用设备**：显示启用状态的设备数量和占比
- **停用设备**：显示停用状态的设备数量和占比
- **平均年龄**：计算所有设备的平均使用年限

**可视化图表**：
- **设备状态分布图**：环形图显示启用/停用设备比例
- **厂区分布图**：柱状图显示各厂区的设备数量分布
- **动态更新**：数据变化时图表自动刷新
- **交互提示**：鼠标悬停显示详细数据

**技术特性**：
- **Chart.js驱动**：使用Chart.js库实现专业级图表
- **响应式设计**：图表自适应不同屏幕尺寸
- **动画效果**：平滑的数据变化动画
- **数字动画**：统计数字的渐变动画效果
- **实时计算**：基于最新数据实时计算统计指标

#### 级联选择功能
- **厂区优先**：先选择厂区，再选择该厂区下的设备
- **动态更新**：厂区变更时自动更新设备选择列表
- **搜索支持**：设备选择支持搜索功能，提高大量设备时的选择效率

### 维修/保养记录管理

系统提供统一的维修和保养记录管理功能，支持三种记录类型：

#### 记录类型
1. **维修记录**：设备故障维修的详细记录
2. **保养记录**：定期保养维护的记录
3. **临时保养记录**：紧急或临时性保养的记录

#### 记录信息字段
- **记录编号**：系统自动生成（MR-维修、UR-保养、TR-临时保养）
- **关联设备**：选择需要维修/保养的设备
- **记录类型**：维修/保养/临时保养
- **日期时间**：维修/保养的执行时间
- **操作人员**：执行维修/保养的人员
- **详细内容**：维修/保养的具体内容描述
- **使用材料**：维修/保养过程中使用的材料
- **费用信息**：相关的费用支出
- **备注说明**：其他相关信息

#### 动态表单功能
- **类型切换**：根据选择的记录类型动态显示相应的表单字段
- **维修表单**：故障描述、维修方法、更换部件、维修费用等
- **保养表单**：保养项目、使用材料、下次保养日期等
- **临时保养表单**：保养原因、保养项目、紧急程度、使用材料等

#### 记录管理功能
- **添加记录**：创建新的维修/保养记录
- **编辑记录**：修改现有记录信息
- **删除记录**：移除错误或无效的记录
- **批量操作**：支持批量删除多条记录
- **搜索筛选**：按设备、操作人、日期范围等条件筛选
- **状态标识**：不同类型记录使用不同颜色标识（维修-红色、保养-蓝色、临时保养-绿色）

### 数据导出功能

系统提供强大的数据导出功能，支持Excel格式导出：

#### 导出类型
1. **设备信息导出**：导出所有设备的基本信息
2. **维修/保养记录导出**：导出维修和保养记录
3. **综合数据导出**：同时导出设备信息和记录数据

#### 导出筛选选项
- **记录类型筛选**：
  - 所有记录
  - 仅维修记录
  - 仅保养记录
  - 仅临时保养记录
- **日期范围筛选**：指定导出数据的时间范围
- **设备筛选**：选择特定设备的记录

#### 导出内容
**设备信息表**：
- 设备基本信息（编号、名称、型号等）
- 厂区和位置信息
- 状态和时间信息
- 维修/保养统计（各类型记录数量）

**维修/保养记录表**：
- 记录详细信息
- 关联设备信息
- 操作人员和时间
- 费用和材料信息

**统计概览表**：
- 设备总数统计
- 各厂区设备分布
- 维修/保养记录统计
- 月度趋势分析

#### 导出特性
- **直接下载**：Excel文件直接通过浏览器下载，无需临时文件
- **实时生成**：导出数据实时从数据库获取，确保数据最新
- **格式优化**：Excel表格包含表头、样式和数据验证
- **多工作表**：不同类型数据分别放在不同的工作表中
- **版本标识**：维修保养记录导出包含"SO4-09406 R:1.0"版本号
- **智能换行**：机台名称和维修记录列自动换行并调整行高
- **时间优化**：时间列宽度足够显示完整日期时间，无需手动调整
- **精确布局**：基于实际测量的列宽和行高设置，确保打印效果

### 数据存储

#### SQLite数据库存储
系统已从JSON文件存储迁移到SQLite数据库，提供更好的性能、数据完整性和并发支持：

**主数据库**：`backend/data/application_system.db`

**数据库表结构**：
- **factories表**：厂区信息存储
  - id, name, description, createdAt, updatedAt
- **devices表**：设备信息存储
  - id, code, name, model, factory, location, status, purchaseDate, supplier, warrantyDate, specifications, notes, createdAt, updatedAt
- **maintenance_records表**：维修保养记录存储
  - id, recordCode, deviceId, deviceCode, deviceName, factory, location, responsible, maintenanceType, reportedBy, reportDate, startTime, endTime, description, result, reviewer, faultSeverity, customFields, priority, status, assignedTo, completedBy, completedDate, cost, parts, solution, attachments, createdAt, updatedAt

#### 数据迁移特性
- **完整迁移**：所有历史JSON数据已成功迁移到SQLite数据库
- **字段映射**：实现了数据库字段到前端字段的智能映射
- **记录编号恢复**：维修记录编号已恢复为原始格式（厂区代码+日期+序号）
- **向后兼容**：保持与原有功能的完全兼容
- **数据备份**：迁移过程中自动创建JSON格式备份文件

#### 记录编号格式
维修保养记录使用标准化的编号格式：
- **格式**：`{厂区代码}{YYYYMMDD}{序号}`
- **示例**：
  - `TH20250611001` - 泰国知勉厂区，2025年06月11日，第1号
  - `SR20250604002` - 东莞迅安厂区，2025年06月04日，第2号
  - `ZT20250605001` - 知腾厂区，2025年06月05日，第1号
- **生成规则**：
  - 厂区代码：2位字母（TH、SR、ZT等）
  - 日期：8位数字YYYYMMDD格式
  - 序号：3位数字，从001开始，同一天同一厂区自动递增

### 系统集成

设备管理系统与主系统完全集成：

- **权限控制**：只有管理员角色可以访问设备管理功能
- **导航菜单**：设备管理功能集成在主导航菜单中
- **响应式设计**：支持PC端和移动端访问
- **统一样式**：与主系统保持一致的UI设计风格
- **错误处理**：完整的错误处理和用户提示机制

### 使用建议

1. **厂区配置**：首先配置好厂区信息，再添加设备
2. **设备编号**：系统自动生成设备编号，建议不要手动修改
3. **定期维护**：建议定期录入设备的维修和保养记录
4. **数据备份**：定期导出设备数据作为备份
5. **权限管理**：确保只有授权的管理员可以访问设备管理功能

## 设备健康度评估系统

系统内置了智能的设备健康度评估功能，通过多维度数据分析为每台设备计算健康度分数，帮助管理员制定科学的维护策略。

### 健康度评估概述

设备健康度评估系统基于设备的历史数据和当前状态，通过科学的算法模型计算出0-100分的健康度分数，并提供相应的维护建议和故障预测。

#### 评估维度
设备健康度评估包含四个核心维度：

1. **设备年龄评分（权重20%）**
   - 基于设备使用年限计算
   - 考虑设备的预期使用寿命
   - 年龄越长，分数越低

2. **维修频率评分（权重30%）**
   - 基于设备的维修记录频率
   - 维修次数越多，分数越低
   - 考虑维修的时间分布

3. **故障严重程度评分（权重30%）**
   - 基于用户在维修记录中选择的故障程度
   - 严重故障：用户选择"严重故障"
   - 一般故障：用户选择"一般故障"
   - 轻微问题：用户选择"轻微问题"
   - 向后兼容：历史记录仍使用关键词分析

4. **保养情况评分（权重20%）**
   - 基于定期保养的执行情况
   - 保养越及时，分数越高
   - 考虑保养的质量和频率

#### 健康度等级
系统将健康度分数划分为五个等级：

- **优秀（90-100分）**：设备状态良好，正常使用
- **良好（80-89分）**：设备状态较好，建议定期检查
- **一般（70-79分）**：设备需要关注，增加检查频率
- **较差（60-69分）**：设备存在问题，需要及时维护
- **危险（0-59分）**：设备状态危险，需要立即处理

### 主要功能

#### 1. 健康度概览
- **总体统计**：显示所有设备的健康度分布统计
- **等级分布**：各健康度等级的设备数量和占比
- **趋势分析**：健康度变化趋势图表
- **关键指标**：平均健康度、最低健康度设备等

#### 2. 设备健康度列表
- **按厂区分组**：设备按所属厂区进行分组显示
- **默认折叠**：厂区列表默认折叠状态，点击展开查看详情
- **健康度显示**：每台设备显示健康度分数和等级
- **颜色标识**：不同健康度等级使用不同颜色标识
- **厂区统计**：显示每个厂区各等级设备的数量统计

#### 3. 设备详情查看
- **基本信息**：设备编号、名称、位置等基本信息
- **健康度分数**：大号显示当前健康度分数和等级
- **详细评分**：显示四个维度的具体评分
- **计算过程**：鼠标悬停总分数可查看详细计算过程
- **历史数据**：设备年龄、维修次数、保养次数等统计

#### 4. 智能提示功能
- **分数计算详情**：鼠标悬停在总分数上显示完整计算过程
- **权重说明**：显示各评分维度的权重分配
- **计算公式**：展示健康度分数的计算公式
- **智能定位**：提示框智能选择显示位置，避免被遮挡
- **响应式设计**：在不同屏幕尺寸下自适应显示

#### 5. 故障预测
- **预测算法**：基于历史数据预测下次可能的故障时间
- **置信度评估**：提供预测结果的可信度评估
- **平均间隔**：计算设备故障的平均时间间隔
- **预警提醒**：对高风险设备提供预警信息

#### 6. 维护建议
- **智能建议**：根据健康度分数生成个性化维护建议
- **优先级分类**：建议按高、中、低优先级分类
- **执行时间**：建议分为立即执行、1个月内、3个月内
- **具体措施**：提供详细的维护措施和操作指导

#### 7. 维护计划
- **个性化计划**：为每台设备生成专属的维护计划
- **时间安排**：按紧急程度安排维护时间
- **资源配置**：建议所需的人员和材料资源
- **计划打印**：支持打印维护计划用于现场执行

### 技术特性

#### 1. 智能算法
- **多维度评估**：综合考虑设备的多个关键指标
- **权重优化**：基于实际经验调整各维度权重
- **动态计算**：实时根据最新数据更新健康度分数
- **预测模型**：使用机器学习算法进行故障预测

#### 2. 用户体验
- **直观显示**：使用颜色和图形直观展示健康度状态
- **交互友好**：支持点击、悬停等多种交互方式
- **响应式设计**：适配PC端和移动端设备
- **加载优化**：采用分页和懒加载提高页面性能

#### 3. 数据可视化
- **分数圆环**：使用圆环图直观显示健康度分数
- **颜色编码**：不同健康度等级使用不同颜色
- **统计图表**：提供各种统计图表和趋势分析
- **实时更新**：数据变化时自动更新显示

#### 4. 系统集成
- **数据同步**：与设备管理系统数据实时同步
- **权限控制**：支持不同用户角色的访问权限
- **API接口**：提供标准API接口供其他系统调用
- **日志记录**：完整记录健康度评估的操作日志

### 评估算法详解

#### 计算公式
```
健康度分数 = 设备年龄评分 × 20% + 维修频率评分 × 30% + 故障严重程度评分 × 30% + 保养情况评分 × 20%
```

#### 各维度评分标准

**设备年龄评分**：
- 0-2年：100分
- 2-5年：90-80分（线性递减）
- 5-10年：80-60分（线性递减）
- 10年以上：60-40分（线性递减）

**维修频率评分**：
- 年维修次数0次：100分
- 年维修次数1-2次：90-80分
- 年维修次数3-5次：80-60分
- 年维修次数6次以上：60-40分

**故障严重程度评分**：
- 无维修记录：100分
- 仅轻微问题：90-80分（用户选择"轻微问题"）
- 一般故障：80-60分（用户选择"一般故障"）
- 严重故障：60-40分（用户选择"严重故障"）
- 历史记录：基于关键词分析（向后兼容）

**保养情况评分**：
- 保养及时充分：100分
- 保养基本及时：90-80分
- 保养偶有延迟：80-60分
- 保养严重不足：60-40分

### 使用指南

#### 1. 查看健康度概览
1. 登录系统后点击"设备健康度"菜单
2. 查看整体健康度统计和分布情况
3. 关注健康度较低的设备数量和占比

#### 2. 查看设备详情
1. 在设备列表中点击"详情"按钮
2. 查看设备的详细健康度评分
3. 鼠标悬停在总分数上查看计算过程

#### 3. 制定维护计划
1. 点击设备的"维护计划"按钮
2. 查看系统生成的个性化维护建议
3. 根据优先级安排维护工作

#### 4. 故障预测分析
1. 在设备详情中查看故障预测信息
2. 关注预测置信度和平均故障间隔
3. 对高风险设备提前安排检查

### 数据要求

为确保健康度评估的准确性，需要以下数据支持：

#### 必需数据
- **设备基本信息**：购买日期、型号、厂区等
- **维修记录**：维修日期、故障程度（严重故障/一般故障/轻微问题）、维修内容
- **保养记录**：保养日期、保养项目、保养质量

#### 可选数据
- **运行参数**：设备运行时间、负载情况
- **环境数据**：温度、湿度、振动等环境参数
- **操作记录**：操作人员、操作频率等

### 故障程度评分改进

#### 新的评分方式
系统已升级为基于用户主动选择的故障程度评分方式，替代了原有的关键词分析方法：

**用户选择方式**：
- 在填写维修记录时，用户需要从以下选项中选择故障程度：
  - **严重故障**：设备完全停机、无法启动、存在安全隐患等
  - **一般故障**：设备功能异常、性能下降、需要维修等
  - **轻微问题**：设备需要调整、清洁、校准等简单处理

**评分标准**：
- 严重故障：+30分（对健康度影响最大）
- 一般故障：+15分（对健康度影响中等）
- 轻微问题：+5分（对健康度影响较小）

#### 向后兼容性
- **历史数据处理**：对于历史维修记录，系统仍使用关键词分析方法
- **无缝过渡**：新旧评分方式可以同时工作，确保数据连续性
- **数据完整性**：所有历史健康度评分保持有效

#### 改进优势
1. **更准确的评分**：避免关键词分析的主观性和不准确性
2. **标准化数据**：统一的故障程度分类确保评分一致性
3. **简化操作**：用户只需选择而不需要记住特定关键词
4. **数据质量**：必填验证确保维修记录的完整性

### 系统优势

1. **科学评估**：基于多维度数据的科学评估模型
2. **预测能力**：提供故障预测和趋势分析
3. **个性化建议**：为每台设备提供专属维护建议
4. **直观展示**：使用图形化界面直观展示健康度状态
5. **实时更新**：数据实时同步，评估结果及时更新
6. **易于使用**：简洁的用户界面，操作简单直观
7. **智能评分**：基于用户选择的故障程度，评分更加准确可靠

### 应用价值

1. **降低维护成本**：通过预测性维护减少不必要的维护支出
2. **提高设备可靠性**：及时发现和处理设备问题
3. **优化资源配置**：合理安排维护人员和材料资源
4. **延长设备寿命**：通过科学维护延长设备使用寿命
5. **提升生产效率**：减少因设备故障导致的生产中断

## Dashboard首页系统

系统提供了功能丰富的Dashboard首页，为不同角色的用户提供个性化的数据展示和快速操作入口。

### 核心功能模块

#### 1. 欢迎区域
- **智能问候**：根据当前时间显示"早上好"、"下午好"、"晚上好"
- **用户信息**：显示当前登录用户名和角色
- **角色描述**：根据用户角色显示相应的权限说明
- **实时时间**：显示当前日期和时间，包含星期信息

#### 2. 核心数据概览
Dashboard提供三个核心业务数据卡片，支持实时趋势分析：

**申请总数卡片**：
- 显示申请数量（根据用户权限过滤）
- 趋势指示器：本月vs上月申请数量变化百分比
- 权限控制：管理员和审批人员看全局数据，普通用户看个人数据

**设备总数卡片**：
- 显示设备总数（所有用户可见）
- 趋势指示器：本月vs上月新增设备变化百分比
- 基于设备入库时间计算趋势

**审批效率卡片**：
- 显示审批效率百分比
- 个人效率：审批人员看自己的审批效率
- 系统效率：管理员看整体审批效率
- 趋势指示器：本月vs上月效率变化

#### 3. 趋势指示器系统
每个数据卡片都包含智能趋势分析：

**视觉表现**：
- ↗ 绿色：数据上升趋势
- ↘ 红色：数据下降趋势
- → 灰色：数据持平

**计算逻辑**：
- 申请趋势：`((本月申请数 - 上月申请数) / 上月申请数) × 100%`
- 设备趋势：`((本月新增设备 - 上月新增设备) / 上月新增设备) × 100%`
- 效率趋势：`本月效率 - 上月效率`（百分点差值）

#### 4. 快速操作区域
根据用户角色动态显示相应的快速操作按钮：

**管理员操作**：
- 新建申请、申请记录、用户管理、设备管理、E-mail系统设置、数据导出

**厂长操作**：
- 新建申请、申请记录、待审核、已审核、设备查看、数据导出

**其他审批人员操作**：
- 待审核、已审核、设备查看、数据导出

**普通用户操作**：
- 新建申请、我的申请、设备查看、数据导出

#### 5. 数据可视化图表
提供两个核心业务图表：

**申请趋势图表**：
- 折线图显示最近申请数量趋势
- 支持悬停查看详细数据
- 空状态友好提示

**设备健康度分布图表**：
- 环形图显示设备健康度分布
- 按优秀、良好、一般、较差分类
- 颜色编码和百分比显示

#### 6. 最近活动功能
实时展示系统中的最新活动，支持多种活动类型：

**活动类型**：
1. **申请活动**（蓝色边框）：
   - 新申请提交：📄 蓝色 - "用户 提交了新申请"
   - 等待审批：⏳ 橙色 - "申请 XXX 等待审批"
   - 审批通过：✅ 绿色 - "申请 XXX 已通过审批"
   - 审批拒绝：❌ 红色 - "申请 XXX 被拒绝"

2. **维修保养活动**（紫色边框）：
   - 维修完成：🔧 红色 - "操作员 完成了 设备名 的维修工作"
   - 保养完成：🛠️ 绿色 - "操作员 完成了 设备名 的保养工作"

3. **设备活动**（绿色边框）：
   - 设备入库：📦 蓝色 - "新设备 设备名 已入库"

**功能特性**：
- 智能时间显示：刚刚/分钟前/小时前/天前/具体日期
- 权限控制：根据用户角色显示相应的活动数据
- 手动刷新：带动画效果的刷新按钮
- 自动刷新：每30秒自动更新数据
- 活动限制：只显示最近7天的活动，最多15条记录

**权限规则**：
- 管理员/只读用户：查看所有活动
- 审批人员：查看所有活动（了解系统整体状况）
- 机电部用户：查看所有维修保养记录
- 普通用户：只看自己的申请活动和自己操作的维修保养记录

#### 7. 待办事项功能
基于用户角色的智能待办事项管理系统：

**核心特性**：
- 角色化待办事项：根据用户角色生成相应的待办任务
- 优先级管理：高/中/普通/低四级优先级分类
- 智能排序：按优先级和时间自动排序
- 快速操作：点击待办事项直接跳转到相关页面
- 实时更新：自动刷新和手动刷新功能

**角色化待办事项**：
1. **管理员**：系统管理、用户管理、待审批申请统计
2. **厂长**：分配给自己的待审核申请、申请状态更新
3. **总监**：待总监审批的申请、需要选择经理的申请
4. **经理**：总监分配的待审核申请
5. **CEO**：待CEO审批的申请
6. **普通用户**：自己申请的状态更新、设备相关通知
7. **只读用户**：重要申请通知、系统公告

**功能特性**：
- 优先级视觉标识：不同颜色边框区分优先级
- 智能时间格式化：相对时间和绝对时间显示
- 自动刷新机制：每5分钟自动更新数据
- 事件驱动更新：申请状态变化时自动刷新
- 响应式设计：适配PC端和移动端

### 技术特性

#### 1. 响应式设计
- 自适应网格布局：`grid-template-columns: repeat(auto-fit, minmax(280px, 1fr))`
- 移动端优化：卡片垂直堆叠，图表高度调整
- 平板适配：2+1布局或完全响应式

#### 2. 性能优化
- 并行数据加载：概览、图表、活动数据同时加载
- 防抖处理：窗口大小变化时的图表重绘
- 内存管理：组件销毁时清理图表实例和定时器
- 懒加载：图表延迟100ms渲染确保DOM就绪

#### 3. 用户体验
- 加载状态：骨架屏效果显示数据加载中
- 错误处理：网络错误时的友好提示
- 动画效果：卡片悬停、按钮点击、刷新动画
- 即时反馈：操作成功/失败的Toast提示

#### 4. 数据安全
- 权限验证：后端API严格验证用户权限
- 数据过滤：根据用户角色过滤可见数据
- 参数校验：所有API参数进行有效性检查

### API接口

Dashboard系统提供三个核心API接口：

#### 1. 概览数据接口
```
GET /api/dashboard/overview?username={username}&role={role}
```
返回数据：
- totalApplications：申请总数
- applicationTrend：申请趋势百分比
- totalDevices：设备总数
- deviceTrend：设备趋势百分比
- approvalEfficiency：审批效率
- efficiencyTrend：效率趋势百分比

#### 2. 图表数据接口
```
GET /api/dashboard/charts?username={username}&role={role}
```
返回数据：
- applicationTrend：申请趋势图表数据
- deviceHealth：设备健康度分布数据
- departmentStats：部门统计数据

#### 3. 活动数据接口
```
GET /api/dashboard/activity?username={username}&role={role}
```
返回数据：
- activities：最近活动列表，包含文本、时间、图标、颜色、类型等信息

### 权限控制详解

#### 数据可见性规则
1. **申请数据**：
   - 管理员/只读：所有申请
   - 审批人员：所有申请（用于趋势分析）
   - 普通用户：仅自己的申请

2. **设备数据**：
   - 所有用户：可查看所有设备数据

3. **维修保养数据**：
   - 管理员/只读：所有记录
   - 审批人员：所有记录
   - 机电部：所有记录
   - 普通用户：仅自己操作的记录

4. **审批效率数据**：
   - 审批人员：个人审批效率
   - 管理员：系统整体效率
   - 其他用户：不显示该卡片

### 使用指南

#### 1. 首次访问
1. 登录系统后自动跳转到Dashboard首页
2. 系统根据用户角色自动配置显示内容
3. 数据自动加载，显示加载动画

#### 2. 数据查看
1. 查看核心数据卡片了解业务概况
2. 观察趋势指示器了解数据变化
3. 点击快速操作按钮进入相应功能模块

#### 3. 活动监控
1. 查看最近活动了解系统动态
2. 点击刷新按钮手动更新活动数据
3. 系统每30秒自动刷新数据

#### 4. 图表分析
1. 查看申请趋势图了解业务变化
2. 查看设备健康度分布了解设备状况
3. 图表支持交互操作和详细信息查看

### 系统优势

1. **个性化展示**：根据用户角色提供定制化的数据视图
2. **实时更新**：数据实时同步，确保信息准确性
3. **直观易用**：图形化界面，数据一目了然
4. **快速操作**：常用功能一键直达
5. **全面监控**：涵盖申请、设备、活动等多个维度
6. **响应式设计**：适配各种设备和屏幕尺寸

## 设备管理权限系统

系统实现了精细化的设备管理权限控制，确保不同角色用户只能访问和操作相应的功能。

### 权限规则详解

#### 1. 设备信息管理权限
**管理员（admin）**：
- 查看所有设备信息
- 添加新设备
- 修改设备信息
- 删除设备记录
- 批量操作设备

**其他所有用户**：
- 仅查看设备信息
- 不能进行任何修改操作
- 可以导出设备数据

#### 2. 维修保养记录权限
**机电部用户（mechanical）**：
- 查看所有维修保养记录
- 添加新的维修保养记录
- 修改自己创建的记录
- 删除自己创建的记录

**管理员（admin）**：
- 查看所有维修保养记录
- 添加、修改、删除任何记录
- 批量操作记录

**其他用户**：
- 仅查看维修保养记录
- 不能进行任何修改操作
- 可以导出记录数据

#### 3. 厂区管理权限
**管理员（admin）**：
- 查看所有厂区信息
- 添加新厂区
- 修改厂区信息
- 删除厂区

**其他所有用户**：
- 仅查看厂区信息
- 不能进行任何修改操作

#### 4. 数据导出权限
**所有用户**：
- 都有导出数据的权限
- 可以导出设备信息
- 可以导出维修保养记录
- 可以选择导出格式和筛选条件

### 权限实现机制

#### 1. 后端API权限验证
每个API接口都包含严格的权限检查：

```javascript
// 设备管理权限检查示例
if (role !== 'admin') {
    return res.status(403).json({
        success: false,
        message: '权限不足，只有管理员可以修改设备信息'
    });
}

// 维修保养记录权限检查示例
if (role !== 'admin' && role !== 'mechanical') {
    return res.status(403).json({
        success: false,
        message: '权限不足，只有管理员和机电部用户可以添加记录'
    });
}
```

#### 2. 前端界面权限控制
根据用户角色动态显示/隐藏操作按钮：

- 添加/编辑/删除按钮仅对有权限的用户显示
- 表单字段根据权限设置为只读或可编辑
- 批量操作功能根据权限启用/禁用

#### 3. 记录所有权验证
对于机电部用户，系统会验证记录的所有权：

- 只能修改自己创建的记录（operator字段匹配用户名）
- 删除操作同样需要验证所有权
- 管理员不受此限制，可以操作所有记录

### 审批权限系统

系统实现了基于角色的审批权限控制，确保审批流程的安全性和规范性。

#### 1. 申请提交权限
**厂长用户（director）**：
- 可以新建申请
- 可以查看申请记录
- 首页显示"新建申请"和"申请记录"快速操作

**其他审批人员（chief、manager、ceo）**：
- 不能新建申请
- 不能查看申请记录页面
- 首页不显示申请相关的快速操作

**管理员（admin）**：
- 可以新建申请
- 可以查看所有申请记录
- 拥有完整的申请管理权限

**普通用户（user）**：
- 可以新建申请
- 可以查看自己的申请记录

#### 2. 审批效率计算权限
**个人审批效率**（审批人员）：
- 基于用户实际参与的审批记录计算
- 只统计该用户作为审批人的记录
- 计算公式：`(已完成审批数 / 总参与审批数) × 100%`

**系统审批效率**（管理员）：
- 基于所有申请的整体审批进度计算
- 计算公式：`(已通过数量 + 已拒绝数量) / 总申请数量 × 100%`

#### 3. 首页快速操作权限
系统根据用户角色动态调整首页快速操作：

**厂长专属操作**：
- 新建申请
- 申请记录
- 待审核
- 已审核

**其他审批人员操作**：
- 待审核
- 已审核
- （不包含申请相关操作）

### 权限验证流程

#### 1. 登录验证
- 用户登录时验证用户名和密码
- 获取用户角色信息
- 设置会话权限上下文

#### 2. API调用验证
- 每个API调用都包含用户名和角色参数
- 后端验证用户是否存在
- 检查用户角色是否有权限执行操作

#### 3. 前端权限控制
- 根据用户角色显示/隐藏功能模块
- 动态生成菜单和操作按钮
- 实时检查用户权限状态

#### 4. 数据过滤
- 根据用户权限过滤返回的数据
- 确保用户只能看到有权限访问的信息
- 保护敏感数据不被未授权访问

### 安全机制

#### 1. 权限最小化原则
- 用户只获得完成工作所需的最小权限
- 默认拒绝，明确授权
- 定期审查和调整权限设置

#### 2. 操作日志记录
- 记录所有关键操作的日志
- 包含操作人、操作时间、操作内容
- 便于审计和问题追踪

#### 3. 错误处理
- 权限不足时返回明确的错误信息
- 不暴露系统内部结构信息
- 提供用户友好的错误提示

#### 4. 会话管理
- 安全的会话管理机制
- 定期验证用户权限状态
- 支持权限变更的实时生效

### 权限配置管理

#### 1. 角色定义
系统预定义了以下用户角色：
- admin：系统管理员
- director：厂长
- chief：总监
- manager：经理
- ceo：CEO
- mechanical：机电部用户
- user：普通用户
- readonly：只读用户

#### 2. 权限矩阵
系统维护了完整的权限矩阵，定义了每个角色对每个功能的访问权限：

| 功能模块 | admin | director | chief | manager | ceo | mechanical | user | readonly |
|----------|-------|----------|-------|---------|-----|------------|------|----------|
| 申请提交 | ✅ | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ |
| 申请审批 | ❌ | ✅ | ✅ | ✅ | ✅ | ❌ | ❌ | ❌ |
| 设备管理 | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ | ❌ |
| 维修记录 | ✅ | ❌ | ❌ | ❌ | ❌ | ✅ | ❌ | ❌ |
| 数据导出 | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ | ✅ |

#### 3. 动态权限调整
- 支持运行时权限调整
- 权限变更立即生效
- 提供权限变更的审计日志

### 最佳实践

#### 1. 权限设计原则
- 职责分离：不同角色承担不同职责
- 最小权限：只授予必要的权限
- 权限继承：高级角色继承低级角色权限
- 明确授权：权限授予必须明确和可追溯

#### 2. 安全建议
- 定期审查用户权限
- 及时回收离职人员权限
- 监控异常权限使用
- 保持权限配置的文档化

#### 3. 用户体验
- 提供清晰的权限说明
- 友好的权限不足提示
- 合理的功能分组和布局
- 直观的权限状态显示


#### 1. 数据库系统迁移
**从JSON到SQLite的完整迁移**：
- **数据库引擎**：采用better-sqlite3作为SQLite数据库实现
- **迁移范围**：所有业务数据（申请、用户、设备、厂区、部门、维修记录）
- **数据完整性**：100%数据迁移成功，无数据丢失
- **性能提升**：数据库查询性能显著提升，支持复杂查询和事务处理
- **并发支持**：支持多用户并发访问，解决JSON文件的并发限制

**数据库表结构设计**：
- **规范化设计**：采用第三范式设计，减少数据冗余
- **索引优化**：为常用查询字段添加索引，提升查询性能
- **外键约束**：建立表间关联关系，确保数据一致性
- **字段类型优化**：使用合适的数据类型，节省存储空间

#### 2. 维修记录管理系统修复
**记录编号格式恢复**：
- **问题识别**：数据库迁移后维修记录编号格式错误（MR+ID格式）
- **格式修复**：恢复为原始业务格式（厂区代码+日期+序号）
- **数据修复脚本**：创建专门的修复脚本，从JSON备份中恢复正确编号
- **成功修复记录**：7条历史记录编号全部恢复正确

**字段映射优化**：
- **数据访问层改进**：实现SQLite字段到前端字段的智能映射
- **兼容性保证**：确保前端代码无需修改，保持向后兼容
- **空值处理**：优化空值显示，避免"undefined"问题
- **新字段支持**：添加startTime、endTime、result、reviewer等新字段

**前端显示修复**：
- **表格显示优化**：修复维修记录表格中的"undefined"显示问题
- **字段验证**：为所有可能为空的字段添加空值检查
- **用户体验**：空值统一显示为"-"，提供一致的用户体验
- **模态框修复**：修复编辑模态框中的字段显示问题

#### 3. 数据库架构优化
**表结构完善**：
- **maintenance_records表增强**：添加recordCode、startTime、endTime、result、reviewer、faultSeverity、customFields等字段
- **字段类型优化**：使用合适的数据类型（TEXT、DATETIME、JSON等）
- **默认值设置**：为新字段设置合理的默认值
- **数据迁移兼容**：确保新字段与历史数据兼容

**API接口优化**：
- **CRUD操作完善**：优化增删改查操作，支持新字段
- **事务处理**：使用数据库事务确保数据一致性
- **错误处理**：完善的错误处理和回滚机制
- **性能优化**：优化SQL查询，减少数据库访问次数

#### 4. 系统稳定性提升
**数据备份机制**：
- **自动备份**：数据迁移时自动创建JSON格式备份
- **备份验证**：验证备份数据的完整性和正确性
- **恢复机制**：提供数据恢复功能，确保数据安全
- **版本控制**：备份文件包含时间戳，支持多版本管理

**错误处理改进**：
- **数据库连接**：优化数据库连接管理，防止连接泄露
- **异常捕获**：完善的异常捕获和处理机制
- **日志记录**：详细的操作日志，便于问题排查
- **用户提示**：友好的错误提示信息



#### 1. 部门管理界面优化
**表格布局和显示优化**：
- 表格列宽精确控制：使用`table-fixed`布局和`colgroup`精确控制列宽分配
  - 部门名称：15%，部门代码：12%，描述：40%，用户数量：13%，操作：20%
- 防止文字换行：添加`whitespace-nowrap`确保标题和操作按钮横向显示完整
- 紧凑布局设计：减少单元格内边距从`p-3`到`p-2`，提升表格紧凑性

**部门描述内容优化**：
- 描述文本精简：将冗长的部门描述缩短为简洁的关键词组合
  - 示例：财务部描述从"负责公司财务管理、会计核算、资金管理、成本控制等财务相关工作"
  - 优化为："财务管理、会计核算、资金管理、成本控制"
- 智能文本截断：添加`truncateDescription()`方法，超过20字符自动截断显示省略号
- 悬停提示功能：使用`title`属性显示完整描述，鼠标悬停可查看详细内容

**CSS样式专门优化**：
- 专用样式类：为部门管理表格添加专门的CSS样式类
- 滚动容器：设置最大高度400px，超出内容自动滚动
- 操作按钮优化：使用更小字体`text-sm`和紧凑间距`space-x-1`
- 描述列特殊处理：设置最大宽度和文本溢出处理，添加帮助光标

**数据完善和标准化**：
- 部门代码补全：为之前没有代码的部门添加合适的英文缩写
  - 船务部：SHP，测试部门：TST
- 描述标准化：所有部门都有简洁明了的职能描述
- 数据一致性：确保所有部门信息的完整性和规范性

#### 2. Excel导出功能全面优化
**维修保养记录导出格式改进**：
- 添加版本号标识：在Excel表格右上方显示"SO4-09406 R:1.0"
- 优化表格布局：版本号单独占第1行，主标题移至第2行
- 时间列宽度优化：开始时间和结束时间列宽从2.75cm增加到3.5cm
- 完整时间显示：确保"2025/06/06 19:38"格式的时间完整显示，无需双击
- 时间格式标准化：统一不显示秒，只显示到分钟

**自动换行和行高调整功能**：
- 智能文本行数计算：新增`calculateTextLines()`方法
- 动态行高调整：根据机台名称和维修/保养记录内容长度自动调整行高
- 中英文字符识别：中文字符占2个字符宽度，英文字符占1个字符宽度
- 多段落支持：支持包含换行符的多段落文本自动换行
- 基础行高0.46cm，每增加一行增加0.46cm

**代码优化和重构**：
- 删除重复的导出方法：移除未使用的`exportMaintenanceToExcel()`方法
- 保留核心方法：只保留`exportMaintenanceToExcelBuffer()`方法
- 减少代码维护成本：消除功能重复，提高代码质量
- 统一导出接口：所有导出功能使用Buffer方式直接下载

**Excel表格格式标准化**：
- 列宽精确控制：基于实际测量的字符/厘米转换系数(5.17)
- 表格布局优化：
  - 第1行：版本号"SO4-09406 R:1.0"(右上方)
  - 第2行：主标题"维修及保养记录"(居中)
  - 第3行：列标题行
  - 第4行开始：数据行
- 自动换行列：机台名称和维修/保养记录列支持自动换行
- 边框和样式：统一的表格边框和字体样式

#### 2. UI交互体验优化
**模态框自适应高度控制**：
- 智能高度计算：模态框高度自动适配视口大小（最大90%）
- 内容区域滚动：超长内容自动启用滚动条
- 响应式调整：窗口大小变化时自动重新计算高度
- 多模态框支持：支持详情、编辑、预览等多种模态框类型

**申请内容弹窗显示**：
- 长文本优化：申请内容超过80字符时显示省略号
- 点击查看详情：点击截断内容弹出完整内容窗口
- 智能换行：保持原始格式的文本换行显示
- 键盘快捷键：支持ESC键关闭弹窗

**交互体验改进**：
- 背景点击关闭：点击模态框背景自动关闭
- 防止背景滚动：模态框打开时锁定背景页面滚动
- 滚动位置重置：关闭模态框时重置内容滚动位置
- 视觉反馈优化：hover效果和点击状态反馈

### 2025年6月功能增强

#### 1. Dashboard首页系统重构
**核心数据概览优化**：
- 删除了无意义的系统健康度卡片（原为固定值100%）
- 保留三个核心业务卡片：申请总数、设备总数、审批效率
- 实现了真实的趋势计算功能：
  - 设备趋势：基于本月vs上月新增设备数量计算
  - 审批效率趋势：基于本月vs上月效率变化计算
  - 申请趋势：基于本月vs上月申请数量计算

**最近活动功能完整实现**：
- 支持三种活动类型：申请、维修保养、设备入库
- 智能时间显示：刚刚/分钟前/小时前/天前/具体日期
- 活动类型视觉区分：不同颜色边框和图标
- 权限控制：根据用户角色显示相应活动
- 手动刷新功能：带动画效果的刷新按钮
- 自动刷新：每30秒自动更新数据
- 活动限制：最近7天，最多15条记录

**响应式设计优化**：
- 三卡片自适应布局，删除系统健康度后布局更加合理
- 移动端优化的活动列表显示
- 图表和卡片的响应式适配

#### 2. 设备管理权限系统完善
**精细化权限控制**：
- 设备管理页面：管理员可编辑，其他用户只读
- 维修保养记录：机电部用户可编辑自己的记录，其他用户只读
- 厂区管理：只有管理员可以编辑
- 数据导出：所有用户都有权限

**权限验证机制**：
- 后端API严格权限验证
- 前端界面动态权限控制
- 记录所有权验证（机电部用户只能修改自己的记录）

#### 3. 审批权限系统优化
**申请权限规则调整**：
- 厂长用户：可以新建申请和查看申请记录
- 其他审批人员（总监、经理、CEO）：只有审批权限，无申请权限
- 首页快速操作根据权限动态调整

**审批效率计算修复**：
- 个人审批效率：基于用户实际参与的审批记录计算
- 系统审批效率：基于所有申请的整体审批进度计算
- 修复了总监审批时approverUsername记录问题
- 兼容历史数据，确保效率计算准确性

#### 4. 用户体验改进
**界面交互优化**：
- 活动刷新按钮的旋转动画效果
- Toast提示消息系统
- 加载状态和错误处理优化
- 图表空状态友好提示

**数据展示优化**：
- 趋势指示器的颜色和图标优化
- 活动时间的智能格式化
- 卡片悬停效果和视觉反馈
- 滚动条美化和交互优化

#### 5. 技术架构改进
**代码组织优化**：
- 模块化的Dashboard管理器类
- 分离的API接口设计
- 统一的错误处理机制
- 内存管理和性能优化

**数据处理优化**：
- 并行数据加载提高页面性能
- 防抖处理减少不必要的API调用
- 数据缓存和实时同步机制
- 权限数据的安全过滤

### 功能完整性总结

#### 已完成的核心功能
1. **申请审批系统**：✅ 完整实现
   - 多级审批流程
   - 特殊审批规则
   - 邮件通知系统
   - 定时提醒功能

2. **设备管理系统**：✅ 完整实现
   - 厂区管理
   - 设备信息管理
   - 维修保养记录
   - 设备健康度评估
   - 数据导出功能

3. **用户管理系统**：✅ 完整实现
   - 多角色权限控制
   - 用户信息管理
   - 批量操作功能

4. **Dashboard首页**：✅ 完整实现
   - 核心数据概览
   - 趋势分析
   - 快速操作
   - 数据可视化
   - 最近活动功能

5. **权限控制系统**：✅ 完整实现
   - 基于角色的访问控制
   - 精细化权限管理
   - 数据安全保护

#### 系统特色功能
1. **智能审批流程**：支持复杂的多级审批和特殊规则
2. **设备健康度评估**：基于AI算法的设备状态评估
3. **实时数据同步**：前后端数据实时同步更新
4. **响应式设计**：完美适配PC、平板、手机端
5. **权限精细控制**：基于角色的精细化权限管理
6. **数据可视化**：丰富的图表和趋势分析
7. **用户体验优化**：流畅的交互和友好的界面设计
8. **网络诊断系统**：智能网络连接检测和代理兼容性
9. **待办事项管理**：基于角色的智能待办事项系统
10. **Excel导出优化**：专业级表格格式和自动换行功能
11. **UI交互优化**：模态框自适应高度和内容弹窗显示
12. **响应式布局**：完美适配各种设备和屏幕尺寸
13. **部门管理优化**：紧凑表格布局、智能文本截断、专用样式设计

#### 技术优势
1. **模块化架构**：清晰的代码组织和模块分离
2. **安全性保障**：多层次的权限验证和数据保护
3. **性能优化**：数据缓存、懒加载、防抖处理
4. **可扩展性**：易于扩展的架构设计
5. **维护性**：完善的错误处理和日志记录
6. **兼容性**：跨浏览器和设备的良好兼容性

### 系统成熟度评估

**功能完整性**：99% - 核心业务功能全部实现，Excel导出、UI优化、部门管理、统计分析功能完善
**用户体验**：97% - 界面友好，交互流畅，模态框自适应，内容弹窗优化，部门管理表格布局优化
**系统稳定性**：95% - 完善的错误处理和异常管理
**安全性**：90% - 多层次的权限控制和数据保护
**性能表现**：90% - 良好的响应速度和资源利用，代码优化减少冗余
**可维护性**：96% - 清晰的代码结构和文档，删除重复代码，模块化设计

**总体评估**：系统已达到生产环境部署标准，具备完整的企业级管理功能。最新的部门管理界面优化、Excel导出功能完善、UI交互优化、网络诊断和待办事项管理进一步提升了系统的专业性和用户体验。部门管理模块的表格布局优化确保了在有限空间内的最佳信息展示效果。

## 网络诊断系统

系统内置了智能的网络诊断工具，确保在各种网络环境下都能稳定运行，特别针对代理网络和VPN用户进行了优化。

### 核心功能

#### 1. 网络连接检测
- **实时监控**：持续监控网络连接状态
- **连接质量评估**：测量网络延迟和响应时间
- **自动重连**：网络恢复后自动重试失败的请求
- **状态通知**：网络状态变化时显示用户友好的通知

#### 2. 代理兼容性检测
- **代理识别**：自动检测是否通过代理服务器访问
- **IP地址分析**：分析客户端IP、转发IP和真实IP
- **兼容性验证**：确保代理环境下的功能完整性
- **调试信息**：提供详细的网络诊断信息

#### 3. 网络质量监控
- **延迟测试**：定期测试服务器响应延迟
- **连接稳定性**：监控连接的稳定性和可靠性
- **性能指标**：收集网络性能数据
- **质量报告**：生成网络质量评估报告

### 技术特性

#### 1. 智能检测算法
- **多重验证**：使用多种方法验证网络状态
- **超时控制**：合理的超时设置避免长时间等待
- **错误处理**：完善的错误处理和降级机制
- **兼容性保障**：支持各种浏览器和网络环境

#### 2. 用户体验优化
- **非侵入式**：后台运行，不影响正常使用
- **智能通知**：只在必要时显示通知信息
- **快速响应**：快速检测和响应网络变化
- **友好提示**：提供清晰的网络状态说明

#### 3. 企业级支持
- **代理友好**：完全支持企业代理环境
- **VPN兼容**：与各种VPN解决方案兼容
- **防火墙适配**：适应企业防火墙配置
- **安全保障**：不收集敏感网络信息

### API接口

#### 健康检查接口
```
GET /api/health
```
返回数据：
- status：服务器状态
- timestamp：检查时间戳
- uptime：服务器运行时间
- server：服务器配置信息
- network：网络连接信息

### 使用场景

#### 1. 企业环境部署
- **代理网络**：企业内部通过代理服务器访问
- **VPN连接**：远程办公通过VPN访问系统
- **防火墙环境**：严格的网络安全策略环境
- **多地部署**：跨地区的分布式部署

#### 2. 网络故障诊断
- **连接问题**：快速定位网络连接问题
- **性能问题**：识别网络性能瓶颈
- **兼容性问题**：解决代理和VPN兼容性问题
- **配置问题**：协助网络配置优化

### 配置选项

系统提供灵活的网络诊断配置：
- **检测间隔**：可调整的网络质量检测间隔
- **超时设置**：可配置的连接超时时间
- **通知控制**：可开启/关闭网络状态通知
- **调试模式**：详细的调试信息输出

## 待办事项管理系统

系统提供了完整的待办事项管理功能，根据用户角色智能生成个性化的待办任务，提升工作效率。

### 核心架构

#### 1. 前端架构
- **独立模块设计**：`TodoManager`类独立管理待办事项
- **响应式界面**：适配PC端和移动端的待办事项显示
- **实时更新**：支持手动刷新和自动刷新机制
- **事件驱动**：基于系统事件的智能更新

#### 2. 后端架构
- **角色化生成**：根据用户角色生成相应的待办事项
- **权限控制**：严格的权限验证和数据过滤
- **实时计算**：基于最新数据动态生成待办任务
- **优先级算法**：智能的优先级计算和排序

### 角色化待办事项

#### 1. 管理员待办事项
- **系统管理**：待审批申请统计、长时间未处理申请提醒
- **用户管理**：非活跃用户提醒、权限异常检查
- **设备管理**：设备健康度异常、维修记录统计
- **数据监控**：系统性能监控、数据备份提醒

#### 2. 审批人员待办事项
- **厂长**：分配给自己的待审核申请、紧急申请特别标注
- **总监**：待总监审批的申请、需要选择经理的申请
- **经理**：总监分配的待审核申请、按紧急程度排序
- **CEO**：待CEO审批的申请、高金额申请特别提醒

#### 3. 普通用户待办事项
- **申请状态**：自己申请的状态更新通知
- **设备相关**：设备维修保养通知（机电部用户）
- **系统通知**：重要系统公告和更新提醒
- **个人任务**：个人相关的工作任务

### 功能特性

#### 1. 智能优先级管理
- **四级优先级**：高/中/普通/低四级优先级分类
- **动态计算**：根据任务紧急程度和重要性动态调整
- **视觉标识**：不同颜色边框区分优先级
- **自动排序**：按优先级和时间自动排序显示

#### 2. 快速操作支持
- **一键跳转**：点击待办事项直接跳转到相关页面
- **批量操作**：支持批量标记已读、清除已完成
- **快捷键**：支持键盘快捷键操作
- **上下文菜单**：右键菜单提供更多操作选项

#### 3. 实时更新机制
- **自动刷新**：每5分钟自动更新待办事项数据
- **事件驱动**：申请状态变化时立即更新
- **手动刷新**：提供手动刷新按钮
- **增量更新**：只更新变化的数据，提高性能

### API接口

#### 获取待办事项接口
```
GET /api/dashboard/todos?username={username}&role={role}
```
返回数据：
- todos：待办事项列表
- 每个待办事项包含：id、text、time、priority、icon、iconColor、actionUrl

#### 待办事项操作接口
```
POST /api/todos/mark-all-read
POST /api/todos/clear-completed
```

### 使用指南

#### 1. 查看待办事项
1. 登录系统后进入Dashboard首页
2. 在"最新动态"区域查看"待办事项"卡片
3. 待办事项按优先级自动排序显示

#### 2. 处理待办事项
1. 点击待办事项可快速跳转到相关页面
2. 处理完成后系统会自动更新待办列表
3. 可手动点击刷新按钮更新数据

#### 3. 优先级识别
- **红色边框**：高优先级，需要立即处理
- **橙色边框**：中等优先级，需要及时处理
- **蓝色边框**：普通优先级，按计划处理
- **灰色边框**：低优先级，信息类通知

### 安全特性

#### 1. 权限验证
- **API级别**：每个请求都验证用户身份和角色
- **数据过滤**：严格按照用户角色过滤待办事项
- **操作限制**：只显示用户有权限的操作

#### 2. 数据保护
- **敏感信息**：不在前端暴露敏感数据
- **会话管理**：与现有会话管理系统集成
- **审计日志**：记录重要操作的审计日志
