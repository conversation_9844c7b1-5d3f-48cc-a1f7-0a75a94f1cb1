// 设备统计和图表展示模块
class DeviceStats {
    constructor() {
        this.statusChart = null;
        this.factoryChart = null;
        this.devices = [];
        this.factories = [];
        this.isInitialized = false;
    }

    // 初始化
    init() {
        if (this.isInitialized) {
            return;
        }
        this.bindEvents();
        this.isInitialized = true;
    }

    // 绑定事件
    bindEvents() {
        // 厂区图表刷新按钮
        const factoryChartRefresh = document.getElementById('factoryChartRefresh');
        if (factoryChartRefresh) {
            factoryChartRefresh.addEventListener('click', () => {
                this.updateFactoryChart();
            });
        }
    }

    // 更新统计数据
    updateStats(devices, factories) {
        this.devices = devices || [];
        this.factories = factories || [];
        
        this.updateStatsCards();
        this.updateStatusChart();
        this.updateFactoryChart();
    }

    // 更新统计卡片
    updateStatsCards() {
        const total = this.devices.length;
        const active = this.devices.filter(device => device.status === '启用').length;
        const inactive = this.devices.filter(device => device.status === '停用').length;
        const averageAge = this.calculateAverageAge();

        // 更新总数
        const totalElement = document.getElementById('totalDevicesCount');
        if (totalElement) {
            this.animateNumber(totalElement, total);
        }

        // 更新启用设备
        const activeElement = document.getElementById('activeDevicesCount');
        const activePercentageElement = document.getElementById('activeDevicesPercentage');
        if (activeElement) {
            this.animateNumber(activeElement, active);
        }
        if (activePercentageElement) {
            const percentage = total > 0 ? Math.round((active / total) * 100) : 0;
            activePercentageElement.textContent = `${percentage}%`;
        }

        // 更新停用设备
        const inactiveElement = document.getElementById('inactiveDevicesCount');
        const inactivePercentageElement = document.getElementById('inactiveDevicesPercentage');
        if (inactiveElement) {
            this.animateNumber(inactiveElement, inactive);
        }
        if (inactivePercentageElement) {
            const percentage = total > 0 ? Math.round((inactive / total) * 100) : 0;
            inactivePercentageElement.textContent = `${percentage}%`;
        }

        // 更新平均年龄
        const averageAgeElement = document.getElementById('averageDeviceAge');
        if (averageAgeElement) {
            averageAgeElement.textContent = averageAge.toFixed(1);
        }
    }

    // 计算设备平均年龄
    calculateAverageAge() {
        if (this.devices.length === 0) return 0;

        const today = new Date();
        let totalAge = 0;
        let validDevices = 0;

        this.devices.forEach(device => {
            if (device.entryDate) {
                const entryDate = new Date(device.entryDate);
                if (!isNaN(entryDate.getTime())) {
                    const ageInYears = (today - entryDate) / (1000 * 60 * 60 * 24 * 365.25);
                    totalAge += ageInYears;
                    validDevices++;
                }
            }
        });

        return validDevices > 0 ? totalAge / validDevices : 0;
    }

    // 数字动画效果
    animateNumber(element, targetValue) {
        const currentValue = parseInt(element.textContent) || 0;
        const increment = Math.ceil((targetValue - currentValue) / 20);
        
        if (currentValue === targetValue) return;

        const timer = setInterval(() => {
            const newValue = parseInt(element.textContent) + increment;
            if ((increment > 0 && newValue >= targetValue) || (increment < 0 && newValue <= targetValue)) {
                element.textContent = targetValue;
                clearInterval(timer);
            } else {
                element.textContent = newValue;
            }
        }, 50);
    }

    // 检查Chart.js是否可用
    isChartJsAvailable() {
        return typeof Chart !== 'undefined' && Chart.Chart !== undefined;
    }

    // 更新设备状态分布图
    updateStatusChart() {
        const canvas = document.getElementById('deviceStatusChart');
        if (!canvas) return;

        // 检查Chart.js是否可用
        if (!this.isChartJsAvailable()) {
            console.error('Chart.js库未加载，无法创建设备状态图表');
            return;
        }

        const ctx = canvas.getContext('2d');

        // 销毁现有图表
        if (this.statusChart) {
            this.statusChart.destroy();
        }

        const active = this.devices.filter(device => device.status === '启用').length;
        const inactive = this.devices.filter(device => device.status === '停用').length;

        this.statusChart = new Chart(ctx, {
            type: 'doughnut',
            data: {
                labels: ['启用', '停用'],
                datasets: [{
                    data: [active, inactive],
                    backgroundColor: ['#10B981', '#EF4444'],
                    hoverBackgroundColor: ['#059669', '#DC2626'],
                    borderWidth: 0,
                    cutout: '65%'
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: 10
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((context.parsed / total) * 100) : 0;
                                return `${context.label}: ${context.parsed}台 (${percentage}%)`;
                            }
                        }
                    }
                },
                animation: {
                    animateRotate: true,
                    duration: 800
                }
            }
        });
    }

    // 更新厂区分布图
    updateFactoryChart() {
        const canvas = document.getElementById('factoryDistributionChart');
        if (!canvas) return;

        // 检查Chart.js是否可用
        if (!this.isChartJsAvailable()) {
            console.error('Chart.js库未加载，无法创建厂区分布图表');
            return;
        }

        const ctx = canvas.getContext('2d');
        
        // 销毁现有图表
        if (this.factoryChart) {
            this.factoryChart.destroy();
        }

        // 统计各厂区设备数量
        const factoryStats = this.getFactoryStats();

        // 如果没有数据，显示空状态
        if (factoryStats.labels.length === 0) {
            factoryStats.labels = ['暂无数据'];
            factoryStats.data = [0];
        }

        // 创建渐变色
        const gradient = ctx.createLinearGradient(0, 0, 0, 200);
        gradient.addColorStop(0, '#3B82F6');
        gradient.addColorStop(1, '#60A5FA');

        this.factoryChart = new Chart(ctx, {
            type: 'bar',
            data: {
                labels: factoryStats.labels,
                datasets: [{
                    label: '设备数量',
                    data: factoryStats.data,
                    backgroundColor: gradient,
                    borderColor: '#1D4ED8',
                    borderWidth: 1,
                    borderRadius: 6,
                    borderSkipped: false,
                    barThickness: 32,
                    maxBarThickness: 40
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                layout: {
                    padding: {
                        top: 10,
                        bottom: 5,
                        left: 5,
                        right: 5
                    }
                },
                plugins: {
                    legend: {
                        display: false
                    },
                    tooltip: {
                        backgroundColor: 'rgba(0, 0, 0, 0.8)',
                        titleColor: '#fff',
                        bodyColor: '#fff',
                        borderColor: 'rgba(255, 255, 255, 0.1)',
                        borderWidth: 1,
                        cornerRadius: 6,
                        displayColors: false,
                        callbacks: {
                            label: function(context) {
                                return `设备数量: ${context.parsed.y}台`;
                            },
                            afterLabel: function(context) {
                                const total = context.dataset.data.reduce((a, b) => a + b, 0);
                                const percentage = total > 0 ? Math.round((context.parsed.y / total) * 100) : 0;
                                return `占比: ${percentage}%`;
                            }
                        }
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        grid: {
                            color: 'rgba(0, 0, 0, 0.05)',
                            drawBorder: false
                        },
                        ticks: {
                            stepSize: 1,
                            color: '#6B7280',
                            font: {
                                size: 11
                            }
                        }
                    },
                    x: {
                        grid: {
                            display: false
                        },
                        ticks: {
                            color: '#6B7280',
                            font: {
                                size: 11
                            },
                            maxRotation: 0,
                            minRotation: 0
                        },
                        categoryPercentage: 0.6,
                        barPercentage: 0.8
                    }
                },
                animation: {
                    duration: 800,
                    easing: 'easeInOutQuart'
                }
            }
        });
    }

    // 获取厂区统计数据
    getFactoryStats() {
        const factoryMap = new Map();
        
        // 初始化所有厂区为0
        this.factories.forEach(factory => {
            factoryMap.set(factory.id, {
                name: factory.name,
                count: 0
            });
        });

        // 统计各厂区设备数量
        this.devices.forEach(device => {
            if (device.factory && factoryMap.has(device.factory)) {
                const factory = factoryMap.get(device.factory);
                factory.count++;
            }
        });

        // 转换为图表数据格式
        const labels = [];
        const data = [];
        
        factoryMap.forEach(factory => {
            labels.push(factory.name);
            data.push(factory.count);
        });

        return { labels, data };
    }

    // 获取厂区名称
    getFactoryName(factoryId) {
        const factory = this.factories.find(f => f.id === factoryId);
        return factory ? factory.name : factoryId || '未知厂区';
    }

    // 销毁图表
    destroy() {
        if (this.statusChart) {
            this.statusChart.destroy();
            this.statusChart = null;
        }
        if (this.factoryChart) {
            this.factoryChart.destroy();
            this.factoryChart = null;
        }
    }
}

// 全局实例
let deviceStats = null;

// 初始化设备统计模块
function initDeviceStats() {
    if (!deviceStats) {
        deviceStats = new DeviceStats();
        deviceStats.init();
    }
    return deviceStats;
}

// 导出模块
if (typeof module !== 'undefined' && module.exports) {
    module.exports = DeviceStats;
}
