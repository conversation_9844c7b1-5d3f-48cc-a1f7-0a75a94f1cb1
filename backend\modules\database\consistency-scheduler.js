/**
 * 数据一致性检查调度器
 * 定期执行数据一致性检查，确保系统数据完整性
 */
class ConsistencyScheduler {
    constructor(dataConsistencyChecker, logger) {
        this.dataConsistencyChecker = dataConsistencyChecker;
        this.logger = logger;
        this.scheduledTasks = new Map();
        this.isRunning = false;
        
        // 默认配置
        this.config = {
            // 每日完整检查时间（凌晨2点）
            dailyCheckTime: '02:00',
            // 快速检查间隔（每小时）
            quickCheckInterval: 60 * 60 * 1000, // 1小时
            // 是否启用自动修复
            autoRepair: false,
            // 自动修复选项
            repairOptions: {
                removeOrphanedFiles: false,
                fixMissingFileRecords: true
            }
        };
    }

    /**
     * 启动调度器
     */
    start(config = {}) {
        if (this.isRunning) {
            this.logger.warn('数据一致性检查调度器已在运行');
            return;
        }

        // 合并配置
        this.config = { ...this.config, ...config };
        this.isRunning = true;

        // 启动定期快速检查
        this.startQuickChecks();
        
        // 启动每日完整检查
        this.startDailyChecks();
        
        // 启动时执行一次快速检查
        this.scheduleQuickCheck();

        this.logger.info('数据一致性检查调度器已启动', {
            quickCheckInterval: this.config.quickCheckInterval,
            dailyCheckTime: this.config.dailyCheckTime,
            autoRepair: this.config.autoRepair
        });
    }

    /**
     * 停止调度器
     */
    stop() {
        if (!this.isRunning) {
            return;
        }

        // 清除所有定时任务
        for (const [name, taskId] of this.scheduledTasks) {
            clearInterval(taskId);
            clearTimeout(taskId);
        }
        
        this.scheduledTasks.clear();
        this.isRunning = false;
        
        this.logger.info('数据一致性检查调度器已停止');
    }

    /**
     * 启动快速检查
     */
    startQuickChecks() {
        const intervalId = setInterval(() => {
            this.scheduleQuickCheck();
        }, this.config.quickCheckInterval);
        
        this.scheduledTasks.set('quickCheck', intervalId);
    }

    /**
     * 启动每日完整检查
     */
    startDailyChecks() {
        const checkDailySchedule = () => {
            const now = new Date();
            const [hour, minute] = this.config.dailyCheckTime.split(':').map(Number);
            
            const scheduledTime = new Date();
            scheduledTime.setHours(hour, minute, 0, 0);
            
            // 如果今天的时间已过，安排明天
            if (scheduledTime <= now) {
                scheduledTime.setDate(scheduledTime.getDate() + 1);
            }
            
            const delay = scheduledTime.getTime() - now.getTime();
            
            const timeoutId = setTimeout(() => {
                this.scheduleFullCheck();
                // 安排下一次检查
                checkDailySchedule();
            }, delay);
            
            this.scheduledTasks.set('dailyCheck', timeoutId);
            
            this.logger.info(`下次完整数据一致性检查安排在: ${scheduledTime.toISOString()}`);
        };
        
        checkDailySchedule();
    }

    /**
     * 执行快速检查
     */
    async scheduleQuickCheck() {
        try {
            this.logger.info('开始快速数据一致性检查');
            
            // 快速检查只检查关键项目
            const results = {
                missingFiles: await this.dataConsistencyChecker.findMissingFiles(),
                databaseIntegrity: await this.dataConsistencyChecker.checkDatabaseIntegrity()
            };
            
            // 检查是否有严重问题
            const hasCriticalIssues = this.hasCriticalIssues(results);
            
            if (hasCriticalIssues) {
                this.logger.warn('快速检查发现严重问题', { results });
                
                // 如果启用自动修复，尝试修复
                if (this.config.autoRepair) {
                    await this.performAutoRepair();
                }
            } else {
                this.logger.info('快速数据一致性检查通过');
            }
            
        } catch (error) {
            this.logger.error('快速数据一致性检查失败', { error: error.message });
        }
    }

    /**
     * 执行完整检查
     */
    async scheduleFullCheck() {
        try {
            this.logger.info('开始完整数据一致性检查');
            
            const result = await this.dataConsistencyChecker.performFullConsistencyCheck();
            
            if (result.success) {
                const hasIssues = this.hasAnyIssues(result.results);
                
                if (hasIssues) {
                    this.logger.warn('完整检查发现数据一致性问题', { 
                        results: result.results,
                        stats: result.stats 
                    });
                    
                    // 如果启用自动修复，尝试修复
                    if (this.config.autoRepair) {
                        await this.performAutoRepair();
                    }
                } else {
                    this.logger.info('完整数据一致性检查通过', { stats: result.stats });
                }
            } else {
                this.logger.error('完整数据一致性检查失败', { error: result.error });
            }
            
        } catch (error) {
            this.logger.error('完整数据一致性检查异常', { error: error.message });
        }
    }

    /**
     * 执行自动修复
     */
    async performAutoRepair() {
        try {
            this.logger.info('开始自动修复数据一致性问题');
            
            const repairResult = await this.dataConsistencyChecker.repairInconsistencies(
                this.config.repairOptions
            );
            
            if (repairResult.success) {
                this.logger.info('自动修复完成', { results: repairResult.results });
            } else {
                this.logger.error('自动修复失败', { error: repairResult.error });
            }
            
        } catch (error) {
            this.logger.error('自动修复异常', { error: error.message });
        }
    }

    /**
     * 检查是否有严重问题
     */
    hasCriticalIssues(results) {
        // 数据库完整性问题被认为是严重的
        if (results.databaseIntegrity && !results.databaseIntegrity.passed) {
            return true;
        }
        
        // 大量缺失文件被认为是严重的
        if (results.missingFiles && results.missingFiles.found > 10) {
            return true;
        }
        
        return false;
    }

    /**
     * 检查是否有任何问题
     */
    hasAnyIssues(results) {
        return (
            (results.attachmentConsistency && results.attachmentConsistency.inconsistencies > 0) ||
            (results.databaseIntegrity && !results.databaseIntegrity.passed) ||
            (results.orphanedFiles && results.orphanedFiles.found > 0) ||
            (results.missingFiles && results.missingFiles.found > 0) ||
            (results.fileIntegrity && results.fileIntegrity.corrupted > 0)
        );
    }

    /**
     * 获取调度器状态
     */
    getStatus() {
        return {
            isRunning: this.isRunning,
            config: this.config,
            scheduledTasks: Array.from(this.scheduledTasks.keys()),
            stats: this.dataConsistencyChecker.getStats()
        };
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        const oldConfig = { ...this.config };
        this.config = { ...this.config, ...newConfig };
        
        // 如果调度相关配置改变，重启调度器
        if (
            oldConfig.quickCheckInterval !== this.config.quickCheckInterval ||
            oldConfig.dailyCheckTime !== this.config.dailyCheckTime
        ) {
            if (this.isRunning) {
                this.stop();
                this.start();
            }
        }
        
        this.logger.info('数据一致性检查调度器配置已更新', { 
            oldConfig, 
            newConfig: this.config 
        });
    }

    /**
     * 手动触发检查
     */
    async triggerManualCheck(type = 'full') {
        if (type === 'quick') {
            await this.scheduleQuickCheck();
        } else {
            await this.scheduleFullCheck();
        }
    }
}

module.exports = ConsistencyScheduler;
