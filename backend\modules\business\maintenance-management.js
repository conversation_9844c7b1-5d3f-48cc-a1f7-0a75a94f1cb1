const fs = require('fs');
const path = require('path');
const DataAccess = require('../../data-access');

// 维修保养管理模块
class MaintenanceManagement {
    constructor() {
        this.dataAccess = new DataAccess();
        // 保留文件路径用于向后兼容，但不再使用
        this.recordsDir = path.join(__dirname, '..', 'data', 'maintenance-records');
        this.templatesFile = path.join(__dirname, '..', 'data', 'maintenance-templates.json');
        this.cache = {
            records: null,
            lastRecordUpdate: 0,
            cacheTTL: 30000 // 30秒缓存
        };
    }

    // 注意：初始化方法已移除，现在使用SQLite数据库



    // 生成记录文件路径（基于记录日期）
    getRecordFilePath(recordDate, recordId) {
        const date = new Date(recordDate);
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');

        const dateDir = path.join(this.recordsDir, String(year), month, day);

        // 确保目录存在
        if (!fs.existsSync(dateDir)) {
            fs.mkdirSync(dateDir, { recursive: true });
        }

        return path.join(dateDir, `${recordId}.json`);
    }

    // 读取单个记录数据
    getRecordById(id) {
        try {
            return this.dataAccess.getMaintenanceRecordById(id);
        } catch (error) {
            console.error('读取记录数据失败:', error);
            return null;
        }
    }

    // 从SQLite数据库中读取所有记录数据（保持接口兼容性）
    getAllRecordsFromFiles() {
        try {
            return this.dataAccess.getMaintenanceRecords();
        } catch (error) {
            console.error('读取记录数据失败:', error);
            return [];
        }
    }

    // 读取记录数据（带缓存）
    getRecords() {
        const now = Date.now();
        if (this.cache.records && (now - this.cache.lastRecordUpdate < this.cache.cacheTTL)) {
            return this.cache.records;
        }

        try {
            const records = this.dataAccess.getMaintenanceRecords();

            this.cache.records = records;
            this.cache.lastRecordUpdate = now;
            return this.cache.records;
        } catch (error) {
            console.error('读取记录数据失败:', error);
            return [];
        }
    }

    // 保存单个记录数据到SQLite
    saveRecord(record) {
        try {
            // 如果是新记录，使用addMaintenanceRecord，否则使用updateMaintenanceRecord
            if (!record.id || !this.dataAccess.getMaintenanceRecordById(record.id)) {
                this.dataAccess.addMaintenanceRecord(record);
            } else {
                this.dataAccess.updateMaintenanceRecord(record.id, record);
            }

            // 清除缓存，强制下次读取时重新加载
            this.cache.records = null;
            this.cache.lastRecordUpdate = 0;

            console.log(`维修记录已保存: ID=${record.id}`);
            return true;
        } catch (error) {
            console.error('保存维修记录失败:', error);
            return false;
        }
    }

    // 删除单个记录数据
    deleteRecordFile(record) {
        try {
            this.dataAccess.deleteMaintenanceRecord(record.id);
            console.log(`维修记录已删除: ID=${record.id}`);

            // 清除缓存
            this.cache.records = null;
            this.cache.lastRecordUpdate = 0;

            return true;
        } catch (error) {
            console.error('删除维修记录失败:', error);
            return false;
        }
    }

    // 保存记录数据（兼容旧接口，但使用新的文件结构）
    saveRecords(records) {
        try {
            // 为每个记录保存单独的文件
            for (const record of records) {
                if (!this.saveRecord(record)) {
                    console.error(`保存维修记录失败: ID=${record.id}`);
                    return false;
                }
            }

            // 更新缓存
            this.cache.records = records;
            this.cache.lastRecordUpdate = Date.now();

            return true;
        } catch (error) {
            console.error('批量保存维修记录数据失败:', error);
            return false;
        }
    }

    // 读取模板数据
    getTemplates() {
        try {
            return this.dataAccess.getMaintenanceTemplates();
        } catch (error) {
            console.error('读取模板数据失败:', error);
            return { maintenance: [], upkeep: [], '临时保养': [] };
        }
    }

    // 保存模板数据
    saveTemplates(templates) {
        try {
            this.dataAccess.updateMaintenanceTemplates(templates);
            return true;
        } catch (error) {
            console.error('保存模板数据失败:', error);
            return false;
        }
    }

    // 生成记录编号（根据厂区ID和日期）
    generateRecordCode(factoryId, type) {
        const records = this.getRecords();
        const now = new Date();
        const dateStr = now.toISOString().slice(0, 10).replace(/-/g, ''); // YYYYMMDD

        // 如果没有提供厂区ID，使用默认前缀
        if (!factoryId) {
            const prefix = type === '维修' ? 'MR' : type === '保养' ? 'UR' : 'TR';
            const todayRecords = records.filter(record =>
                record.recordCode && record.recordCode.startsWith(`${prefix}${dateStr}`)
            );
            const nextNumber = todayRecords.length + 1;
            return `${prefix}${dateStr}${nextNumber.toString().padStart(3, '0')}`;
        }

        // 使用厂区ID作为前缀：厂区ID + 日期 + 流水号
        const prefix = factoryId.toUpperCase(); // 确保厂区ID为大写
        const expectedPrefix = `${prefix}${dateStr}`;

        // 查找当天已有的记录编号（同一厂区）
        const todayRecords = records.filter(record =>
            record.recordCode && record.recordCode.startsWith(expectedPrefix)
        );

        const nextNumber = todayRecords.length + 1;
        return `${expectedPrefix}${nextNumber.toString().padStart(3, '0')}`;
    }

    // 获取所有记录
    getAllRecords(filters = {}) {
        try {
            // 构建SQLite搜索过滤器
            const searchFilters = {};

            if (filters.deviceId) {
                searchFilters.deviceCode = filters.deviceId; // 映射到deviceCode字段
            }

            if (filters.factory) {
                searchFilters.factory = filters.factory;
            }

            if (filters.type && filters.type !== 'all') {
                searchFilters.maintenanceType = filters.type;
            }

            if (filters.operator) {
                searchFilters.reportedBy = filters.operator;
            }

            if (filters.startDate) {
                searchFilters.dateFrom = filters.startDate;
            }

            if (filters.endDate) {
                searchFilters.dateTo = filters.endDate;
            }

            // 使用SQLite搜索功能
            let records = this.dataAccess.searchMaintenanceRecords(searchFilters);

            // 应用额外的搜索过滤
            if (filters.search) {
                const searchTerm = filters.search.toLowerCase();
                records = records.filter(record =>
                    (record.deviceCode && record.deviceCode.toLowerCase().includes(searchTerm)) ||
                    (record.description && record.description.toLowerCase().includes(searchTerm)) ||
                    (record.solution && record.solution.toLowerCase().includes(searchTerm)) ||
                    (record.reportedBy && record.reportedBy.toLowerCase().includes(searchTerm))
                );
            }

            return records;
        } catch (error) {
            console.error('获取记录失败:', error);
            return [];
        }
    }



    // 添加记录
    addRecord(recordData) {
        try {
            // 验证必填字段
            if (!recordData.deviceId || !recordData.type || !recordData.date ||
                !recordData.operator || !recordData.description) {
                return { success: false, message: '设备、类型、日期、操作人和描述为必填项' };
            }

            const newRecord = {
                id: Date.now().toString(),
                recordCode: recordData.recordCode || this.generateRecordCode(recordData.factory, recordData.type),
                factory: recordData.factory,
                location: recordData.location || '', // 添加位置字段
                deviceId: recordData.deviceId,
                type: recordData.type,
                faultSeverity: recordData.faultSeverity || '', // 添加故障程度字段
                date: recordData.date,
                startTime: recordData.startTime,
                endTime: recordData.endTime,
                operator: recordData.operator, // 操作人员字段，同时作为记录创建者
                description: recordData.description,
                result: recordData.result || '',
                reviewer: recordData.reviewer || '', // 添加审查人员字段
                customFields: recordData.customFields || {},
                attachments: recordData.attachments || [],
                createTime: new Date().toISOString(),
                updateTime: new Date().toISOString()
            };

            // 使用新的保存方式：保存单个记录文件
            if (this.saveRecord(newRecord)) {
                // 清除缓存以确保下次读取时获取最新数据
                this.cache.records = null;
                this.cache.lastRecordUpdate = 0;

                return { success: true, message: '记录添加成功', record: newRecord };
            } else {
                return { success: false, message: '保存记录数据失败' };
            }
        } catch (error) {
            console.error('添加记录失败:', error);
            return { success: false, message: '添加记录失败' };
        }
    }

    // 更新记录
    updateRecord(id, recordData) {
        try {
            const records = this.getRecords();
            const recordIndex = records.findIndex(record => record.id === id);

            if (recordIndex === -1) {
                return { success: false, message: '记录不存在' };
            }

            // 验证必填字段
            if (!recordData.deviceId || !recordData.type || !recordData.date ||
                !recordData.operator || !recordData.description) {
                return { success: false, message: '设备、类型、日期、操作人和描述为必填项' };
            }

            // 更新记录信息
            records[recordIndex] = {
                ...records[recordIndex],
                recordCode: recordData.recordCode || records[recordIndex].recordCode,
                factory: recordData.factory || records[recordIndex].factory,
                location: recordData.location !== undefined ? recordData.location : records[recordIndex].location, // 更新位置字段
                deviceId: recordData.deviceId,
                type: recordData.type,
                faultSeverity: recordData.faultSeverity !== undefined ? recordData.faultSeverity : records[recordIndex].faultSeverity, // 更新故障程度字段
                date: recordData.date,
                startTime: recordData.startTime,
                endTime: recordData.endTime,
                operator: recordData.operator,
                description: recordData.description,
                result: recordData.result || records[recordIndex].result,
                reviewer: recordData.reviewer !== undefined ? recordData.reviewer : records[recordIndex].reviewer, // 更新审查人员字段
                customFields: recordData.customFields || records[recordIndex].customFields,
                attachments: recordData.attachments || records[recordIndex].attachments,
                updateTime: new Date().toISOString()
            };

            // 使用新的保存方式：保存单个记录文件
            if (this.saveRecord(records[recordIndex])) {
                // 清除缓存以确保下次读取时获取最新数据
                this.cache.records = null;
                this.cache.lastRecordUpdate = 0;

                return { success: true, message: '记录更新成功', record: records[recordIndex] };
            } else {
                return { success: false, message: '保存记录数据失败' };
            }
        } catch (error) {
            console.error('更新记录失败:', error);
            return { success: false, message: '更新记录失败' };
        }
    }

    // 删除记录
    deleteRecord(id) {
        try {
            const records = this.getRecords();
            const record = records.find(rec => rec.id === id);

            if (!record) {
                return { success: false, message: '记录不存在' };
            }

            // 删除记录文件
            if (this.deleteRecordFile(record)) {
                // 清除缓存以确保下次读取时获取最新数据
                this.cache.records = null;
                this.cache.lastRecordUpdate = 0;

                return { success: true, message: '记录删除成功' };
            } else {
                return { success: false, message: '删除记录文件失败' };
            }
        } catch (error) {
            console.error('删除记录失败:', error);
            return { success: false, message: '删除记录失败' };
        }
    }

    // 批量删除记录
    deleteRecords(ids) {
        try {
            const records = this.getRecords();
            const recordsToDelete = records.filter(record => ids.includes(record.id));

            let successCount = 0;
            let errorCount = 0;

            // 删除每个记录文件
            for (const record of recordsToDelete) {
                if (this.deleteRecordFile(record)) {
                    successCount++;
                } else {
                    errorCount++;
                }
            }

            // 清除缓存
            this.cache.records = null;
            this.cache.lastRecordUpdate = 0;

            if (errorCount === 0) {
                return { success: true, message: `成功删除 ${successCount} 条记录` };
            } else {
                return { success: false, message: `删除完成，成功 ${successCount} 条，失败 ${errorCount} 条` };
            }
        } catch (error) {
            console.error('批量删除记录失败:', error);
            return { success: false, message: '批量删除记录失败' };
        }
    }

    // 获取记录统计信息
    getRecordStats() {
        const records = this.getRecords();
        const total = records.length;
        const maintenance = records.filter(record => record.type === '维修').length;
        const upkeep = records.filter(record => record.type === '保养').length;
        const temporaryUpkeep = records.filter(record => record.type === '临时保养').length;

        // 按月统计
        const monthlyStats = {};
        records.forEach(record => {
            const month = record.date.slice(0, 7); // YYYY-MM
            if (!monthlyStats[month]) {
                monthlyStats[month] = { maintenance: 0, upkeep: 0, temporaryUpkeep: 0 };
            }
            if (record.type === '维修') {
                monthlyStats[month].maintenance++;
            } else if (record.type === '保养') {
                monthlyStats[month].upkeep++;
            } else if (record.type === '临时保养') {
                monthlyStats[month].temporaryUpkeep++;
            }
        });

        return {
            total,
            maintenance,
            upkeep,
            temporaryUpkeep,
            monthlyStats
        };
    }
}

module.exports = MaintenanceManagement;
