/**
 * API访问控制模块
 * 实现基于角色的精确权限管理（RBAC）
 */
class AccessControl {
    constructor(logger) {
        this.logger = logger;
        
        // 权限定义
        this.permissions = {
            // 用户管理权限
            'users.view': '查看用户',
            'users.create': '创建用户',
            'users.edit': '编辑用户',
            'users.delete': '删除用户',
            'users.manage_roles': '管理用户角色',
            
            // 申请管理权限
            'applications.view': '查看申请',
            'applications.create': '创建申请',
            'applications.edit': '编辑申请',
            'applications.delete': '删除申请',
            'applications.approve': '审批申请',
            'applications.reject': '拒绝申请',
            'applications.export': '导出申请数据',
            
            // 设备管理权限
            'devices.view': '查看设备',
            'devices.create': '创建设备',
            'devices.edit': '编辑设备',
            'devices.delete': '删除设备',
            'devices.maintenance': '设备维护',
            
            // 维护记录权限
            'maintenance.view': '查看维护记录',
            'maintenance.create': '创建维护记录',
            'maintenance.edit': '编辑维护记录',
            'maintenance.delete': '删除维护记录',
            
            // 系统管理权限
            'system.monitor': '系统监控',
            'system.backup': '系统备份',
            'system.config': '系统配置',
            'system.logs': '查看日志',
            'system.users_sessions': '管理用户会话',
            
            // 报表权限
            'reports.view': '查看报表',
            'reports.export': '导出报表',
            'reports.create': '创建报表',
            
            // 文件权限
            'files.upload': '上传文件',
            'files.download': '下载文件',
            'files.delete': '删除文件'
        };
        
        // 角色权限映射
        this.rolePermissions = {
            'admin': [
                // 管理员拥有所有权限
                ...Object.keys(this.permissions)
            ],
            'manager': [
                // 管理者权限
                'users.view', 'users.create', 'users.edit',
                'applications.view', 'applications.create', 'applications.edit', 'applications.approve', 'applications.reject', 'applications.export',
                'devices.view', 'devices.create', 'devices.edit', 'devices.maintenance',
                'maintenance.view', 'maintenance.create', 'maintenance.edit',
                'reports.view', 'reports.export', 'reports.create',
                'files.upload', 'files.download',
                'system.monitor'
            ],
            'operator': [
                // 操作员权限
                'applications.view', 'applications.create', 'applications.edit',
                'devices.view', 'devices.edit', 'devices.maintenance',
                'maintenance.view', 'maintenance.create', 'maintenance.edit',
                'reports.view',
                'files.upload', 'files.download'
            ],
            'user': [
                // 普通用户权限
                'applications.view', 'applications.create',
                'devices.view',
                'maintenance.view',
                'files.upload', 'files.download'
            ],
            'viewer': [
                // 只读用户权限
                'applications.view',
                'devices.view',
                'maintenance.view',
                'reports.view'
            ]
        };
        
        // API端点权限映射
        this.apiPermissions = {
            // 用户管理API
            'GET:/api/users': ['users.view'],
            'POST:/api/users': ['users.create'],
            'PUT:/api/users/:id': ['users.edit'],
            'DELETE:/api/users/:id': ['users.delete'],
            'PUT:/api/users/:id/role': ['users.manage_roles'],
            
            // 申请管理API
            'GET:/api/applications': ['applications.view'],
            'POST:/api/applications': ['applications.create'],
            'PUT:/api/applications/:id': ['applications.edit'],
            'DELETE:/api/applications/:id': ['applications.delete'],
            'POST:/api/applications/:id/approve': ['applications.approve'],
            'POST:/api/applications/:id/reject': ['applications.reject'],
            'GET:/api/applications/export': ['applications.export'],
            
            // 设备管理API
            'GET:/api/devices': ['devices.view'],
            'POST:/api/devices': ['devices.create'],
            'PUT:/api/devices/:id': ['devices.edit'],
            'DELETE:/api/devices/:id': ['devices.delete'],
            'POST:/api/devices/:id/maintenance': ['devices.maintenance'],
            
            // 维护记录API
            'GET:/api/maintenance': ['maintenance.view'],
            'POST:/api/maintenance': ['maintenance.create'],
            'PUT:/api/maintenance/:id': ['maintenance.edit'],
            'DELETE:/api/maintenance/:id': ['maintenance.delete'],
            
            // 系统管理API
            'GET:/api/system/stats': ['system.monitor'],
            'GET:/api/system/health': ['system.monitor'],
            'POST:/api/system/backup/create': ['system.backup'],
            'GET:/api/system/backup/list': ['system.backup'],
            'POST:/api/system/backup/restore': ['system.backup'],
            'GET:/api/system/sessions/stats': ['system.users_sessions'],
            'DELETE:/api/system/sessions/:id': ['system.users_sessions'],
            'GET:/api/system/logs': ['system.logs'],
            
            // 报表API
            'GET:/api/reports': ['reports.view'],
            'POST:/api/reports': ['reports.create'],
            'GET:/api/reports/export': ['reports.export'],
            
            // 文件API
            'POST:/api/upload': ['files.upload'],
            'GET:/api/files/:id': ['files.download'],
            'DELETE:/api/files/:id': ['files.delete']
        };
        
        // 资源所有权检查
        this.resourceOwnership = {
            'applications': {
                ownerField: 'username',
                allowOwnerAccess: ['applications.view', 'applications.edit']
            }
        };
    }

    /**
     * 检查用户是否有指定权限
     */
    hasPermission(userRole, permission) {
        const rolePerms = this.rolePermissions[userRole] || [];
        return rolePerms.includes(permission);
    }

    /**
     * 检查用户是否可以访问API端点
     */
    canAccessAPI(userRole, method, path) {
        const apiKey = `${method}:${path}`;
        const requiredPermissions = this.apiPermissions[apiKey];
        
        if (!requiredPermissions) {
            // 如果没有定义权限要求，默认允许访问
            return { allowed: true, reason: 'no_permission_required' };
        }
        
        // 检查用户是否有任一所需权限
        const userPermissions = this.rolePermissions[userRole] || [];
        const hasPermission = requiredPermissions.some(perm => userPermissions.includes(perm));
        
        if (hasPermission) {
            return { allowed: true, permissions: requiredPermissions };
        } else {
            return { 
                allowed: false, 
                reason: 'insufficient_permissions',
                required: requiredPermissions,
                userRole
            };
        }
    }

    /**
     * 检查资源所有权
     */
    checkResourceOwnership(resourceType, resource, username, permission) {
        const ownershipConfig = this.resourceOwnership[resourceType];
        
        if (!ownershipConfig) {
            return false;
        }
        
        // 检查是否允许所有者访问此权限
        if (!ownershipConfig.allowOwnerAccess.includes(permission)) {
            return false;
        }
        
        // 检查用户是否是资源所有者
        const ownerField = ownershipConfig.ownerField;
        return resource[ownerField] === username;
    }

    /**
     * 创建权限检查中间件
     */
    createPermissionMiddleware(requiredPermissions = []) {
        return (req, res, next) => {
            try {
                const user = req.user;
                if (!user) {
                    return res.status(401).json({
                        success: false,
                        message: '未认证用户',
                        code: 'UNAUTHORIZED'
                    });
                }
                
                // 检查权限
                const userPermissions = this.rolePermissions[user.role] || [];
                const hasPermission = requiredPermissions.length === 0 || 
                    requiredPermissions.some(perm => userPermissions.includes(perm));
                
                if (!hasPermission) {
                    this.logger.warn('Access denied - insufficient permissions', {
                        userId: user.id,
                        username: user.username,
                        role: user.role,
                        requiredPermissions,
                        userPermissions,
                        path: req.path,
                        method: req.method
                    });
                    
                    return res.status(403).json({
                        success: false,
                        message: '权限不足',
                        code: 'FORBIDDEN',
                        required: requiredPermissions
                    });
                }
                
                // 记录访问日志
                this.logger.info('API access granted', {
                    userId: user.id,
                    username: user.username,
                    role: user.role,
                    path: req.path,
                    method: req.method,
                    permissions: requiredPermissions
                });
                
                next();
            } catch (error) {
                this.logger.error('Permission check error', {
                    error: error.message,
                    path: req.path,
                    method: req.method
                });
                
                res.status(500).json({
                    success: false,
                    message: '权限检查失败',
                    code: 'PERMISSION_CHECK_ERROR'
                });
            }
        };
    }

    /**
     * 创建资源所有权检查中间件
     */
    createOwnershipMiddleware(resourceType, getResourceFn) {
        return async (req, res, next) => {
            try {
                const user = req.user;
                if (!user) {
                    return res.status(401).json({
                        success: false,
                        message: '未认证用户',
                        code: 'UNAUTHORIZED'
                    });
                }
                
                // 获取资源
                const resource = await getResourceFn(req);
                if (!resource) {
                    return res.status(404).json({
                        success: false,
                        message: '资源不存在',
                        code: 'RESOURCE_NOT_FOUND'
                    });
                }
                
                // 检查所有权
                const isOwner = this.checkResourceOwnership(
                    resourceType, 
                    resource, 
                    user.username, 
                    req.permission
                );
                
                if (!isOwner) {
                    this.logger.warn('Access denied - not resource owner', {
                        userId: user.id,
                        username: user.username,
                        resourceType,
                        resourceId: resource.id,
                        path: req.path,
                        method: req.method
                    });
                    
                    return res.status(403).json({
                        success: false,
                        message: '无权访问此资源',
                        code: 'RESOURCE_ACCESS_DENIED'
                    });
                }
                
                req.resource = resource;
                next();
            } catch (error) {
                this.logger.error('Ownership check error', {
                    error: error.message,
                    path: req.path,
                    method: req.method
                });
                
                res.status(500).json({
                    success: false,
                    message: '所有权检查失败',
                    code: 'OWNERSHIP_CHECK_ERROR'
                });
            }
        };
    }

    /**
     * 获取用户权限列表
     */
    getUserPermissions(userRole) {
        return this.rolePermissions[userRole] || [];
    }

    /**
     * 获取权限描述
     */
    getPermissionDescription(permission) {
        return this.permissions[permission] || permission;
    }

    /**
     * 获取所有角色和权限信息
     */
    getRolePermissionMatrix() {
        const matrix = {};
        
        for (const [role, permissions] of Object.entries(this.rolePermissions)) {
            matrix[role] = {
                permissions: permissions,
                descriptions: permissions.map(perm => ({
                    permission: perm,
                    description: this.getPermissionDescription(perm)
                }))
            };
        }
        
        return matrix;
    }

    /**
     * 添加自定义权限
     */
    addPermission(permission, description) {
        this.permissions[permission] = description;
        this.logger.info('Permission added', { permission, description });
    }

    /**
     * 为角色添加权限
     */
    addPermissionToRole(role, permission) {
        if (!this.rolePermissions[role]) {
            this.rolePermissions[role] = [];
        }
        
        if (!this.rolePermissions[role].includes(permission)) {
            this.rolePermissions[role].push(permission);
            this.logger.info('Permission added to role', { role, permission });
        }
    }

    /**
     * 从角色移除权限
     */
    removePermissionFromRole(role, permission) {
        if (this.rolePermissions[role]) {
            const index = this.rolePermissions[role].indexOf(permission);
            if (index > -1) {
                this.rolePermissions[role].splice(index, 1);
                this.logger.info('Permission removed from role', { role, permission });
            }
        }
    }

    /**
     * 获取访问控制统计信息
     */
    getAccessControlStats() {
        const stats = {
            totalPermissions: Object.keys(this.permissions).length,
            totalRoles: Object.keys(this.rolePermissions).length,
            totalAPIEndpoints: Object.keys(this.apiPermissions).length,
            roleDistribution: {}
        };
        
        // 统计每个角色的权限数量
        for (const [role, permissions] of Object.entries(this.rolePermissions)) {
            stats.roleDistribution[role] = permissions.length;
        }
        
        return stats;
    }
}

module.exports = AccessControl;
