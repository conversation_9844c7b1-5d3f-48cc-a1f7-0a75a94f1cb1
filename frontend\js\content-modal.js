/**
 * 申请内容弹窗显示功能
 * 用于显示完整的申请内容，当内容过长时提供更好的阅读体验
 */

// 创建弹窗元素
function createContentModal() {
    // 检查是否已存在弹窗
    if (document.getElementById('contentModal')) {
        return;
    }

    // 创建弹窗元素
    const modal = document.createElement('div');
    modal.id = 'contentModal';
    modal.className = 'hidden fixed inset-0 bg-black bg-opacity-50 z-50 flex items-center justify-center';
    modal.innerHTML = `
        <div class="relative bg-white rounded-lg p-6 w-full max-w-2xl max-h-[90vh] overflow-hidden">
            <div class="flex justify-between items-center mb-4">
                <h2 class="text-2xl font-bold">申请内容</h2>
                <button onclick="closeContentModal()" class="text-gray-500 hover:text-gray-700">✕</button>
            </div>
            <div id="contentModalBody" class="space-y-4 overflow-y-auto pr-2 whitespace-pre-wrap" style="max-height: calc(80vh - 4rem);"></div>
        </div>
    `;

    // 添加到文档中
    document.body.appendChild(modal);
}

// 显示申请内容弹窗
function showContentModal(content) {
    // 确保弹窗元素已创建
    createContentModal();

    // 设置内容
    const contentModalBody = document.getElementById('contentModalBody');
    contentModalBody.textContent = content;

    // 显示弹窗
    const modal = document.getElementById('contentModal');
    modal.classList.remove('hidden');

    // 添加body类防止背景滚动
    document.body.classList.add('modal-open');

    // 应用模态框高度控制（如果已加载modal-height-control.js）
    if (typeof adjustModalHeight === 'function') {
        setTimeout(() => adjustModalHeight('contentModal'), 10);
    }
}

// 关闭申请内容弹窗
function closeContentModal() {
    const modal = document.getElementById('contentModal');
    if (modal) {
        modal.classList.add('hidden');

        // 移除body类，恢复背景滚动
        document.body.classList.remove('modal-open');

        // 重置滚动位置
        const contentModalBody = document.getElementById('contentModalBody');
        if (contentModalBody) {
            contentModalBody.scrollTop = 0;
        }
    }
}

// 截断文本，超过指定长度时显示省略号
function truncateText(text, maxLength = 50) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
}

// 创建可点击的申请内容单元格
function createClickableContent(content) {
    if (!content) return '';

    // 判断内容是否需要截断
    const needsTruncation = content.length > 80;

    // 获取显示内容，如果内容超过80个字符则显示"..."
    const displayContent = needsTruncation ? content.substring(0, 80) + '...' : content;

    // 计算内容的行数（假设每行约25个字符）
    const lines = Math.ceil(content.length / 25);
    const maxLines = 3; // 最多显示3行
    // 保留截断逻辑但不再使用isContentTruncated变量

    // 创建可点击的元素，使用更友好的样式
    return `<div class="cursor-pointer group" onclick="showContentModal('${escapeJsString(content)}')">
        <div class="text-sm text-gray-700"
            style="display: -webkit-box; -webkit-line-clamp: ${Math.min(lines, maxLines)}; -webkit-box-orient: vertical;
            overflow: hidden; line-height: 1.3; max-height: ${Math.min(lines, maxLines) * 1.3}em;">
            ${sanitizeInput(displayContent)}
        </div>
        <!-- 移除了"点击查看详情"的文本显示 -->
    </div>`;
}

// 转义JavaScript字符串中的特殊字符
function escapeJsString(str) {
    if (!str) return '';
    return str
        .replace(/\\/g, '\\\\')
        .replace(/'/g, "\\'")
        .replace(/"/g, '\\"')
        .replace(/\n/g, '\\n')
        .replace(/\r/g, '\\r')
        .replace(/\t/g, '\\t');
}

// 确保在页面加载完成后创建弹窗元素
document.addEventListener('DOMContentLoaded', function() {
    createContentModal();

    // 添加点击背景关闭弹窗的功能
    document.addEventListener('click', function(event) {
        const modal = document.getElementById('contentModal');
        const modalContent = modal?.querySelector('div');

        if (modal && !modal.classList.contains('hidden') &&
            event.target === modal && modalContent && !modalContent.contains(event.target)) {
            closeContentModal();
        }
    });

    // 添加ESC键关闭弹窗的功能
    document.addEventListener('keydown', function(event) {
        if (event.key === 'Escape') {
            closeContentModal();
        }
    });
});
