const jwt = require('jsonwebtoken');
const bcrypt = require('bcrypt');

/**
 * JWT认证服务模块
 * 提供用户认证、token生成、验证和刷新功能
 * 遵循安全最佳实践，支持token过期和刷新机制
 */
class AuthService {
    constructor(passwordPolicy = null) {
        // 密码策略管理器
        this.passwordPolicy = passwordPolicy;

        // JWT配置
        this.jwtConfig = {
            // 使用环境变量或默认密钥（生产环境必须使用环境变量）
            accessTokenSecret: process.env.JWT_ACCESS_SECRET || 'makrite_access_secret_2025_secure_key_change_in_production',
            refreshTokenSecret: process.env.JWT_REFRESH_SECRET || 'makrite_refresh_secret_2025_secure_key_change_in_production',
            
            // Token过期时间
            accessTokenExpiry: process.env.JWT_ACCESS_EXPIRY || '15m', // 15分钟
            refreshTokenExpiry: process.env.JWT_REFRESH_EXPIRY || '7d', // 7天
            
            // 发行者信息
            issuer: 'makrite-management-system',
            audience: 'makrite-users'
        };

        // 存储刷新token的内存存储（生产环境建议使用Redis）
        this.refreshTokenStore = new Map();
        
        // 黑名单token存储（用于登出）
        this.blacklistedTokens = new Set();
        
        // 定期清理过期token
        this.startTokenCleanup();
    }

    /**
     * 生成访问token
     * @param {Object} user - 用户信息
     * @returns {string} JWT访问token
     */
    generateAccessToken(user) {
        const payload = {
            userId: user.id,
            username: user.username,
            role: user.role,
            department: user.department,
            type: 'access'
        };

        return jwt.sign(payload, this.jwtConfig.accessTokenSecret, {
            expiresIn: this.jwtConfig.accessTokenExpiry,
            issuer: this.jwtConfig.issuer,
            audience: this.jwtConfig.audience,
            subject: user.id.toString()
        });
    }

    /**
     * 生成刷新token
     * @param {Object} user - 用户信息
     * @returns {string} JWT刷新token
     */
    generateRefreshToken(user) {
        const payload = {
            userId: user.id,
            username: user.username,
            type: 'refresh'
        };

        const refreshToken = jwt.sign(payload, this.jwtConfig.refreshTokenSecret, {
            expiresIn: this.jwtConfig.refreshTokenExpiry,
            issuer: this.jwtConfig.issuer,
            audience: this.jwtConfig.audience,
            subject: user.id.toString()
        });

        // 存储刷新token
        this.refreshTokenStore.set(refreshToken, {
            userId: user.id,
            username: user.username,
            createdAt: new Date(),
            expiresAt: new Date(Date.now() + this.parseExpiry(this.jwtConfig.refreshTokenExpiry))
        });

        return refreshToken;
    }

    /**
     * 验证访问token
     * @param {string} token - JWT token
     * @returns {Object|null} 解码后的用户信息或null
     */
    verifyAccessToken(token) {
        try {
            // 检查是否在黑名单中
            if (this.blacklistedTokens.has(token)) {
                return null;
            }

            const decoded = jwt.verify(token, this.jwtConfig.accessTokenSecret, {
                issuer: this.jwtConfig.issuer,
                audience: this.jwtConfig.audience
            });

            // 验证token类型
            if (decoded.type !== 'access') {
                return null;
            }

            return decoded;
        } catch (error) {
            console.error('访问token验证失败:', error.message);
            return null;
        }
    }

    /**
     * 验证刷新token
     * @param {string} token - 刷新token
     * @returns {Object|null} 解码后的用户信息或null
     */
    verifyRefreshToken(token) {
        try {
            // 检查token是否存在于存储中
            if (!this.refreshTokenStore.has(token)) {
                return null;
            }

            const decoded = jwt.verify(token, this.jwtConfig.refreshTokenSecret, {
                issuer: this.jwtConfig.issuer,
                audience: this.jwtConfig.audience
            });

            // 验证token类型
            if (decoded.type !== 'refresh') {
                return null;
            }

            // 检查存储的token信息
            const storedInfo = this.refreshTokenStore.get(token);
            if (storedInfo.userId !== decoded.userId) {
                return null;
            }

            return decoded;
        } catch (error) {
            console.error('刷新token验证失败:', error.message);
            // 如果token无效，从存储中移除
            this.refreshTokenStore.delete(token);
            return null;
        }
    }

    /**
     * 刷新访问token
     * @param {string} refreshToken - 刷新token
     * @param {Function} getUserById - 获取用户信息的函数
     * @returns {Object|null} 新的token对或null
     */
    async refreshAccessToken(refreshToken, getUserById) {
        const decoded = this.verifyRefreshToken(refreshToken);
        if (!decoded) {
            return null;
        }

        try {
            // 获取最新的用户信息
            const user = await getUserById(decoded.userId);
            if (!user) {
                // 用户不存在，移除刷新token
                this.refreshTokenStore.delete(refreshToken);
                return null;
            }

            // 生成新的访问token
            const newAccessToken = this.generateAccessToken(user);

            return {
                accessToken: newAccessToken,
                refreshToken: refreshToken, // 刷新token保持不变
                user: {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    department: user.department
                }
            };
        } catch (error) {
            console.error('刷新访问token失败:', error);
            return null;
        }
    }

    /**
     * 用户登录
     * @param {string} username - 用户名
     * @param {string} password - 密码
     * @param {Function} getUserByUsername - 获取用户的函数
     * @returns {Object|null} 登录结果
     */
    async login(username, password, getUserByUsername, clientIp = '127.0.0.1') {
        try {
            // 检查账户是否被锁定
            if (this.passwordPolicy) {
                const lockStatus = this.passwordPolicy.isAccountLocked(username, clientIp);
                if (lockStatus && lockStatus.locked) {
                    return {
                        success: false,
                        message: `账户已被锁定，请在${lockStatus.remainingTime}分钟后重试`,
                        lockStatus
                    };
                }
            }

            const user = await getUserByUsername(username);
            if (!user) {
                // 记录失败尝试
                if (this.passwordPolicy) {
                    this.passwordPolicy.recordFailedAttempt(username, clientIp);
                }
                return { success: false, message: '用户名或密码错误' };
            }

            // 验证密码
            const isValidPassword = await bcrypt.compare(password, user.password);
            if (!isValidPassword) {
                // 记录失败尝试
                if (this.passwordPolicy) {
                    this.passwordPolicy.recordFailedAttempt(username, clientIp);
                }
                return { success: false, message: '用户名或密码错误' };
            }

            // 登录成功，清除失败尝试记录
            if (this.passwordPolicy) {
                this.passwordPolicy.clearFailedAttempts(username, clientIp);
            }

            // 生成token
            const accessToken = this.generateAccessToken(user);
            const refreshToken = this.generateRefreshToken(user);

            return {
                success: true,
                message: '登录成功',
                accessToken,
                refreshToken,
                user: {
                    id: user.id,
                    username: user.username,
                    role: user.role,
                    department: user.department,
                    email: user.email
                }
            };
        } catch (error) {
            console.error('登录失败:', error);
            return { success: false, message: '登录失败，请重试' };
        }
    }

    /**
     * 用户登出
     * @param {string} accessToken - 访问token
     * @param {string} refreshToken - 刷新token
     */
    logout(accessToken, refreshToken) {
        // 将访问token加入黑名单
        if (accessToken) {
            this.blacklistedTokens.add(accessToken);
        }

        // 移除刷新token
        if (refreshToken) {
            this.refreshTokenStore.delete(refreshToken);
        }
    }

    /**
     * 解析过期时间字符串为毫秒
     * @param {string} expiry - 过期时间字符串（如 '15m', '7d'）
     * @returns {number} 毫秒数
     */
    parseExpiry(expiry) {
        const units = {
            's': 1000,
            'm': 60 * 1000,
            'h': 60 * 60 * 1000,
            'd': 24 * 60 * 60 * 1000
        };

        const match = expiry.match(/^(\d+)([smhd])$/);
        if (!match) {
            throw new Error('无效的过期时间格式');
        }

        const [, value, unit] = match;
        return parseInt(value) * units[unit];
    }

    /**
     * 启动token清理任务
     * 定期清理过期的刷新token和黑名单token
     */
    startTokenCleanup() {
        // 每小时清理一次
        setInterval(() => {
            this.cleanupExpiredTokens();
        }, 60 * 60 * 1000);
    }

    /**
     * 清理过期的token
     */
    cleanupExpiredTokens() {
        const now = new Date();

        // 清理过期的刷新token
        for (const [token, info] of this.refreshTokenStore.entries()) {
            if (info.expiresAt < now) {
                this.refreshTokenStore.delete(token);
            }
        }

        // 清理过期的黑名单token（简单实现：清理超过24小时的token）
        // 生产环境建议使用更精确的过期时间跟踪
        if (this.blacklistedTokens.size > 1000) {
            this.blacklistedTokens.clear();
        }

        console.log(`Token清理完成: 刷新token数量: ${this.refreshTokenStore.size}, 黑名单token数量: ${this.blacklistedTokens.size}`);
    }

    /**
     * 更改密码
     * @param {string} username - 用户名
     * @param {string} currentPassword - 当前密码
     * @param {string} newPassword - 新密码
     * @param {Function} getUserByUsername - 获取用户的函数
     * @param {Function} updateUserPassword - 更新用户密码的函数
     * @returns {Object} 更改结果
     */
    async changePassword(username, currentPassword, newPassword, getUserByUsername, updateUserPassword) {
        try {
            const user = await getUserByUsername(username);
            if (!user) {
                return { success: false, message: '用户不存在' };
            }

            // 验证当前密码
            const isValidPassword = await bcrypt.compare(currentPassword, user.password);
            if (!isValidPassword) {
                return { success: false, message: '当前密码错误' };
            }

            // 验证新密码长度（至少6位）
            if (!newPassword || newPassword.length < 6) {
                return {
                    success: false,
                    message: '新密码至少需要6位'
                };
            }

            // 检查新密码是否与当前密码相同
            const isSamePassword = await bcrypt.compare(newPassword, user.password);
            if (isSamePassword) {
                return { success: false, message: '新密码不能与当前密码相同' };
            }

            // 加密新密码
            const saltRounds = 12;
            const hashedPassword = await bcrypt.hash(newPassword, saltRounds);

            // 更新密码
            await updateUserPassword(username, hashedPassword);

            // 使所有现有token失效（强制重新登录）
            this.invalidateUserTokens(user.id);

            return {
                success: true,
                message: '密码更改成功，请重新登录'
            };
        } catch (error) {
            console.error('更改密码失败:', error);
            return { success: false, message: '密码更改失败，请重试' };
        }
    }

    /**
     * 使用户的所有token失效
     * @param {string} userId - 用户ID
     */
    invalidateUserTokens(userId) {
        // 移除该用户的所有刷新token
        for (const [token, info] of this.refreshTokenStore.entries()) {
            if (info.userId === userId) {
                this.refreshTokenStore.delete(token);
            }
        }
    }

    /**
     * 获取token统计信息
     * @returns {Object} 统计信息
     */
    getTokenStats() {
        return {
            refreshTokenCount: this.refreshTokenStore.size,
            blacklistedTokenCount: this.blacklistedTokens.size,
            lastCleanup: new Date()
        };
    }
}

module.exports = AuthService;
