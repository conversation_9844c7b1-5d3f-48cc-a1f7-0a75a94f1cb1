const os = require('os');
const fs = require('fs');
const path = require('path');
const { performance } = require('perf_hooks');

/**
 * 实时性能监控模块
 * 监控系统资源使用率，包括CPU、内存、磁盘等
 */
class PerformanceMonitor {
    constructor(logger) {
        this.logger = logger;
        
        // 监控配置
        this.config = {
            // 监控间隔（毫秒）
            monitorInterval: 5000,
            // 历史数据保留数量
            historySize: 100,
            // 告警阈值
            thresholds: {
                cpu: 80,        // CPU使用率告警阈值
                memory: 85,     // 内存使用率告警阈值
                disk: 90,       // 磁盘使用率告警阈值
                responseTime: 1000  // 响应时间告警阈值（毫秒）
            },
            // 是否启用告警
            alertsEnabled: true
        };
        
        // 监控数据历史
        this.history = {
            cpu: [],
            memory: [],
            disk: [],
            network: [],
            process: []
        };
        
        // 当前监控状态
        this.isMonitoring = false;
        this.monitoringInterval = null;
        
        // 告警状态
        this.alerts = {
            active: [],
            history: []
        };
        
        // 性能基准
        this.baseline = {
            cpu: 0,
            memory: 0,
            responseTime: 0,
            established: false
        };
        
        // 启动监控
        this.startMonitoring();
    }

    /**
     * 启动性能监控
     */
    startMonitoring() {
        if (this.isMonitoring) {
            return;
        }

        this.isMonitoring = true;
        this.monitoringInterval = setInterval(() => {
            this.collectMetrics();
        }, this.config.monitorInterval);

        this.logger.info('Performance monitoring started', {
            interval: this.config.monitorInterval,
            thresholds: this.config.thresholds
        });
    }

    /**
     * 停止性能监控
     */
    stopMonitoring() {
        if (!this.isMonitoring) {
            return;
        }

        this.isMonitoring = false;
        if (this.monitoringInterval) {
            clearInterval(this.monitoringInterval);
            this.monitoringInterval = null;
        }

        this.logger.info('Performance monitoring stopped');
    }

    /**
     * 收集性能指标
     */
    async collectMetrics() {
        try {
            const timestamp = Date.now();
            
            // 收集CPU指标
            const cpuMetrics = await this.getCPUMetrics();
            
            // 收集内存指标
            const memoryMetrics = this.getMemoryMetrics();
            
            // 收集磁盘指标
            const diskMetrics = await this.getDiskMetrics();
            
            // 收集网络指标
            const networkMetrics = this.getNetworkMetrics();
            
            // 收集进程指标
            const processMetrics = this.getProcessMetrics();
            
            // 存储历史数据
            this.addToHistory('cpu', { timestamp, ...cpuMetrics });
            this.addToHistory('memory', { timestamp, ...memoryMetrics });
            this.addToHistory('disk', { timestamp, ...diskMetrics });
            this.addToHistory('network', { timestamp, ...networkMetrics });
            this.addToHistory('process', { timestamp, ...processMetrics });
            
            // 检查告警条件
            if (this.config.alertsEnabled) {
                this.checkAlerts({
                    cpu: cpuMetrics,
                    memory: memoryMetrics,
                    disk: diskMetrics,
                    timestamp
                });
            }
            
            // 建立性能基准
            if (!this.baseline.established) {
                this.establishBaseline();
            }
            
        } catch (error) {
            this.logger.error('Failed to collect performance metrics', {
                error: error.message
            });
        }
    }

    /**
     * 获取CPU指标
     */
    async getCPUMetrics() {
        return new Promise((resolve) => {
            const startMeasure = this.getCPUUsage();
            
            setTimeout(() => {
                const endMeasure = this.getCPUUsage();
                const idleDifference = endMeasure.idle - startMeasure.idle;
                const totalDifference = endMeasure.total - startMeasure.total;
                const usage = 100 - (100 * idleDifference / totalDifference);
                
                resolve({
                    usage: Math.round(usage * 100) / 100,
                    cores: os.cpus().length,
                    loadAverage: os.loadavg(),
                    model: os.cpus()[0].model
                });
            }, 100);
        });
    }

    /**
     * 获取CPU使用情况
     */
    getCPUUsage() {
        const cpus = os.cpus();
        let idle = 0;
        let total = 0;
        
        for (const cpu of cpus) {
            for (const type in cpu.times) {
                total += cpu.times[type];
            }
            idle += cpu.times.idle;
        }
        
        return { idle, total };
    }

    /**
     * 获取内存指标
     */
    getMemoryMetrics() {
        const totalMemory = os.totalmem();
        const freeMemory = os.freemem();
        const usedMemory = totalMemory - freeMemory;
        const usage = (usedMemory / totalMemory) * 100;
        
        // 进程内存使用
        const processMemory = process.memoryUsage();
        
        return {
            total: totalMemory,
            free: freeMemory,
            used: usedMemory,
            usage: Math.round(usage * 100) / 100,
            process: {
                rss: processMemory.rss,
                heapTotal: processMemory.heapTotal,
                heapUsed: processMemory.heapUsed,
                external: processMemory.external
            }
        };
    }

    /**
     * 获取磁盘指标
     */
    async getDiskMetrics() {
        try {
            const stats = fs.statSync(process.cwd());
            const diskPath = process.platform === 'win32' ? 'C:' : '/';
            
            // 在Windows上使用不同的方法获取磁盘信息
            if (process.platform === 'win32') {
                return this.getWindowsDiskMetrics();
            } else {
                return this.getUnixDiskMetrics();
            }
        } catch (error) {
            return {
                total: 0,
                free: 0,
                used: 0,
                usage: 0,
                error: error.message
            };
        }
    }

    /**
     * 获取Windows磁盘指标
     */
    getWindowsDiskMetrics() {
        // 简化的磁盘监控，实际生产环境可以使用更精确的方法
        return {
            total: 0,
            free: 0,
            used: 0,
            usage: 0,
            platform: 'windows'
        };
    }

    /**
     * 获取Unix磁盘指标
     */
    getUnixDiskMetrics() {
        // 简化的磁盘监控，实际生产环境可以使用statvfs
        return {
            total: 0,
            free: 0,
            used: 0,
            usage: 0,
            platform: 'unix'
        };
    }

    /**
     * 获取网络指标
     */
    getNetworkMetrics() {
        const networkInterfaces = os.networkInterfaces();
        const interfaces = [];
        
        for (const [name, addresses] of Object.entries(networkInterfaces)) {
            const ipv4 = addresses.find(addr => addr.family === 'IPv4' && !addr.internal);
            if (ipv4) {
                interfaces.push({
                    name,
                    address: ipv4.address,
                    netmask: ipv4.netmask,
                    mac: ipv4.mac
                });
            }
        }
        
        return {
            interfaces,
            hostname: os.hostname()
        };
    }

    /**
     * 获取进程指标
     */
    getProcessMetrics() {
        return {
            pid: process.pid,
            uptime: process.uptime(),
            version: process.version,
            platform: process.platform,
            arch: process.arch,
            nodeVersion: process.version,
            v8Version: process.versions.v8
        };
    }

    /**
     * 添加到历史记录
     */
    addToHistory(type, data) {
        if (!this.history[type]) {
            this.history[type] = [];
        }
        
        this.history[type].push(data);
        
        // 保持历史记录大小限制
        if (this.history[type].length > this.config.historySize) {
            this.history[type].shift();
        }
    }

    /**
     * 检查告警条件
     */
    checkAlerts(metrics) {
        const alerts = [];
        
        // CPU告警
        if (metrics.cpu.usage > this.config.thresholds.cpu) {
            alerts.push({
                type: 'cpu',
                level: 'warning',
                message: `CPU使用率过高: ${metrics.cpu.usage.toFixed(2)}%`,
                value: metrics.cpu.usage,
                threshold: this.config.thresholds.cpu,
                timestamp: metrics.timestamp
            });
        }
        
        // 内存告警
        if (metrics.memory.usage > this.config.thresholds.memory) {
            alerts.push({
                type: 'memory',
                level: 'warning',
                message: `内存使用率过高: ${metrics.memory.usage.toFixed(2)}%`,
                value: metrics.memory.usage,
                threshold: this.config.thresholds.memory,
                timestamp: metrics.timestamp
            });
        }
        
        // 磁盘告警
        if (metrics.disk.usage > this.config.thresholds.disk) {
            alerts.push({
                type: 'disk',
                level: 'critical',
                message: `磁盘使用率过高: ${metrics.disk.usage.toFixed(2)}%`,
                value: metrics.disk.usage,
                threshold: this.config.thresholds.disk,
                timestamp: metrics.timestamp
            });
        }
        
        // 处理新告警
        for (const alert of alerts) {
            this.handleAlert(alert);
        }
        
        // 清除已解决的告警
        this.clearResolvedAlerts(metrics);
    }

    /**
     * 处理告警
     */
    handleAlert(alert) {
        // 检查是否已存在相同类型的活跃告警
        const existingAlert = this.alerts.active.find(a => a.type === alert.type);
        
        if (!existingAlert) {
            // 新告警
            this.alerts.active.push(alert);
            this.alerts.history.push({ ...alert, status: 'triggered' });
            
            this.logger.warn('Performance alert triggered', alert);
        } else {
            // 更新现有告警
            existingAlert.value = alert.value;
            existingAlert.timestamp = alert.timestamp;
        }
    }

    /**
     * 清除已解决的告警
     */
    clearResolvedAlerts(metrics) {
        const resolvedAlerts = [];
        
        this.alerts.active = this.alerts.active.filter(alert => {
            let isResolved = false;
            
            switch (alert.type) {
                case 'cpu':
                    isResolved = metrics.cpu.usage <= alert.threshold;
                    break;
                case 'memory':
                    isResolved = metrics.memory.usage <= alert.threshold;
                    break;
                case 'disk':
                    isResolved = metrics.disk.usage <= alert.threshold;
                    break;
            }
            
            if (isResolved) {
                resolvedAlerts.push(alert);
                this.alerts.history.push({ ...alert, status: 'resolved', resolvedAt: Date.now() });
                this.logger.info('Performance alert resolved', alert);
            }
            
            return !isResolved;
        });
    }

    /**
     * 建立性能基准
     */
    establishBaseline() {
        if (this.history.cpu.length < 10 || this.history.memory.length < 10) {
            return;
        }
        
        // 计算平均值作为基准
        const cpuAvg = this.history.cpu.slice(-10).reduce((sum, item) => sum + item.usage, 0) / 10;
        const memoryAvg = this.history.memory.slice(-10).reduce((sum, item) => sum + item.usage, 0) / 10;
        
        this.baseline = {
            cpu: cpuAvg,
            memory: memoryAvg,
            responseTime: 0,
            established: true,
            establishedAt: Date.now()
        };
        
        this.logger.info('Performance baseline established', this.baseline);
    }

    /**
     * 获取当前性能指标
     */
    getCurrentMetrics() {
        const latest = {
            cpu: this.history.cpu[this.history.cpu.length - 1] || null,
            memory: this.history.memory[this.history.memory.length - 1] || null,
            disk: this.history.disk[this.history.disk.length - 1] || null,
            network: this.history.network[this.history.network.length - 1] || null,
            process: this.history.process[this.history.process.length - 1] || null
        };
        
        return {
            current: latest,
            alerts: {
                active: this.alerts.active,
                count: this.alerts.active.length
            },
            baseline: this.baseline,
            monitoring: this.isMonitoring
        };
    }

    /**
     * 获取历史数据
     */
    getHistoryData(type, limit = 50) {
        if (!this.history[type]) {
            return [];
        }
        
        return this.history[type].slice(-limit);
    }

    /**
     * 获取性能报告
     */
    getPerformanceReport() {
        const now = Date.now();
        const oneHourAgo = now - (60 * 60 * 1000);
        
        // 过滤最近一小时的数据
        const recentCPU = this.history.cpu.filter(item => item.timestamp > oneHourAgo);
        const recentMemory = this.history.memory.filter(item => item.timestamp > oneHourAgo);
        
        const report = {
            period: {
                start: oneHourAgo,
                end: now,
                duration: '1 hour'
            },
            cpu: this.calculateStats(recentCPU, 'usage'),
            memory: this.calculateStats(recentMemory, 'usage'),
            alerts: {
                total: this.alerts.history.length,
                recent: this.alerts.history.filter(alert => alert.timestamp > oneHourAgo).length,
                active: this.alerts.active.length
            },
            baseline: this.baseline
        };
        
        return report;
    }

    /**
     * 计算统计数据
     */
    calculateStats(data, field) {
        if (data.length === 0) {
            return { min: 0, max: 0, avg: 0, count: 0 };
        }
        
        const values = data.map(item => item[field]);
        const min = Math.min(...values);
        const max = Math.max(...values);
        const avg = values.reduce((sum, val) => sum + val, 0) / values.length;
        
        return {
            min: Math.round(min * 100) / 100,
            max: Math.round(max * 100) / 100,
            avg: Math.round(avg * 100) / 100,
            count: data.length
        };
    }

    /**
     * 更新配置
     */
    updateConfig(newConfig) {
        this.config = { ...this.config, ...newConfig };
        
        // 如果监控间隔改变，重启监控
        if (newConfig.monitorInterval && this.isMonitoring) {
            this.stopMonitoring();
            this.startMonitoring();
        }
        
        this.logger.info('Performance monitor configuration updated', this.config);
    }

    /**
     * 获取配置
     */
    getConfig() {
        return { ...this.config };
    }
}

module.exports = PerformanceMonitor;
