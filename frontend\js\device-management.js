// 设备管理前端模块
class DeviceManagement {
    constructor() {
        this.devices = [];
        this.factories = [];
        this.currentPage = 1;
        this.pageSize = 10;
        this.totalPages = 1;
        this.filters = {};
        this.selectedDevices = new Set();
        this.searchTimeout = null;
        this.isInitialized = false;
        this.sortField = null;
        this.sortDirection = 'asc';
        this.isLoading = false; // 防止重复加载
        this.refreshDebounceTimer = null; // 防抖定时器
        this.init();
    }

    // 防抖工具函数
    debounce(func, wait) {
        return (...args) => {
            clearTimeout(this.refreshDebounceTimer);
            this.refreshDebounceTimer = setTimeout(() => func.apply(this, args), wait);
        };
    }

    // 初始化
    init() {
        if (this.isInitialized) {
            return; // 避免重复初始化
        }
        this.bindEvents();
        this.loadFactories();
        // 第一次初始化时不显示全屏加载器，避免页面切换时的闪烁
        this.loadDevices(false);

        // 初始化设备统计模块
        if (typeof initDeviceStats === 'function') {
            this.deviceStats = initDeviceStats();
        }

        // 初始化权限控制
        this.initPermissions();

        this.isInitialized = true;
    }

    // 初始化权限控制
    initPermissions() {
        // 获取当前用户信息
        this.currentUser = sessionStorage.getItem('username');
        this.currentRole = sessionStorage.getItem('role');
        this.currentDepartment = sessionStorage.getItem('department');

        // 根据权限控制按钮显示
        this.updateButtonPermissions();
    }

    // 更新按钮权限
    updateButtonPermissions() {
        const isAdmin = this.currentRole === 'admin';

        // 设备管理按钮权限控制
        const addDeviceBtn = document.getElementById('addDeviceBtn');
        const importDevicesBtn = document.getElementById('importDevicesBtn');
        const batchDeleteBtn = document.getElementById('batchDeleteDevicesBtn');
        const exportDevicesBtn = document.getElementById('exportDevicesBtn');

        if (addDeviceBtn) {
            if (isAdmin) {
                addDeviceBtn.style.display = '';
                addDeviceBtn.style.visibility = 'visible';
            } else {
                addDeviceBtn.style.display = 'none';
            }
        }
        if (importDevicesBtn) {
            if (isAdmin) {
                importDevicesBtn.style.display = '';
                importDevicesBtn.style.visibility = 'visible';
            } else {
                importDevicesBtn.style.display = 'none';
            }
        }
        if (batchDeleteBtn) {
            if (isAdmin) {
                batchDeleteBtn.style.display = '';
                batchDeleteBtn.style.visibility = 'visible';
            } else {
                batchDeleteBtn.style.display = 'none';
            }
        }
        if (exportDevicesBtn) {
            // 所有用户都可以导出设备数据
            exportDevicesBtn.style.display = '';
            exportDevicesBtn.style.visibility = 'visible';
        }
    }

    // 绑定事件
    bindEvents() {
        // 搜索功能 - 添加防抖机制
        const searchInput = document.getElementById('deviceSearchInput');
        if (searchInput) {
            searchInput.addEventListener('input', (e) => {
                this.filters.search = e.target.value;
                this.currentPage = 1;

                // 清除之前的搜索超时
                if (this.searchTimeout) {
                    clearTimeout(this.searchTimeout);
                }

                // 延迟搜索，避免频繁请求
                this.searchTimeout = setTimeout(() => {
                    this.loadDevices(false); // 搜索时不显示loading
                }, 300);
            });
        }

        // 状态筛选
        const statusFilter = document.getElementById('deviceStatusFilter');
        if (statusFilter) {
            statusFilter.addEventListener('change', (e) => {
                this.filters.status = e.target.value;
                this.currentPage = 1;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(statusFilter);
                this.loadDevices(false).finally(() => {
                    this.hideFilterLoading(statusFilter);
                });
            });
        }

        // 厂区筛选
        const factoryFilter = document.getElementById('deviceFactoryFilter');
        if (factoryFilter) {
            factoryFilter.addEventListener('change', (e) => {
                this.filters.factory = e.target.value;
                this.currentPage = 1;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(factoryFilter);
                this.loadDevices(false).finally(() => {
                    this.hideFilterLoading(factoryFilter);
                });
            });
        }

        // 位置筛选
        const locationFilter = document.getElementById('deviceLocationFilter');
        if (locationFilter) {
            locationFilter.addEventListener('change', (e) => {
                this.filters.location = e.target.value;
                this.currentPage = 1;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(locationFilter);
                this.loadDevices(false).finally(() => {
                    this.hideFilterLoading(locationFilter);
                });
            });
        }

        // 负责人筛选
        const responsibleFilter = document.getElementById('deviceResponsibleFilter');
        if (responsibleFilter) {
            responsibleFilter.addEventListener('change', (e) => {
                this.filters.responsible = e.target.value;
                this.currentPage = 1;
                // 使用局部加载指示器，不显示全屏遮罩
                this.showFilterLoading(responsibleFilter);
                this.loadDevices(false).finally(() => {
                    this.hideFilterLoading(responsibleFilter);
                });
            });
        }

        // 添加设备按钮
        const addDeviceBtn = document.getElementById('addDeviceBtn');
        if (addDeviceBtn) {
            addDeviceBtn.addEventListener('click', () => this.showAddDeviceModal());
        }

        // 导入设备按钮
        const importDevicesBtn = document.getElementById('importDevicesBtn');
        if (importDevicesBtn) {
            importDevicesBtn.addEventListener('click', () => this.showImportModal());
        }

        // 批量删除按钮
        const batchDeleteBtn = document.getElementById('batchDeleteDevicesBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.addEventListener('click', () => this.batchDeleteDevices());
        }

        // 导出设备列表按钮 - 防止重复绑定
        const exportDevicesBtn = document.getElementById('exportDevicesBtn');
        if (exportDevicesBtn) {
            // 移除之前的事件监听器（如果存在）
            exportDevicesBtn.removeEventListener('click', this.exportDevicesHandler);
            // 创建绑定的处理函数
            this.exportDevicesHandler = () => this.exportDevices();
            exportDevicesBtn.addEventListener('click', this.exportDevicesHandler);
        }

        // 刷新按钮 - 添加防抖机制
        const refreshDevicesBtn = document.getElementById('refreshDevicesBtn');
        if (refreshDevicesBtn) {
            // 移除之前的事件监听器
            refreshDevicesBtn.removeEventListener('click', this.debouncedDeviceRefresh);

            // 创建防抖刷新函数
            this.debouncedDeviceRefresh = this.debounce(() => {
                if (this.isLoading) {
                    console.log('设备数据正在加载中，跳过重复请求');
                    return;
                }
                console.log('执行设备数据刷新');
                // 使用按钮内的加载动画，不显示全屏遮罩
                this.showButtonLoading(refreshDevicesBtn, '刷新中...');
                this.loadDevices(false).finally(() => {
                    this.hideButtonLoading(refreshDevicesBtn, '刷新');
                });
            }, 1000); // 1秒防抖

            // 绑定新的事件监听器
            refreshDevicesBtn.addEventListener('click', this.debouncedDeviceRefresh);
        }

        // 全选复选框
        const selectAllCheckbox = document.getElementById('selectAllDevices');
        if (selectAllCheckbox) {
            selectAllCheckbox.addEventListener('change', (e) => this.toggleSelectAll(e.target.checked));
        }

        // 表格排序功能
        document.querySelectorAll('.sortable').forEach(header => {
            header.addEventListener('click', (e) => {
                const sortField = e.currentTarget.dataset.sort;
                this.sortTable(sortField);
            });
        });
    }

    // 加载厂区列表
    async loadFactories() {
        try {
            const response = await apiRequest('/api/factories');
            if (response.success) {
                this.factories = response.factories;
                this.updateFactorySelects();

                // 如果设备数据已加载，更新统计
                if (this.deviceStats && this.devices.length > 0) {
                    this.deviceStats.updateStats(this.devices, this.factories);
                }
            } else {
                console.error('加载厂区列表失败:', response.message);
            }
        } catch (error) {
            console.error('加载厂区列表失败:', error);
        }
    }

    // 重新加载厂区数据（供厂区管理调用）
    async reloadFactories() {
        await this.loadFactories();
    }

    // 更新厂区选择框
    updateFactorySelects() {
        // 更新设备表单中的厂区选择
        const deviceFactorySelect = document.getElementById('deviceFactory');
        if (deviceFactorySelect) {
            deviceFactorySelect.innerHTML = '<option value="">请选择厂区</option>' +
                this.factories.map(factory =>
                    `<option value="${factory.id}">${factory.name}</option>`
                ).join('');
        }

        // 更新筛选器中的厂区选择
        const factoryFilter = document.getElementById('deviceFactoryFilter');
        if (factoryFilter) {
            factoryFilter.innerHTML = '<option value="">全部厂区</option>' +
                this.factories.map(factory =>
                    `<option value="${factory.id}">${factory.name}</option>`
                ).join('');
        }
    }

    // 更新位置和负责人选择框
    updateLocationAndResponsibleSelects() {
        // 更新位置选择框
        const locationFilter = document.getElementById('deviceLocationFilter');
        if (locationFilter) {
            const currentValue = locationFilter.value;
            const locations = [...new Set(this.devices.map(device => device.location).filter(Boolean))].sort();

            locationFilter.innerHTML = '<option value="">全部位置</option>';
            locations.forEach(location => {
                const option = document.createElement('option');
                option.value = location;
                option.textContent = location;
                locationFilter.appendChild(option);
            });
            locationFilter.value = currentValue;
        }

        // 更新负责人选择框
        const responsibleFilter = document.getElementById('deviceResponsibleFilter');
        if (responsibleFilter) {
            const currentValue = responsibleFilter.value;
            const responsibles = [...new Set(this.devices.map(device => device.responsible).filter(Boolean))].sort();

            responsibleFilter.innerHTML = '<option value="">全部负责人</option>';
            responsibles.forEach(responsible => {
                const option = document.createElement('option');
                option.value = responsible;
                option.textContent = responsible;
                responsibleFilter.appendChild(option);
            });
            responsibleFilter.value = currentValue;
        }
    }

    // 加载设备列表
    async loadDevices(showLoadingIndicator = true) {
        if (this.isLoading) {
            console.log('设备数据正在加载中，跳过重复请求');
            return;
        }

        try {
            this.isLoading = true;
            console.log('开始加载设备列表数据');

            if (showLoadingIndicator) {
                showLoading('正在加载设备列表...');
            }

            const queryParams = new URLSearchParams();
            Object.keys(this.filters).forEach(key => {
                if (this.filters[key]) {
                    queryParams.append(key, this.filters[key]);
                }
            });

            const response = await apiRequest(`/api/devices?${queryParams.toString()}`);

            if (response.success) {
                this.devices = response.devices;

                // 如果有排序状态，重新应用排序
                if (this.sortField) {
                    this.applySorting();
                } else {
                    this.updateDeviceTable();
                    this.updatePagination();
                }

                this.updateSelectedCount();
                this.updateLocationAndResponsibleSelects();

                // 更新统计数据和图表
                if (this.deviceStats) {
                    this.deviceStats.updateStats(this.devices, this.factories);
                }

                console.log('设备列表数据加载成功');
            } else {
                showError('加载设备列表失败: ' + response.message);
            }
        } catch (error) {
            console.error('加载设备列表失败:', error);
            showError('加载设备列表失败，请检查网络连接');
        } finally {
            this.isLoading = false;
            if (showLoadingIndicator) {
                hideLoading();
            }
        }
    }

    // 更新设备表格
    updateDeviceTable() {
        const tbody = document.getElementById('deviceTableBody');
        if (!tbody) return;

        if (this.devices.length === 0) {
            tbody.innerHTML = `
                <tr>
                    <td colspan="9" class="px-6 py-4 text-center text-gray-500">
                        暂无设备数据
                    </td>
                </tr>
            `;
            return;
        }

        // 计算分页
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageDevices = this.devices.slice(startIndex, endIndex);
        this.totalPages = Math.ceil(this.devices.length / this.pageSize);

        tbody.innerHTML = pageDevices.map(device => {
            // 格式化进厂日期显示
            let entryDateDisplay = '-';
            if (device.entryDate) {
                entryDateDisplay = device.entryDate;
            } else if (device.createTime) {
                // 如果没有进厂日期，使用创建时间的日期部分作为显示
                entryDateDisplay = new Date(device.createTime).toISOString().split('T')[0];
            }

            return `
                <tr class="hover:bg-gray-50">
                    <td class="px-6 py-4 whitespace-nowrap">
                        <input type="checkbox"
                               class="device-checkbox rounded border-gray-300 text-blue-600 focus:ring-blue-500"
                               value="${device.id}"
                               ${this.selectedDevices.has(device.id) ? 'checked' : ''}
                               onchange="deviceManager.toggleDeviceSelection('${device.id}', this.checked)">
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium text-gray-900">
                        ${device.deviceCode}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${device.deviceName}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${this.getFactoryName(device.factory)}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${device.location}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${device.responsible}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                        ${entryDateDisplay}
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap">
                        <span class="px-2 inline-flex text-xs leading-5 font-semibold rounded-full ${
                            device.status === '启用'
                                ? 'bg-green-100 text-green-800'
                                : 'bg-red-100 text-red-800'
                        }">
                            ${device.status}
                        </span>
                    </td>
                    <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                        <button onclick="deviceManager.viewDevice('${device.id}')"
                                class="text-blue-600 hover:text-blue-900 mr-3">查看</button>
                        ${this.currentRole === 'admin' ? `
                        <button onclick="deviceManager.editDevice('${device.id}')"
                                class="text-indigo-600 hover:text-indigo-900 mr-3">编辑</button>
                        <button onclick="deviceManager.deleteDevice('${device.id}')"
                                class="text-red-600 hover:text-red-900">删除</button>
                        ` : ''}
                    </td>
                </tr>
            `;
        }).join('');
    }

    // 获取厂区名称
    getFactoryName(factoryId) {
        const factory = this.factories.find(f => f.id === factoryId);
        return factory ? factory.name : factoryId || '未知厂区';
    }

    // 更新分页
    updatePagination() {
        const pagination = document.getElementById('devicePagination');
        if (!pagination) return;

        const totalItems = this.devices.length;

        pagination.innerHTML = `
            <div class="flex items-center justify-between">
                <div class="text-sm text-gray-500">
                    共 <span id="deviceTotalItems">${totalItems}</span> 条记录，每页 ${this.pageSize} 条
                </div>
                <div class="flex space-x-2">
                    <button id="devicePrevPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed" ${this.currentPage <= 1 ? 'disabled' : ''}>上一页</button>
                    <div id="devicePageNumbers" class="flex space-x-1"></div>
                    <button id="deviceNextPage" class="px-3 py-1 border rounded-md bg-gray-100 hover:bg-gray-200 disabled:opacity-50 disabled:cursor-not-allowed" ${this.currentPage >= this.totalPages ? 'disabled' : ''}>下一页</button>
                </div>
            </div>
        `;

        // 设置按钮点击事件
        document.getElementById('devicePrevPage').onclick = () => this.goToPage(this.currentPage - 1);
        document.getElementById('deviceNextPage').onclick = () => this.goToPage(this.currentPage + 1);

        // 生成页码按钮
        this.generatePageNumbers();
    }

    // 生成页码按钮
    generatePageNumbers() {
        const pageNumbersContainer = document.getElementById('devicePageNumbers');
        if (!pageNumbersContainer) return;

        pageNumbersContainer.innerHTML = '';

        // 确定要显示的页码范围
        let startPage = Math.max(1, this.currentPage - 2);
        let endPage = Math.min(this.totalPages, startPage + 4);

        // 调整起始页，确保始终显示5个页码（如果有足够的页数）
        if (endPage - startPage < 4 && this.totalPages > 5) {
            startPage = Math.max(1, endPage - 4);
        }

        // 添加第一页按钮（如果不在显示范围内）
        if (startPage > 1) {
            const firstPageBtn = document.createElement('button');
            firstPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            firstPageBtn.textContent = '1';
            firstPageBtn.onclick = () => this.goToPage(1);
            pageNumbersContainer.appendChild(firstPageBtn);

            // 添加省略号（如果第一页和起始页之间有间隔）
            if (startPage > 2) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }
        }

        // 添加页码按钮
        for (let i = startPage; i <= endPage; i++) {
            const pageBtn = document.createElement('button');
            pageBtn.className = `px-3 py-1 border rounded-md ${i === this.currentPage ? 'bg-blue-600 text-white' : 'hover:bg-gray-200'}`;
            pageBtn.textContent = i;
            pageBtn.onclick = () => this.goToPage(i);
            pageNumbersContainer.appendChild(pageBtn);
        }

        // 添加最后一页按钮（如果不在显示范围内）
        if (endPage < this.totalPages) {
            // 添加省略号（如果结束页和最后一页之间有间隔）
            if (endPage < this.totalPages - 1) {
                const ellipsis = document.createElement('span');
                ellipsis.className = 'px-2 py-1';
                ellipsis.textContent = '...';
                pageNumbersContainer.appendChild(ellipsis);
            }

            const lastPageBtn = document.createElement('button');
            lastPageBtn.className = 'px-3 py-1 border rounded-md hover:bg-gray-200';
            lastPageBtn.textContent = this.totalPages;
            lastPageBtn.onclick = () => this.goToPage(this.totalPages);
            pageNumbersContainer.appendChild(lastPageBtn);
        }
    }

    // 跳转到指定页面
    goToPage(page) {
        if (page < 1 || page > this.totalPages) return;
        this.currentPage = page;
        // 直接更新表格和分页显示，不重新加载数据
        // 这样可以避免全屏加载器的闪烁问题
        this.updateDeviceTable();
        this.updatePagination();
    }

    // 表格排序功能
    sortTable(field) {
        // 如果点击的是同一个字段，切换排序方向
        if (this.sortField === field) {
            this.sortDirection = this.sortDirection === 'asc' ? 'desc' : 'asc';
        } else {
            // 如果是新字段，默认升序
            this.sortField = field;
            this.sortDirection = 'asc';
        }

        // 更新排序图标
        this.updateSortIcons();

        // 执行排序，用户主动排序时回到第一页
        this.applySorting(true);
    }

    // 应用排序到设备数据
    applySorting(resetPage = false) {
        if (!this.sortField) return;

        this.devices.sort((a, b) => {
            let aValue = a[this.sortField] || '';
            let bValue = b[this.sortField] || '';

            // 特殊处理厂区字段 - 按厂区名称排序
            if (this.sortField === 'factory') {
                aValue = this.getFactoryName(aValue);
                bValue = this.getFactoryName(bValue);
            }

            // 特殊处理日期字段
            if (this.sortField === 'entryDate') {
                aValue = new Date(aValue || '1900-01-01');
                bValue = new Date(bValue || '1900-01-01');
            }

            // 特殊处理状态字段 - 按状态权重排序
            if (this.sortField === 'status') {
                const statusOrder = {
                    '启用': 1,
                    '停用': 2
                };
                aValue = statusOrder[aValue] || 999;
                bValue = statusOrder[bValue] || 999;
            } else if (typeof aValue === 'string' && typeof bValue === 'string') {
                // 字符串比较，忽略大小写
                aValue = aValue.toLowerCase();
                bValue = bValue.toLowerCase();
            }

            let result = 0;
            if (aValue < bValue) result = -1;
            else if (aValue > bValue) result = 1;

            return this.sortDirection === 'desc' ? -result : result;
        });

        // 重新渲染表格
        if (resetPage) {
            this.currentPage = 1; // 只有用户主动排序时才回到第一页
        }
        this.updateDeviceTable();
        this.updatePagination();
    }

    // 更新排序图标
    updateSortIcons() {
        document.querySelectorAll('.sortable svg').forEach(icon => {
            // 使用 setAttribute 来设置 SVG 元素的 class
            icon.setAttribute('class', 'w-4 h-4 inline ml-1 text-gray-400');
            icon.innerHTML = '<path d="M5 12l5-5 5 5H5z"/>';
        });

        if (this.sortField) {
            const activeHeader = document.querySelector(`[data-sort="${this.sortField}"] svg`);
            if (activeHeader) {
                // 使用 setAttribute 来设置 SVG 元素的 class
                activeHeader.setAttribute('class', 'w-4 h-4 inline ml-1 text-blue-600');
                if (this.sortDirection === 'desc') {
                    activeHeader.innerHTML = '<path d="M15 8l-5 5-5-5h10z"/>';
                } else {
                    activeHeader.innerHTML = '<path d="M5 12l5-5 5 5H5z"/>';
                }
            }
        }
    }

    // 切换设备选择状态
    toggleDeviceSelection(deviceId, checked) {
        if (checked) {
            this.selectedDevices.add(deviceId);
        } else {
            this.selectedDevices.delete(deviceId);
        }
        this.updateSelectedCount();
        this.updateSelectAllCheckbox();
    }

    // 切换全选状态
    toggleSelectAll(checked) {
        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageDevices = this.devices.slice(startIndex, endIndex);

        pageDevices.forEach(device => {
            if (checked) {
                this.selectedDevices.add(device.id);
            } else {
                this.selectedDevices.delete(device.id);
            }
        });

        // 更新页面上的复选框
        document.querySelectorAll('.device-checkbox').forEach(checkbox => {
            checkbox.checked = checked;
        });

        this.updateSelectedCount();
    }

    // 更新全选复选框状态
    updateSelectAllCheckbox() {
        const selectAllCheckbox = document.getElementById('selectAllDevices');
        if (!selectAllCheckbox) return;

        const startIndex = (this.currentPage - 1) * this.pageSize;
        const endIndex = startIndex + this.pageSize;
        const pageDevices = this.devices.slice(startIndex, endIndex);

        const selectedPageDevices = pageDevices.filter(device => this.selectedDevices.has(device.id));

        if (selectedPageDevices.length === 0) {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = false;
        } else if (selectedPageDevices.length === pageDevices.length) {
            selectAllCheckbox.checked = true;
            selectAllCheckbox.indeterminate = false;
        } else {
            selectAllCheckbox.checked = false;
            selectAllCheckbox.indeterminate = true;
        }
    }

    // 更新选中数量显示
    updateSelectedCount() {
        const countElement = document.getElementById('selectedDeviceCount');
        if (countElement) {
            const count = this.selectedDevices.size;
            countElement.textContent = count > 0 ? `已选择 ${count} 个设备` : '';
        }

        // 更新批量操作按钮状态
        const batchDeleteBtn = document.getElementById('batchDeleteDevicesBtn');
        if (batchDeleteBtn) {
            batchDeleteBtn.disabled = this.selectedDevices.size === 0;
            batchDeleteBtn.classList.toggle('opacity-50', this.selectedDevices.size === 0);
        }
    }

    // 显示添加设备模态框
    showAddDeviceModal() {
        this.showDeviceModal();
    }

    // 查看设备详情
    async viewDevice(deviceId) {
        try {
            const response = await apiRequest(`/api/devices/${deviceId}`);
            if (response.success) {
                this.showDeviceModal(response.device, true);
            } else {
                showError('获取设备详情失败: ' + response.message);
            }
        } catch (error) {
            console.error('获取设备详情失败:', error);
            showError('获取设备详情失败');
        }
    }

    // 编辑设备
    async editDevice(deviceId) {
        try {
            const response = await apiRequest(`/api/devices/${deviceId}`);
            if (response.success) {
                this.showDeviceModal(response.device, false);
            } else {
                showError('获取设备信息失败: ' + response.message);
            }
        } catch (error) {
            console.error('获取设备信息失败:', error);
            showError('获取设备信息失败');
        }
    }

    // 删除设备
    async deleteDevice(deviceId, buttonElement = null) {
        // 权限检查：只有管理员可以删除设备
        if (this.currentRole !== 'admin') {
            showError('权限不足，只有管理员可以删除设备');
            return;
        }

        if (!confirm('确定要删除这个设备吗？删除后无法恢复。')) {
            return;
        }

        try {
            // 如果有按钮元素，使用按钮内加载状态
            if (buttonElement) {
                this.showButtonLoading(buttonElement, '删除中...');
            } else {
                showLoading('正在删除设备...');
            }

            const response = await apiRequest(`/api/devices/${deviceId}`, {
                method: 'DELETE',
                data: {
                    username: this.currentUser,
                    role: this.currentRole,
                    department: this.currentDepartment
                }
            });

            if (response.success) {
                showSuccess(response.message);
                this.selectedDevices.delete(deviceId);
                this.loadDevices(false); // 不显示全屏加载器
            } else {
                showError('删除设备失败: ' + response.message);
            }
        } catch (error) {
            console.error('删除设备失败:', error);
            showError('删除设备失败');
        } finally {
            if (buttonElement) {
                this.hideButtonLoading(buttonElement, '删除');
            } else {
                hideLoading();
            }
        }
    }

    // 批量删除设备
    async batchDeleteDevices() {
        // 权限检查：只有管理员可以批量删除设备
        if (this.currentRole !== 'admin') {
            showError('权限不足，只有管理员可以删除设备');
            return;
        }

        if (this.selectedDevices.size === 0) {
            showError('请先选择要删除的设备');
            return;
        }

        if (!confirm(`确定要删除选中的 ${this.selectedDevices.size} 个设备吗？删除后无法恢复。`)) {
            return;
        }

        try {
            showLoading('正在批量删除设备...');
            const response = await apiRequest('/api/devices', {
                method: 'DELETE',
                data: {
                    ids: Array.from(this.selectedDevices),
                    username: this.currentUser,
                    role: this.currentRole,
                    department: this.currentDepartment
                }
            });

            if (response.success) {
                showSuccess(response.message);
                this.selectedDevices.clear();
                this.loadDevices();
            } else {
                showError('批量删除设备失败: ' + response.message);
            }
        } catch (error) {
            console.error('批量删除设备失败:', error);
            showError('批量删除设备失败');
        } finally {
            hideLoading();
        }
    }

    // 导出设备列表
    async exportDevices() {
        try {
            showLoading('正在导出设备列表...');

            const filename = `设备列表_${new Date().toISOString().slice(0, 10)}.xlsx`;

            const response = await fetch('/api/export/devices', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    filters: this.filters,
                    filename: filename
                })
            });

            if (response.ok) {
                // 获取文件名
                const contentDisposition = response.headers.get('Content-Disposition');
                let downloadFilename = filename;
                if (contentDisposition) {
                    const matches = contentDisposition.match(/filename="(.+)"/);
                    if (matches) {
                        downloadFilename = decodeURIComponent(matches[1]);
                    }
                }

                // 下载文件
                const blob = await response.blob();
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement('a');
                a.href = url;
                a.download = downloadFilename;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);

                showSuccess('设备列表导出成功！');
            } else {
                const errorData = await response.json();
                showError('导出设备列表失败: ' + (errorData.message || '未知错误'));
            }
        } catch (error) {
            console.error('导出设备列表失败:', error);
            showError('导出设备列表失败');
        } finally {
            hideLoading();
        }
    }

    // 显示设备模态框
    showDeviceModal(device = null, readonly = false) {
        const modal = document.getElementById('deviceModal');
        const form = document.getElementById('deviceForm');
        const title = document.getElementById('deviceModalTitle');

        if (!modal || !form || !title) return;

        // 权限检查：非管理员用户只能查看
        const isAdmin = this.currentRole === 'admin';
        if (!isAdmin && !readonly) {
            this.showDeviceModal(device, true); // 强制只读模式
            return;
        }

        // 设置标题
        if (readonly) {
            title.textContent = '设备详情';
        } else if (device) {
            title.textContent = '编辑设备';
        } else {
            title.textContent = '添加设备';
        }

        // 重置表单
        form.reset();

        // 填充表单数据
        if (device) {
            document.getElementById('deviceCode').value = device.deviceCode || '';
            document.getElementById('deviceName').value = device.deviceName || '';
            document.getElementById('deviceFactory').value = device.factory || '';
            document.getElementById('deviceLocation').value = device.location || '';
            document.getElementById('deviceResponsible').value = device.responsible || '';

            // 处理进厂日期 - 如果没有进厂日期，使用创建时间作为默认值
            let entryDate = device.entryDate;
            if (!entryDate && device.createTime) {
                // 将创建时间转换为日期格式
                entryDate = new Date(device.createTime).toISOString().split('T')[0];
            }
            document.getElementById('deviceEntryDate').value = entryDate || '';

            document.getElementById('deviceStatus').value = device.status || '启用';
        }

        // 设置只读状态
        const inputs = form.querySelectorAll('input, select, textarea');
        inputs.forEach(input => {
            input.disabled = readonly;
        });

        // 设置按钮状态
        const saveBtn = document.getElementById('saveDeviceBtn');
        const cancelBtn = document.getElementById('cancelDeviceBtn');

        if (saveBtn) {
            saveBtn.style.display = readonly ? 'none' : 'block';
            saveBtn.textContent = device ? '更新设备' : '添加设备';
        }

        if (cancelBtn) {
            cancelBtn.textContent = readonly ? '关闭' : '取消';
        }

        // 绑定保存事件
        if (!readonly) {
            const newSaveBtn = saveBtn.cloneNode(true);
            saveBtn.parentNode.replaceChild(newSaveBtn, saveBtn);

            newSaveBtn.addEventListener('click', () => {
                this.saveDevice(device ? device.id : null);
            });
        }

        // 显示模态框
        modal.classList.remove('hidden');
        document.body.classList.add('modal-open');
    }

    // 隐藏设备模态框
    hideDeviceModal() {
        const modal = document.getElementById('deviceModal');
        if (modal) {
            modal.classList.add('hidden');
            document.body.classList.remove('modal-open');
        }
    }

    // 保存设备
    async saveDevice(deviceId = null) {
        const form = document.getElementById('deviceForm');
        if (!form) return;

        const formData = new FormData(form);
        const deviceData = {
            deviceCode: formData.get('deviceCode'),
            deviceName: formData.get('deviceName'),
            factory: formData.get('factory'),
            location: formData.get('location'),
            responsible: formData.get('responsible'),
            entryDate: formData.get('entryDate'),
            status: formData.get('status')
        };

        // 验证必填字段
        if (!deviceData.deviceCode || !deviceData.deviceName || !deviceData.factory || !deviceData.location || !deviceData.responsible || !deviceData.entryDate) {
            showError('请填写所有必填字段');
            return;
        }

        // 验证进厂日期不能是未来日期
        const entryDate = new Date(deviceData.entryDate);
        const today = new Date();
        today.setHours(23, 59, 59, 999); // 设置为今天的最后一刻
        if (entryDate > today) {
            showError('设备进厂日期不能是未来日期');
            return;
        }

        try {
            // 获取保存按钮并显示加载状态
            const saveButton = document.querySelector('#deviceModal .bg-blue-500');
            if (saveButton) {
                this.showButtonLoading(saveButton, deviceId ? '更新中...' : '添加中...');
            } else {
                showLoading(deviceId ? '正在更新设备...' : '正在添加设备...');
            }

            // 添加用户权限信息
            deviceData.username = this.currentUser;
            deviceData.role = this.currentRole;
            deviceData.department = this.currentDepartment;

            const url = deviceId ? `/api/devices/${deviceId}` : '/api/devices';
            const method = deviceId ? 'PUT' : 'POST';

            const response = await apiRequest(url, {
                method: method,
                data: deviceData
            });

            if (response.success) {
                showSuccess(response.message);
                this.hideDeviceModal();
                this.loadDevices(false); // 不显示全屏加载器
            } else {
                showError(response.message);
            }
        } catch (error) {
            console.error('保存设备失败:', error);
            showError('保存设备失败');
        } finally {
            const saveButton = document.querySelector('#deviceModal .bg-blue-500');
            if (saveButton) {
                this.hideButtonLoading(saveButton, deviceId ? '更新设备' : '添加设备');
            } else {
                hideLoading();
            }
        }
    }

    // 显示导入模态框
    showImportModal() {
        const modal = document.getElementById('deviceImportModal');
        if (modal) {
            modal.classList.remove('hidden');
            this.bindImportEvents();
        }
    }

    // 隐藏导入模态框
    hideImportModal() {
        const modal = document.getElementById('deviceImportModal');
        if (modal) {
            modal.classList.add('hidden');
            this.resetImportForm();
        }
    }

    // 绑定导入相关事件
    bindImportEvents() {
        // 取消按钮
        const cancelBtn = document.getElementById('cancelImportBtn');
        if (cancelBtn) {
            cancelBtn.onclick = () => this.hideImportModal();
        }

        // 开始导入按钮
        const startBtn = document.getElementById('startImportBtn');
        if (startBtn) {
            startBtn.onclick = () => this.startImport();
        }

        // 文件选择事件
        const fileInput = document.getElementById('deviceImportFile');
        if (fileInput) {
            fileInput.onchange = (e) => this.handleFileSelect(e);
        }
    }

    // 重置导入表单
    resetImportForm() {
        const fileInput = document.getElementById('deviceImportFile');
        const progressDiv = document.getElementById('importProgress');
        const resultDiv = document.getElementById('importResult');
        const startBtn = document.getElementById('startImportBtn');

        if (fileInput) fileInput.value = '';
        if (progressDiv) progressDiv.classList.add('hidden');
        if (resultDiv) resultDiv.classList.add('hidden');
        if (startBtn) {
            startBtn.textContent = '开始导入';
            startBtn.disabled = false;
        }
    }

    // 处理文件选择
    handleFileSelect(event) {
        const file = event.target.files[0];
        const startBtn = document.getElementById('startImportBtn');

        if (file && startBtn) {
            // 验证文件类型
            const validTypes = [
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
                'application/vnd.ms-excel'
            ];

            if (validTypes.includes(file.type) || file.name.endsWith('.xlsx') || file.name.endsWith('.xls')) {
                startBtn.disabled = false;
                startBtn.textContent = '开始导入';
            } else {
                startBtn.disabled = true;
                startBtn.textContent = '请选择Excel文件';
                showError('请选择有效的Excel文件(.xlsx或.xls)');
            }
        }
    }

    // 开始导入
    async startImport() {
        const fileInput = document.getElementById('deviceImportFile');
        const file = fileInput?.files[0];

        if (!file) {
            showError('请选择要导入的Excel文件');
            return;
        }

        const skipDuplicates = document.getElementById('skipDuplicates')?.checked || false;
        const updateExisting = document.getElementById('updateExisting')?.checked || false;

        try {
            this.showImportProgress(0, '准备导入...');

            const formData = new FormData();
            formData.append('file', file);
            formData.append('skipDuplicates', skipDuplicates);
            formData.append('updateExisting', updateExisting);
            formData.append('username', this.currentUser);

            const response = await fetch('/api/devices/import', {
                method: 'POST',
                body: formData
            });

            const result = await response.json();

            if (result.success) {
                this.showImportResult(result);
                this.loadDevices(); // 重新加载设备列表
            } else {
                this.showImportError(result.message, result.errors);
            }
        } catch (error) {
            console.error('导入设备失败:', error);
            this.showImportError('导入失败: ' + error.message);
        }
    }

    // 显示导入进度
    showImportProgress(percent, text) {
        const progressDiv = document.getElementById('importProgress');
        const progressBar = document.getElementById('importProgressBar');
        const progressText = document.getElementById('importProgressText');
        const startBtn = document.getElementById('startImportBtn');

        if (progressDiv) progressDiv.classList.remove('hidden');
        if (progressBar) progressBar.style.width = percent + '%';
        if (progressText) progressText.textContent = text;
        if (startBtn) {
            startBtn.textContent = '导入中...';
            startBtn.disabled = true;
        }
    }

    // 显示导入结果
    showImportResult(result) {
        const resultDiv = document.getElementById('importResult');
        const resultContent = document.getElementById('importResultContent');
        const startBtn = document.getElementById('startImportBtn');

        if (resultDiv && resultContent) {
            resultDiv.classList.remove('hidden');

            let html = `
                <div class="bg-green-50 border border-green-200 rounded-lg">
                    <h4 class="text-green-800 font-medium mb-2">导入成功！</h4>
                    <div class="text-sm text-green-700 space-y-1">
                        <p>• 成功导入 ${result.imported || 0} 条设备记录</p>
                        <p>• 更新 ${result.updated || 0} 条现有记录</p>
                        <p>• 跳过 ${result.skipped || 0} 条重复记录</p>
                    </div>
                </div>
            `;

            if (result.errors && result.errors.length > 0) {
                html += `
                    <div class="bg-yellow-50 border border-yellow-200 rounded-lg mt-3">
                        <h4 class="text-yellow-800 font-medium mb-2">警告信息</h4>
                        <div class="text-sm text-yellow-700 space-y-1">
                            ${result.errors.map(error => `<p>• ${error}</p>`).join('')}
                        </div>
                    </div>
                `;
            }

            resultContent.innerHTML = html;
        }

        if (startBtn) {
            startBtn.textContent = '导入完成';
            startBtn.disabled = true;
        }

        // 隐藏进度条
        const progressDiv = document.getElementById('importProgress');
        if (progressDiv) progressDiv.classList.add('hidden');
    }

    // 显示导入错误
    showImportError(message, errors = []) {
        const resultDiv = document.getElementById('importResult');
        const resultContent = document.getElementById('importResultContent');
        const startBtn = document.getElementById('startImportBtn');

        if (resultDiv && resultContent) {
            resultDiv.classList.remove('hidden');

            let html = `
                <div class="bg-red-50 border border-red-200 rounded-lg">
                    <h4 class="text-red-800 font-medium mb-2">导入失败</h4>
                    <p class="text-sm text-red-700 mb-2">${message}</p>
            `;

            if (errors && errors.length > 0) {
                html += `
                    <div class="text-sm text-red-700 space-y-1">
                        <p class="font-medium">错误详情：</p>
                        ${errors.map(error => `<p>• ${error}</p>`).join('')}
                    </div>
                `;
            }

            html += '</div>';
            resultContent.innerHTML = html;
        }

        if (startBtn) {
            startBtn.textContent = '重新导入';
            startBtn.disabled = false;
        }

        // 隐藏进度条
        const progressDiv = document.getElementById('importProgress');
        if (progressDiv) progressDiv.classList.add('hidden');
    }

    // 显示筛选器加载状态
    showFilterLoading(selectElement) {
        if (!selectElement) return;

        selectElement.disabled = true;
        selectElement.style.opacity = '0.6';

        // 添加加载指示器
        const loadingIndicator = document.createElement('div');
        loadingIndicator.className = 'filter-loading-indicator';
        loadingIndicator.innerHTML = `
            <div class="inline-flex items-center">
                <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-blue-500" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                    <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                    <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
                </svg>
                <span class="text-sm text-gray-600">加载中...</span>
            </div>
        `;
        loadingIndicator.style.cssText = `
            position: absolute;
            top: 50%;
            right: 30px;
            transform: translateY(-50%);
            z-index: 10;
            pointer-events: none;
        `;

        // 将指示器添加到父容器
        const parent = selectElement.parentElement;
        if (parent) {
            parent.style.position = 'relative';
            parent.appendChild(loadingIndicator);
        }
    }

    // 隐藏筛选器加载状态
    hideFilterLoading(selectElement) {
        if (!selectElement) return;

        selectElement.disabled = false;
        selectElement.style.opacity = '1';

        // 移除加载指示器
        const parent = selectElement.parentElement;
        if (parent) {
            const loadingIndicator = parent.querySelector('.filter-loading-indicator');
            if (loadingIndicator) {
                loadingIndicator.remove();
            }
        }
    }

    // 显示按钮加载状态
    showButtonLoading(buttonElement, loadingText = '加载中...') {
        if (!buttonElement) return;

        // 保存原始状态
        buttonElement.dataset.originalText = buttonElement.textContent;
        buttonElement.dataset.originalDisabled = buttonElement.disabled;

        // 设置加载状态
        buttonElement.disabled = true;
        buttonElement.innerHTML = `
            <svg class="animate-spin -ml-1 mr-2 h-4 w-4 text-white inline" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24">
                <circle class="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" stroke-width="4"></circle>
                <path class="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
            </svg>
            ${loadingText}
        `;
    }

    // 隐藏按钮加载状态
    hideButtonLoading(buttonElement, originalText = null) {
        if (!buttonElement) return;

        // 恢复原始状态
        const savedText = buttonElement.dataset.originalText;
        const savedDisabled = buttonElement.dataset.originalDisabled === 'true';

        buttonElement.textContent = originalText || savedText || '操作';
        buttonElement.disabled = savedDisabled;

        // 清理数据属性
        delete buttonElement.dataset.originalText;
        delete buttonElement.dataset.originalDisabled;
    }
}

// 全局设备管理实例
let deviceManager = null;

// 初始化设备管理
function initDeviceManagement() {
    if (!deviceManager) {
        deviceManager = new DeviceManagement();
    }
    return deviceManager;
}
