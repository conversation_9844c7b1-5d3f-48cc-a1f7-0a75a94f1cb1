const validator = require('validator');

/**
 * 输入验证中间件模块
 * 提供统一的数据验证功能，防止SQL注入、XSS攻击等安全威胁
 * 支持多种数据类型验证和自定义验证规则
 */
class ValidationMiddleware {
    constructor() {
        // XSS防护配置
        this.xssConfig = {
            // 危险的HTML标签
            dangerousTags: ['script', 'iframe', 'object', 'embed', 'form', 'input', 'textarea', 'select', 'button'],
            // 危险的属性
            dangerousAttributes: ['onclick', 'onload', 'onerror', 'onmouseover', 'onfocus', 'onblur', 'onchange', 'onsubmit'],
            // 危险的协议
            dangerousProtocols: ['javascript:', 'data:', 'vbscript:']
        };

        // SQL注入防护模式
        this.sqlInjectionPatterns = [
            /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION|SCRIPT)\b)/gi,
            /(--|\/\*|\*\/|;|'|"|`)/g,
            /(\bOR\b|\bAND\b).*?(\b=\b|\bLIKE\b)/gi,
            /(\bunion\b.*?\bselect\b)/gi,
            /(\bdrop\b.*?\btable\b)/gi
        ];
    }

    /**
     * 创建验证中间件
     * @param {Object} schema - 验证规则
     * @returns {Function} Express中间件函数
     */
    validate(schema) {
        return (req, res, next) => {
            try {
                const errors = [];

                // 验证请求体
                if (schema.body) {
                    const bodyErrors = this.validateObject(req.body, schema.body, 'body');
                    errors.push(...bodyErrors);
                }

                // 验证查询参数
                if (schema.query) {
                    const queryErrors = this.validateObject(req.query, schema.query, 'query');
                    errors.push(...queryErrors);
                }

                // 验证路径参数
                if (schema.params) {
                    const paramsErrors = this.validateObject(req.params, schema.params, 'params');
                    errors.push(...paramsErrors);
                }

                // 如果有验证错误，返回错误响应
                if (errors.length > 0) {
                    return res.status(400).json({
                        success: false,
                        message: '输入数据验证失败',
                        errors: errors,
                        timestamp: new Date().toISOString()
                    });
                }

                // 清理和转义输入数据
                this.sanitizeRequest(req);

                next();
            } catch (error) {
                console.error('验证中间件错误:', error);
                res.status(500).json({
                    success: false,
                    message: '服务器内部错误',
                    timestamp: new Date().toISOString()
                });
            }
        };
    }

    /**
     * 验证对象
     * @param {Object} data - 要验证的数据
     * @param {Object} rules - 验证规则
     * @param {string} source - 数据来源（body/query/params）
     * @returns {Array} 错误数组
     */
    validateObject(data, rules, source) {
        const errors = [];

        for (const [field, rule] of Object.entries(rules)) {
            const value = data[field];
            const fieldErrors = this.validateField(value, rule, `${source}.${field}`);
            errors.push(...fieldErrors);
        }

        return errors;
    }

    /**
     * 验证单个字段
     * @param {any} value - 字段值
     * @param {Object} rule - 验证规则
     * @param {string} fieldPath - 字段路径
     * @returns {Array} 错误数组
     */
    validateField(value, rule, fieldPath) {
        const errors = [];

        // 检查必填字段
        if (rule.required && (value === undefined || value === null || value === '')) {
            errors.push({
                field: fieldPath,
                message: '此字段为必填项',
                code: 'REQUIRED'
            });
            return errors; // 如果必填字段为空，不进行其他验证
        }

        // 如果字段为空且非必填，跳过其他验证
        if (value === undefined || value === null || value === '') {
            return errors;
        }

        // 类型验证
        if (rule.type) {
            const typeError = this.validateType(value, rule.type, fieldPath);
            if (typeError) {
                errors.push(typeError);
                return errors; // 类型错误时不进行其他验证
            }
        }

        // 长度验证
        if (rule.minLength !== undefined || rule.maxLength !== undefined) {
            const lengthError = this.validateLength(value, rule, fieldPath);
            if (lengthError) {
                errors.push(lengthError);
            }
        }

        // 数值范围验证
        if (rule.min !== undefined || rule.max !== undefined) {
            const rangeError = this.validateRange(value, rule, fieldPath);
            if (rangeError) {
                errors.push(rangeError);
            }
        }

        // 正则表达式验证
        if (rule.pattern) {
            const patternError = this.validatePattern(value, rule.pattern, fieldPath);
            if (patternError) {
                errors.push(patternError);
            }
        }

        // 邮箱验证
        if (rule.email) {
            const emailError = this.validateEmail(value, fieldPath);
            if (emailError) {
                errors.push(emailError);
            }
        }

        // URL验证
        if (rule.url) {
            const urlError = this.validateUrl(value, fieldPath);
            if (urlError) {
                errors.push(urlError);
            }
        }

        // 自定义验证函数
        if (rule.custom && typeof rule.custom === 'function') {
            const customError = rule.custom(value, fieldPath);
            if (customError) {
                errors.push(customError);
            }
        }

        // 安全验证（XSS和SQL注入）
        if (typeof value === 'string') {
            const securityErrors = this.validateSecurity(value, fieldPath);
            errors.push(...securityErrors);
        }

        return errors;
    }

    /**
     * 类型验证
     */
    validateType(value, expectedType, fieldPath) {
        const actualType = typeof value;
        
        if (expectedType === 'array' && !Array.isArray(value)) {
            return {
                field: fieldPath,
                message: '字段类型必须为数组',
                code: 'INVALID_TYPE',
                expected: 'array',
                actual: actualType
            };
        }

        if (expectedType !== 'array' && actualType !== expectedType) {
            return {
                field: fieldPath,
                message: `字段类型必须为${expectedType}`,
                code: 'INVALID_TYPE',
                expected: expectedType,
                actual: actualType
            };
        }

        return null;
    }

    /**
     * 长度验证
     */
    validateLength(value, rule, fieldPath) {
        const length = typeof value === 'string' ? value.length : 
                      Array.isArray(value) ? value.length : 
                      String(value).length;

        if (rule.minLength !== undefined && length < rule.minLength) {
            return {
                field: fieldPath,
                message: `长度不能少于${rule.minLength}个字符`,
                code: 'MIN_LENGTH',
                minLength: rule.minLength,
                actualLength: length
            };
        }

        if (rule.maxLength !== undefined && length > rule.maxLength) {
            return {
                field: fieldPath,
                message: `长度不能超过${rule.maxLength}个字符`,
                code: 'MAX_LENGTH',
                maxLength: rule.maxLength,
                actualLength: length
            };
        }

        return null;
    }

    /**
     * 数值范围验证
     */
    validateRange(value, rule, fieldPath) {
        const numValue = Number(value);
        
        if (isNaN(numValue)) {
            return {
                field: fieldPath,
                message: '字段必须为有效数字',
                code: 'INVALID_NUMBER'
            };
        }

        if (rule.min !== undefined && numValue < rule.min) {
            return {
                field: fieldPath,
                message: `数值不能小于${rule.min}`,
                code: 'MIN_VALUE',
                min: rule.min,
                actual: numValue
            };
        }

        if (rule.max !== undefined && numValue > rule.max) {
            return {
                field: fieldPath,
                message: `数值不能大于${rule.max}`,
                code: 'MAX_VALUE',
                max: rule.max,
                actual: numValue
            };
        }

        return null;
    }

    /**
     * 正则表达式验证
     */
    validatePattern(value, pattern, fieldPath) {
        const regex = new RegExp(pattern);
        if (!regex.test(String(value))) {
            return {
                field: fieldPath,
                message: '字段格式不正确',
                code: 'INVALID_PATTERN',
                pattern: pattern
            };
        }
        return null;
    }

    /**
     * 邮箱验证
     */
    validateEmail(value, fieldPath) {
        if (!validator.isEmail(String(value))) {
            return {
                field: fieldPath,
                message: '邮箱格式不正确',
                code: 'INVALID_EMAIL'
            };
        }
        return null;
    }

    /**
     * URL验证
     */
    validateUrl(value, fieldPath) {
        if (!validator.isURL(String(value))) {
            return {
                field: fieldPath,
                message: 'URL格式不正确',
                code: 'INVALID_URL'
            };
        }
        return null;
    }

    /**
     * 安全验证（XSS和SQL注入）
     */
    validateSecurity(value, fieldPath) {
        const errors = [];

        // XSS检测
        if (this.containsXSS(value)) {
            errors.push({
                field: fieldPath,
                message: '输入包含潜在的XSS攻击代码',
                code: 'XSS_DETECTED'
            });
        }

        // SQL注入检测
        if (this.containsSQLInjection(value)) {
            errors.push({
                field: fieldPath,
                message: '输入包含潜在的SQL注入代码',
                code: 'SQL_INJECTION_DETECTED'
            });
        }

        return errors;
    }

    /**
     * 检测XSS攻击
     */
    containsXSS(value) {
        const lowerValue = value.toLowerCase();

        // 检查危险标签
        for (const tag of this.xssConfig.dangerousTags) {
            if (lowerValue.includes(`<${tag}`) || lowerValue.includes(`</${tag}`)) {
                return true;
            }
        }

        // 检查危险属性
        for (const attr of this.xssConfig.dangerousAttributes) {
            if (lowerValue.includes(attr)) {
                return true;
            }
        }

        // 检查危险协议
        for (const protocol of this.xssConfig.dangerousProtocols) {
            if (lowerValue.includes(protocol)) {
                return true;
            }
        }

        return false;
    }

    /**
     * 检测SQL注入攻击
     */
    containsSQLInjection(value) {
        for (const pattern of this.sqlInjectionPatterns) {
            if (pattern.test(value)) {
                return true;
            }
        }
        return false;
    }

    /**
     * 清理和转义请求数据
     */
    sanitizeRequest(req) {
        if (req.body) {
            req.body = this.sanitizeObject(req.body);
        }
        if (req.query) {
            req.query = this.sanitizeObject(req.query);
        }
        if (req.params) {
            req.params = this.sanitizeObject(req.params);
        }
    }

    /**
     * 清理对象中的字符串
     */
    sanitizeObject(obj) {
        if (typeof obj === 'string') {
            return this.sanitizeString(obj);
        }
        
        if (Array.isArray(obj)) {
            return obj.map(item => this.sanitizeObject(item));
        }
        
        if (obj && typeof obj === 'object') {
            const sanitized = {};
            for (const [key, value] of Object.entries(obj)) {
                sanitized[key] = this.sanitizeObject(value);
            }
            return sanitized;
        }
        
        return obj;
    }

    /**
     * 清理字符串
     */
    sanitizeString(str) {
        if (typeof str !== 'string') {
            return str;
        }

        // 转义HTML特殊字符
        return str
            .replace(/&/g, '&amp;')
            .replace(/</g, '&lt;')
            .replace(/>/g, '&gt;')
            .replace(/"/g, '&quot;')
            .replace(/'/g, '&#x27;')
            .replace(/\//g, '&#x2F;');
    }
}

module.exports = ValidationMiddleware;
